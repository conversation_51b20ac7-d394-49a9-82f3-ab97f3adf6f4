package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyInquirySymptom;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 职业问诊现有症状
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
public interface IZyInquirySymptomService extends IService<ZyInquirySymptom> {

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId
   * @return List<ZyInquirySymptom>
   */
	public List<ZyInquirySymptom> selectByMainId(String mainId);

    void fillSymptomName(ZyInquirySymptom symptom);
}
