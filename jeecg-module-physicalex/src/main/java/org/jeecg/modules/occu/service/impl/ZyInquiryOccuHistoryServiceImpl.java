package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.occu.entity.ZyInquiryOccuHistory;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.mapper.ZyInquiryOccuHistoryMapper;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.occu.mapper.ZyWorktypeMapper;
import org.jeecg.modules.occu.service.IZyInquiryOccuHistoryService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 职业史
 * @Author: jeecg-boot
 * @Date: 2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquiryOccuHistoryServiceImpl extends ServiceImpl<ZyInquiryOccuHistoryMapper, ZyInquiryOccuHistory> implements IZyInquiryOccuHistoryService {

    @Autowired
    private ZyInquiryOccuHistoryMapper zyInquiryOccuHistoryMapper;

    @Autowired
    private ZyRiskFactorMapper riskFactorMapper;
    @Autowired
    private ZyWorktypeMapper workTypeMapper;

    @Override
    public List<ZyInquiryOccuHistory> selectByMainId(String mainId) {
        List<ZyInquiryOccuHistory> list = zyInquiryOccuHistoryMapper.selectByMainId(mainId);
        for (ZyInquiryOccuHistory zyInquiryOccuHistory : list) {
            fillName(zyInquiryOccuHistory);
        }
        return list;
    }

    @Override
    public void fillName(ZyInquiryOccuHistory inquiryOccuHistory) {
        if (inquiryOccuHistory == null) {
            return;
        }

        String workType = inquiryOccuHistory.getWorkName();
        String riskFactor = inquiryOccuHistory.getRiskName();
        if (StringUtils.isNotBlank(workType)) {
            LambdaQueryWrapper<ZyWorktype> workTypeQurey = new LambdaQueryWrapper<>();
            workTypeQurey.eq(ZyWorktype::getCode, workType).last("limit 1");
            ZyWorktype zyWorktypeDict = workTypeMapper.selectOne(workTypeQurey);
            if (zyWorktypeDict != null) {
                inquiryOccuHistory.setWorkNameDictText(zyWorktypeDict.getName());
            }
        }

        if (StringUtils.isNotBlank(riskFactor)) {
            String[] riskFactors = StringUtils.split(riskFactor, ",");
            LambdaQueryWrapper<ZyRiskFactor> riskFactorLambdaQueryWrapper = new LambdaQueryWrapper<>();
            riskFactorLambdaQueryWrapper.in(ZyRiskFactor::getCode, riskFactors);
            List<ZyRiskFactor> riskFactorDictList = riskFactorMapper.selectList(riskFactorLambdaQueryWrapper);
            if (riskFactorDictList != null && riskFactorDictList.size() > 0) {
                String riskFactorNames = riskFactorDictList.stream().map(ZyRiskFactor::getName).collect(Collectors.joining(","));
                inquiryOccuHistory.setRiskNameDictText(riskFactorNames);
            }
        }

    }
}
