package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyInquiry;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerReg;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description: 职业病问诊
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
public interface IZyInquiryService extends IService<ZyInquiry> {

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	ZyInquiry getByRegId(String customerRegId);

	ZyInquiry getOrGenerateInquiry4Reg(String regId) throws Exception;

	ZyInquiry getInquiryByRegId(String  regId) throws Exception;

	/**
	 * 根据身份证号获取历史问卷记录列表
	 * @param idCard 身份证号
	 * @return 历史问卷记录列表
	 */
	List<ZyInquiry> getHistoryByIdCard(String idCard);

	/**
	 * 根据身份证号获取完整的历史问卷记录（包含子问卷数据）
	 * @param idCard 身份证号
	 * @return 完整的历史问卷记录列表
	 */
	List<Map<String, Object>> getCompleteHistoryByIdCard(String idCard);

	/**
	 * 根据问卷ID获取完整的问卷数据（包含所有子问卷）
	 * @param inquiryId 问卷ID
	 * @return 完整的问卷数据
	 */
	Map<String, Object> getCompleteDataById(String inquiryId);

	/**
	 * 根据身份证号获取智能填写推荐数据
	 * @param idCard 身份证号
	 * @return 智能推荐数据列表
	 */
	List<Map<String, Object>> getSmartFillRecommendations(String idCard);

	/**
	 * 应用智能推荐数据到当前问卷
	 * @param targetRegId 目标客户登记ID
	 * @param sourceInquiryId 源问卷ID
	 * @param applySubQuestionnaires 是否应用子问卷数据
	 * @return 操作结果
	 */
	Map<String, Object> applySmartFillData(String targetRegId, String sourceInquiryId, boolean applySubQuestionnaires);

	/**
	 * 从历史问卷拷贝数据到当前问卷
	 * @param targetRegId 目标客户登记ID
	 * @param sourceInquiryId 源问卷ID
	 * @return 操作结果
	 */
	Map<String, Object> copyFromHistoryInquiry(String targetRegId, String sourceInquiryId);

}
