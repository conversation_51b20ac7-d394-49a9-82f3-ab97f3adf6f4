package org.jeecg.modules.occu.service.impl;

import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import org.jeecg.modules.occu.mapper.ZyRiskFactorItemgroupMapper;
import org.jeecg.modules.occu.service.IZyRiskFactorItemgroupService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 危害因素必检项目
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
@Service
public class ZyRiskFactorItemgroupServiceImpl extends ServiceImpl<ZyRiskFactorItemgroupMapper, ZyRiskFactorItemgroup> implements IZyRiskFactorItemgroupService {
	
	@Autowired
	private ZyRiskFactorItemgroupMapper zyRiskFactorItemgroupMapper;
	
	@Override
	public List<ZyRiskFactorItemgroup> selectByMainId(String mainId) {
		return zyRiskFactorItemgroupMapper.selectByMainId(mainId);
	}

	@Override
	public List<ItemGroup> listByRiskFactors(String jobStatusCode, List<String> factorCodeList) {
		// 直接通过联合查询根据危害因素名称和岗位名称查询项目组合
		return zyRiskFactorItemgroupMapper.listItemGroupsByRiskFactorCodes(factorCodeList, jobStatusCode);
	}


}
