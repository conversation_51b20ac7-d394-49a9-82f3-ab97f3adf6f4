package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.occu.entity.ZySymptomDict;
import org.jeecg.modules.occu.mapper.ZySymptomDictMapper;
import org.jeecg.modules.occu.service.IZySymptomDictService;
import org.jeecg.modules.occu.vo.ZySymptomSystemVO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 职业禁忌症字典
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Service
public class ZySymptomDictServiceImpl extends ServiceImpl<ZySymptomDictMapper, ZySymptomDict> implements IZySymptomDictService {

    /**
     * 获取按系统分组的症状字典列表（带缓存）
     * @return 系统分组列表，按排序号排序
     */
    @Override
    @Cacheable(value = "symptomSystemCache", key = "'all_systems'")
    public List<ZySymptomSystemVO> getSymptomsBySystem() {
        // 查询所有症状字典数据
        QueryWrapper<ZySymptomDict> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("seq"); // 按seq排序
        List<ZySymptomDict> allSymptoms = this.list(queryWrapper);

        // 按系统分组
        Map<String, List<ZySymptomDict>> systemGroups = allSymptoms.stream()
                .filter(symptom -> StringUtils.hasText(symptom.getSystem())) // 过滤掉system为空的数据
                .collect(Collectors.groupingBy(ZySymptomDict::getSystem));

        // 转换为VO并计算排序值
        List<ZySymptomSystemVO> result = new ArrayList<>();
        for (Map.Entry<String, List<ZySymptomDict>> entry : systemGroups.entrySet()) {
            String systemName = entry.getKey();
            List<ZySymptomDict> symptoms = entry.getValue();

            // 计算排序值（该系统下所有症状的seq之和）
            int sortOrder = symptoms.stream()
                    .filter(s -> s.getSeq() != null)
                    .mapToInt(ZySymptomDict::getSeq)
                    .sum();

            // 判断是否为默认系统（如果系统中有任何一个症状是默认的，则该系统为默认）
            String defaultFlag = symptoms.stream()
                    .anyMatch(s -> "1".equals(s.getDefaultFlag()) || "Y".equals(s.getDefaultFlag())) ? "1" : "0";

            ZySymptomSystemVO systemVO = new ZySymptomSystemVO()
                    .setSystemName(systemName)
                    .setDefaultFlag(defaultFlag)
                    .setSortOrder(sortOrder)
                    .setSymptomList(symptoms);

            result.add(systemVO);
        }

        // 按排序值排序
        result.sort(Comparator.comparing(ZySymptomSystemVO::getSortOrder));

        return result;
    }

    /**
     * 清除系统分组缓存
     */
    @Override
    @CacheEvict(value = "symptomSystemCache", allEntries = true)
    public void clearSystemCache() {
        // 缓存清除方法，通过注解实现
    }
}
