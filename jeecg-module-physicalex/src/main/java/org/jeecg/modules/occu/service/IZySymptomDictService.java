package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZySymptomDict;
import org.jeecg.modules.occu.vo.ZySymptomSystemVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 职业禁忌症字典
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
public interface IZySymptomDictService extends IService<ZySymptomDict> {

    /**
     * 获取按系统分组的症状字典列表（带缓存）
     * @return 系统分组列表，按排序号排序
     */
    List<ZySymptomSystemVO> getSymptomsBySystem();

    /**
     * 清除系统分组缓存
     */
    void clearSystemCache();
}
