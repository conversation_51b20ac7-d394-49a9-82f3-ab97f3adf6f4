package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyInquiryOccuHistory;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 职业史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
public interface IZyInquiryOccuHistoryService extends IService<ZyInquiryOccuHistory> {

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId
   * @return List<ZyInquiryOccuHistory>
   */
	public List<ZyInquiryOccuHistory> selectByMainId(String mainId);

    void fillName(ZyInquiryOccuHistory inquiryOccuHistory);
}
