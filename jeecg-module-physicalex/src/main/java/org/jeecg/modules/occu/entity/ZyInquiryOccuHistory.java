package org.jeecg.modules.occu.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.aspect.annotation.Dict;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 职业史
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Data
@TableName("zy_inquiry_occu_history")
@ApiModel(value="zy_inquiry_occu_history对象", description="职业史")
public class ZyInquiryOccuHistory implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**问诊ID*/
    @ApiModelProperty(value = "问诊ID")
    private java.lang.String inquiryId;
	/**开始日期*/
	@Excel(name = "开始日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "开始日期")
    private java.util.Date startDate;
	/**结束日期*/
	@Excel(name = "结束日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "结束日期")
    private java.util.Date endDate;
	/**就职单位*/
	@Excel(name = "就职单位", width = 15)
    @ApiModelProperty(value = "就职单位")
    private java.lang.String company;
	/**总工龄年*/
	@Excel(name = "总工龄年", width = 15)
    @ApiModelProperty(value = "总工龄年")
    private java.lang.Integer workYears;
	/**总工龄月*/
	@Excel(name = "总工龄月", width = 15)
    @ApiModelProperty(value = "总工龄月")
    private java.lang.Integer workMonths;
	/**接害工龄年*/
	@Excel(name = "接害工龄年", width = 15)
    @ApiModelProperty(value = "接害工龄年")
    private java.lang.Integer riskYears;
	/**接害工龄月*/
	@Excel(name = "接害工龄月", width = 15)
    @ApiModelProperty(value = "接害工龄月")
    private java.lang.Integer riskMonths;
	/**车间*/
	@Excel(name = "车间", width = 15)
    @ApiModelProperty(value = "车间")
    private java.lang.String workshop;
	/**工种*/
	@Excel(name = "工种", width = 15)
    @ApiModelProperty(value = "工种")
    private java.lang.String workName;
	/**危害因素*/
	@Excel(name = "危害因素", width = 15)
    @ApiModelProperty(value = "危害因素")
    @Dict(dictTable = "zy_risk_factor", dicText = "name", dicCode = "code")
    private java.lang.String riskName;
	/**是否防护*/
	@Excel(name = "是否防护", width = 15)
    @ApiModelProperty(value = "是否防护")
    private java.lang.String protectFlag;
	/**防护措施*/
	@Excel(name = "防护措施", width = 15)
    @ApiModelProperty(value = "防护措施")
    private java.lang.String protectMeasures;
    /**检查医生*/
    @Excel(name = "检查医生", width = 15)
    @ApiModelProperty(value = "检查医生")
    private String checkDoctor;

    @TableField(exist = false)
    private String riskNameDictText;
    @TableField(exist = false)
    private String protectMeasuresDictText;
    @TableField(exist = false)
    private String workNameDictText;
}
