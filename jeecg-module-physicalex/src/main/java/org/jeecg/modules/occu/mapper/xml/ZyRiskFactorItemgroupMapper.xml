<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.occu.mapper.ZyRiskFactorItemgroupMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  zy_risk_factor_itemgroup 
		WHERE
			 factor_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup">
		SELECT * 
		FROM  zy_risk_factor_itemgroup
		WHERE
			 factor_id = #{mainId} 	</select>
    <select id="getItemGroupByRiskFactors" resultType="java.lang.String">
		select ig.name from item_group ig join zy_risk_factor_itemgroup zrfi on ig.id=zrfi.itemgroup_id
		join zy_risk_factor zrf on zrf.id=zrfi.factor_id where
		zrf.name in
		<foreach collection="riskFactors" item="riskFactor" open="(" separator="," close=")">
			#{riskFactor}
		</foreach>
	</select>

	<select id="listItemGroupsByRiskFactorCodes" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup">
		SELECT DISTINCT ig.*,zrfi.check_part_id,zrfi.check_part_code
		FROM item_group ig
		JOIN zy_risk_factor_itemgroup zrfi ON ig.id = zrfi.itemgroup_id
		WHERE ig.del_flag = 0
		  AND ig.enable_flag = 1
		  AND zrfi.post = #{jobStatusCode}
		  AND zrfi.factor_code IN
		<foreach collection="factorCodes" item="factorCode" open="(" separator="," close=")">
			#{factorCode}
		</foreach>
		ORDER BY ig.sort
	</select>
</mapper>
