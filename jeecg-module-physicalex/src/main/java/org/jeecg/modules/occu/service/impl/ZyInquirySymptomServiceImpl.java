package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.occu.entity.ZyInquirySymptom;
import org.jeecg.modules.occu.entity.ZySymptomDict;
import org.jeecg.modules.occu.mapper.ZyInquirySymptomMapper;
import org.jeecg.modules.occu.mapper.ZySymptomDictMapper;
import org.jeecg.modules.occu.service.IZyInquirySymptomService;
import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 职业问诊现有症状
 * @Author: jeecg-boot
 * @Date: 2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquirySymptomServiceImpl extends ServiceImpl<ZyInquirySymptomMapper, ZyInquirySymptom> implements IZyInquirySymptomService {

    @Autowired
    private ZyInquirySymptomMapper zyInquirySymptomMapper;
    @Autowired
    private ZySymptomDictMapper dictMapper;

    @Override
    public List<ZyInquirySymptom> selectByMainId(String mainId) {
        return zyInquirySymptomMapper.selectByMainId(mainId);
    }

    @Override
    public void fillSymptomName(ZyInquirySymptom symptom) {
        if (StringUtils.isBlank(symptom.getSymptomName())) {
            String symptomCode = symptom.getSymptom();
            LambdaQueryWrapper<ZySymptomDict> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ZySymptomDict::getCode, symptomCode).last("limit 1");
            ZySymptomDict dict = dictMapper.selectOne(wrapper);
            if (dict != null) {
                symptom.setSymptomName(dict.getDictText());
            }
        }
    }
}
