package org.jeecg.modules.occu.service;

import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.occu.entity.ZyRiskFactorItemgroup;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 危害因素必检项目
 * @Author: jeecg-boot
 * @Date: 2025-02-18
 * @Version: V1.0
 */
public interface IZyRiskFactorItemgroupService extends IService<ZyRiskFactorItemgroup> {

    /**
     * 通过主表id查询子表数据
     *
     * @param mainId 主表id
     * @return List<ZyRiskFactorItemgroup>
     */
    List<ZyRiskFactorItemgroup> selectByMainId(String mainId);

    List<ItemGroup> listByRiskFactors(String jobStatusCode,List<String> factorCodeList);
}
