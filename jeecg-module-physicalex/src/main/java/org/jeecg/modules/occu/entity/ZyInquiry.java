package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecgframework.poi.excel.annotation.Excel;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 职业病问诊
 * @Author: jeecg-boot
 * @Date:   2024-05-09
 * @Version: V1.0
 */
@Data
@TableName("zy_inquiry")
@ApiModel(value="zy_inquiry对象", description="职业病问诊")
public class ZyInquiry implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**体检登记ID*/
    @Excel(name = "体检登记ID", width = 15)
    @ApiModelProperty(value = "体检登记ID")
    private java.lang.String customerRegId;
	/**姓名*/
    @Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**性别*/
    @Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private java.lang.String gender;
	/**年龄*/
    @Excel(name = "年龄", width = 15)
    @ApiModelProperty(value = "年龄")
    private java.lang.String age;
	/**月经初潮(岁)*/
    @Excel(name = "月经初潮(岁)", width = 15)
    @ApiModelProperty(value = "月经初潮(岁)")
    private java.lang.Integer menarche;
	/**经期(天)*/
    @Excel(name = "经期(天)", width = 15)
    @ApiModelProperty(value = "经期(天)")
    private java.lang.Integer menstruation;
	/**周期(天)*/
    @Excel(name = "周期(天)", width = 15)
    @ApiModelProperty(value = "周期(天)")
    private java.lang.String period;
	/**停经年龄(岁)*/
    @Excel(name = "停经年龄(岁)", width = 15)
    @ApiModelProperty(value = "停经年龄(岁)")
    private java.lang.Integer menopauseAge;
	/**现有子女个数*/
    @Excel(name = "现有子女个数", width = 15)
    @ApiModelProperty(value = "现有子女个数")
    private java.lang.String childCount;
	/**流产*/
    @Excel(name = "流产", width = 15)
    @ApiModelProperty(value = "流产")
    private java.lang.String abortionCount;
	/**早产*/
    @Excel(name = "早产", width = 15)
    @ApiModelProperty(value = "早产")
    private java.lang.String prematureCount;
	/**死胎*/
    @Excel(name = "死胎", width = 15)
    @ApiModelProperty(value = "死胎")
    private java.lang.String stillbirth;
	/**异常胎*/
    @Excel(name = "异常胎", width = 15)
    @ApiModelProperty(value = "异常胎")
    private java.lang.String abnormalfetal;
	/**孕*/
    @Excel(name = "孕", width = 15)
    @ApiModelProperty(value = "孕")
    private java.lang.String pregnancy;
	/**先天畸形*/
    @Excel(name = "先天畸形", width = 15)
    @ApiModelProperty(value = "先天畸形")
    private java.lang.String congenitalMalformations;
	/**吸烟状态*/
    @Excel(name = "吸烟状态", width = 15, dicCode = "smoke_status")
    @Dict(dicCode = "smoke_status")
    @ApiModelProperty(value = "吸烟状态")
    private java.lang.String smokStatus;
	/**一天多少支*/
    @Excel(name = "一天多少支", width = 15)
    @ApiModelProperty(value = "一天多少支")
    private java.lang.String smokAmount;
	/**吸烟多少年*/
    @Excel(name = "吸烟多少年", width = 15)
    @ApiModelProperty(value = "吸烟多少年")
    private java.lang.String smokYears;
    /**吸烟多少月*/
    @Excel(name = "吸烟多少月", width = 15)
    @ApiModelProperty(value = "吸烟多少月")
    private java.lang.String smokMonths;
	/**饮酒状态*/
    @Excel(name = "饮酒状态", width = 15, dicCode = "drink_status")
    @Dict(dicCode = "drink_status")
    @ApiModelProperty(value = "饮酒状态")
    private java.lang.String drinkStatus;
	/**一天饮酒量(ml)*/
    @Excel(name = "一天饮酒量(ml)", width = 15)
    @ApiModelProperty(value = "一天饮酒量(ml)")
    private java.lang.String drinkAmount;
	/**饮酒多少年*/
    @Excel(name = "饮酒多少年", width = 15)
    @ApiModelProperty(value = "饮酒多少年")
    private java.lang.String drinkYears;

    /**状态：暂存，已完成*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String status;
    /**身份证号*/
    @Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    private String idCard;
    /**体检号*/
    @Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "状态")
    private String examNo;
    /**档案ID*/
    @Excel(name = "档案ID", width = 15)
    @ApiModelProperty(value = "档案ID")
    private String customerId;
    /**档案号*/
    @Excel(name = "档案号", width = 15)
    @ApiModelProperty(value = "档案号")
    private String archiveNo;
    /**现场照片*/
    @Excel(name = "现场照片", width = 15)
    @ApiModelProperty(value = "现场照片")
    private String answerPicture;
    /**签字图片url*/
    @Excel(name = "签字图片url", width = 15)
    @ApiModelProperty(value = "签字图片url")
    private String  signPicture;

    @TableField(exist = false)
    private ZyInquiryDiseaseHistory zyInquiryDiseaseHistory;

    @TableField(exist = false)
    private ZyInquiryOccuHistory zyInquiryOccuHistory;

    @TableField(exist = false)
    private ZyInquiryFamilyHistory zyInquiryFamilyHistory;

    @TableField(exist = false)
    private ZyInquiryMaritalStatus zyInquiryMaritalStatus;
    @TableField(exist = false)
    private List<ZyInquirySymptom> zyInquirySymptomList;

}
