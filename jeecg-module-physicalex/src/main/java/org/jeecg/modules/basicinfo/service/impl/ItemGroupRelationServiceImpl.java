package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.entity.*;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.mapper.ItemGroupRelationMapper;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: item_group_relation
 * @Author: jeecg-boot
 * @Date: 2024-12-02
 * @Version: V1.0
 */
@Slf4j
@Service
public class ItemGroupRelationServiceImpl extends ServiceImpl<ItemGroupRelationMapper, ItemGroupRelation> implements IItemGroupRelationService {
    @Autowired
    private ItemGroupRelationMapper itemGroupRelationMapper;
    @Autowired
    private ItemGroupMapper itemGroupMapper;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;

    @Override
    public GroupRelationVO getRelationGroupsByMainId(String mainId) {
        GroupRelationVO groupRelationVO = new GroupRelationVO();
        groupRelationVO.setGroupId(mainId);
        //附属关系
        List<ItemGroupRelation> attachGroups = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, mainId).eq(ItemGroupRelation::getRelation, "附属"));
        groupRelationVO.setAttachGroups(attachGroups);
        //赠送关系
        List<ItemGroupRelation> giftGroups = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, mainId).eq(ItemGroupRelation::getRelation, "赠送"));
        groupRelationVO.setGiftGroups(giftGroups);
        //互斥关系
        List<ItemGroupRelation> exclusiveGroups = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, mainId).eq(ItemGroupRelation::getRelation, "互斥"));
        List<ItemGroupRelation> exclusiveGroups2 = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getRelationGroupId, mainId).eq(ItemGroupRelation::getRelation, "互斥"));
        List<String> exclusiveIds = exclusiveGroups.stream().map(ItemGroupRelation::getRelationGroupId).toList();
        List<String> exclusiveId2s = exclusiveGroups2.stream().map(ItemGroupRelation::getGroupId).toList();
        if (CollectionUtils.isNotEmpty(exclusiveId2s)) {
            exclusiveIds.addAll(exclusiveId2s);
        }
        if (CollectionUtils.isNotEmpty(exclusiveIds)) {
            groupRelationVO.setExclusiveGroups(exclusiveIds);
        }
        //依赖关系
        List<ItemGroupRelation> dependentGroups = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, mainId).eq(ItemGroupRelation::getRelation, "依赖"));
        groupRelationVO.setDependentGroups(dependentGroups);
        return groupRelationVO;
    }


    @Override
    public void saveRelationGroupBatch(GroupRelationVO groupRelationVO) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> groupIds = Lists.newArrayList();

        // 收集附属关系的groupId
        List<String> attachGroupIds = groupRelationVO.getAttachGroups().stream().map(ItemGroupRelation::getRelationGroupId).toList();
        if (CollectionUtils.isNotEmpty(attachGroupIds)) {
            groupIds.addAll(attachGroupIds);
        }

        // 收集赠送关系的groupId
        if (CollectionUtils.isNotEmpty(groupRelationVO.getGiftGroups())) {
            List<String> giftGroupIds = groupRelationVO.getGiftGroups().stream().map(ItemGroupRelation::getRelationGroupId).filter(StringUtils::isNotBlank).toList();
            groupIds.addAll(giftGroupIds);
        }

        // 收集互斥关系的groupId
        if (CollectionUtils.isNotEmpty(groupRelationVO.getExclusiveGroups())) {
            groupIds.addAll(groupRelationVO.getExclusiveGroups());
        }

        // 收集依赖关系的groupId
        if (CollectionUtils.isNotEmpty(groupRelationVO.getDependentGroups())) {
            List<String> dependentGroupIds = groupRelationVO.getDependentGroups().stream().map(ItemGroupRelation::getRelationGroupId).filter(StringUtils::isNotBlank).toList();
            groupIds.addAll(dependentGroupIds);
        }

        // 只有当groupIds不为空时才查询，避免空IN子句错误
        log.info("收集到的所有关联项目ID: {}", groupIds);
        final Map<String, List<ItemGroup>> map;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            List<ItemGroup> itemGroups = itemGroupMapper.selectBatchIds(groupIds);
            map = itemGroups.stream().collect(Collectors.groupingBy(ItemGroup::getId));
            log.info("查询到的项目信息: {}", itemGroups.stream().map(g -> g.getId() + ":" + g.getName()).toList());
        } else {
            map = new HashMap<>();
            log.info("没有需要查询的关联项目ID");
        }
        // 处理附属关系 - 先删除旧数据，再保存新数据
        int deletedAttachCount = remove(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupRelationVO.getGroupId()).eq(ItemGroupRelation::getRelation, "附属")) ? 1 : 0;
        log.info("删除项目{}的附属关系数据，删除成功: {}", groupRelationVO.getGroupId(), deletedAttachCount > 0);

        if (CollectionUtils.isNotEmpty(groupRelationVO.getAttachGroups())) {
            groupRelationVO.getAttachGroups().forEach(item -> {
                item.setGroupId(groupRelationVO.getGroupId());
                item.setGroupName(groupRelationVO.getGroupName());

                // 安全获取关联项目名称
                List<ItemGroup> relatedGroups = map.get(item.getRelationGroupId());
                if (relatedGroups != null && !relatedGroups.isEmpty()) {
                    item.setRelationGroupName(relatedGroups.get(0).getName());
                } else {
                    item.setRelationGroupName("未知项目");
                    log.warn("找不到ID为{}的项目信息", item.getRelationGroupId());
                }

                item.setRelation("附属");
                item.setCreateBy(loginUser.getUsername());
                item.setCreateTime(new java.util.Date());
            });
            saveBatch(groupRelationVO.getAttachGroups());
            log.info("保存项目{}的附属关系数据，数量: {}", groupRelationVO.getGroupId(), groupRelationVO.getAttachGroups().size());
        } else {
            log.info("项目{}没有附属关系数据需要保存", groupRelationVO.getGroupId());
        }

        // 处理赠送关系 - 先删除旧数据，再保存新数据
        int deletedGiftCount = remove(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupRelationVO.getGroupId()).eq(ItemGroupRelation::getRelation, "赠送")) ? 1 : 0;
        log.info("删除项目{}的赠送关系数据，删除成功: {}", groupRelationVO.getGroupId(), deletedGiftCount > 0);

        if (CollectionUtils.isNotEmpty(groupRelationVO.getGiftGroups())) {
            groupRelationVO.getGiftGroups().forEach(item -> {
                item.setGroupId(groupRelationVO.getGroupId());
                item.setGroupName(groupRelationVO.getGroupName());

                // 安全获取关联项目名称
                List<ItemGroup> relatedGroups = map.get(item.getRelationGroupId());
                if (relatedGroups != null && !relatedGroups.isEmpty()) {
                    item.setRelationGroupName(relatedGroups.get(0).getName());
                } else {
                    item.setRelationGroupName("未知项目");
                    log.warn("找不到ID为{}的项目信息", item.getRelationGroupId());
                }

                item.setRelation("赠送");
                item.setCreateBy(loginUser.getUsername());
                item.setCreateTime(new java.util.Date());
            });
            saveBatch(groupRelationVO.getGiftGroups());
            log.info("保存项目{}的赠送关系数据，数量: {}", groupRelationVO.getGroupId(), groupRelationVO.getGiftGroups().size());
        } else {
            log.info("项目{}没有赠送关系数据需要保存", groupRelationVO.getGroupId());
        }

        // 处理互斥关系 - 先删除旧数据，再保存新数据
        int deletedExclusiveCount = remove(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupRelationVO.getGroupId()).eq(ItemGroupRelation::getRelation, "互斥")) ? 1 : 0;
        log.info("删除项目{}的互斥关系数据，删除成功: {}", groupRelationVO.getGroupId(), deletedExclusiveCount > 0);

        if (CollectionUtils.isNotEmpty(groupRelationVO.getExclusiveGroups())) {
            List<ItemGroupRelation> itemGroupRelations = Lists.newArrayList();
            groupRelationVO.getExclusiveGroups().forEach(item -> {
                ItemGroupRelation itemGroupRelation = new ItemGroupRelation();
                itemGroupRelation.setGroupId(groupRelationVO.getGroupId());
                itemGroupRelation.setGroupName(groupRelationVO.getGroupName());
                itemGroupRelation.setRelationGroupId(item);

                // 安全获取关联项目名称
                List<ItemGroup> relatedGroups = map.get(item);
                if (relatedGroups != null && !relatedGroups.isEmpty()) {
                    itemGroupRelation.setRelationGroupName(relatedGroups.get(0).getName());
                } else {
                    itemGroupRelation.setRelationGroupName("未知项目");
                    log.warn("找不到ID为{}的项目信息", item);
                }

                itemGroupRelation.setRelation("互斥");
                itemGroupRelation.setCreateBy(loginUser.getUsername());
                itemGroupRelation.setCreateTime(new java.util.Date());
                itemGroupRelations.add(itemGroupRelation);
            });
            saveBatch(itemGroupRelations);
            log.info("保存项目{}的互斥关系数据，数量: {}", groupRelationVO.getGroupId(), itemGroupRelations.size());
        } else {
            log.info("项目{}没有互斥关系数据需要保存", groupRelationVO.getGroupId());
        }

        // 处理依赖关系 - 先删除旧数据，再保存新数据
        int deletedDependentCount = remove(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupRelationVO.getGroupId()).eq(ItemGroupRelation::getRelation, "依赖")) ? 1 : 0;
        log.info("删除项目{}的依赖关系数据，删除成功: {}", groupRelationVO.getGroupId(), deletedDependentCount > 0);

        if (CollectionUtils.isNotEmpty(groupRelationVO.getDependentGroups())) {
            groupRelationVO.getDependentGroups().forEach(item -> {
                item.setGroupId(groupRelationVO.getGroupId());
                item.setGroupName(groupRelationVO.getGroupName());

                // 设置关联大项名称
                if (StringUtils.isNotBlank(item.getRelationGroupId())) {
                    List<ItemGroup> relatedGroups = map.get(item.getRelationGroupId());
                    if (relatedGroups != null && !relatedGroups.isEmpty()) {
                        item.setRelationGroupName(relatedGroups.get(0).getName());
                    } else {
                        item.setRelationGroupName("未知项目");
                        log.warn("找不到ID为{}的项目信息", item.getRelationGroupId());
                    }
                }

                // 如果是小项依赖，设置小项名称
                if ("ITEM".equals(item.getRelationItemType()) && StringUtils.isNotBlank(item.getRelationItemId())) {
                    // 从前端传来的名称直接使用，或者查询数据库获取
                    if (StringUtils.isBlank(item.getRelationItemName())) {
                        // 这里可以添加查询ItemInfo的逻辑
                        item.setRelationItemName("未知小项");
                    }
                }

                item.setRelation("依赖");
                item.setCreateBy(loginUser.getUsername());
                item.setCreateTime(new java.util.Date());
            });
            saveBatch(groupRelationVO.getDependentGroups());
            log.info("保存项目{}的依赖关系数据，数量: {}", groupRelationVO.getGroupId(), groupRelationVO.getDependentGroups().size());
        } else {
            log.info("项目{}没有依赖关系数据需要保存", groupRelationVO.getGroupId());
        }
    }

    @Override
    public List<CustomerRegItemGroup> getAttachGroups(List<CustomerRegItemGroup> addingItemGroups) {
        Set<String> groupIds = addingItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Map<String, List<ItemGroupRelation>> relationGroupMap = list(new LambdaQueryWrapper<ItemGroupRelation>().in(ItemGroupRelation::getGroupId, groupIds).eq(ItemGroupRelation::getRelation, "附属")).stream().collect(Collectors.groupingBy(ItemGroupRelation::getGroupId));
        List<CustomerRegItemGroup> relationGroupList = Lists.newArrayList();
        addingItemGroups.forEach(group -> {
            List<ItemGroupRelation> itemGroupRelations = relationGroupMap.get(group.getItemGroupId());
            if (CollectionUtils.isNotEmpty(itemGroupRelations)) {
                // 根据部位逻辑筛选匹配的附属项目
                List<ItemGroupRelation> matchedRelations = filterAttachGroupsByPart(itemGroupRelations, group);
                itemGroupRelations = matchedRelations;

                if (CollectionUtils.isNotEmpty(itemGroupRelations)) {
                    itemGroupRelations.forEach(relation -> {
                        if (StringUtils.isNotBlank(relation.getRelationGroupId())) {
                            ItemGroup itemGroup = itemGroupMapper.selectById(relation.getRelationGroupId());
                            if (Objects.nonNull(itemGroup)) {
                                Integer quantity = Objects.nonNull(relation.getQuantity()) ? relation.getQuantity() : 1;
                                for (int i = 0; i < quantity; i++) {
                                    CustomerRegItemGroup customerRegItemGroup = new CustomerRegItemGroup();
                                    BeanUtils.copyProperties(group, customerRegItemGroup);
                                    customerRegItemGroup.setId(null);
                                    customerRegItemGroup.setItemGroupId(itemGroup.getId());
                                    customerRegItemGroup.setItemGroupName(itemGroup.getName());
                                    customerRegItemGroup.setHisCode(itemGroup.getHisCode());
                                    customerRegItemGroup.setHisName(itemGroup.getHisName());
                                    customerRegItemGroup.setPlatCode(itemGroup.getPlatCode());
                                    customerRegItemGroup.setPlatName(itemGroup.getPlatName());
                                    customerRegItemGroup.setDepartmentId(itemGroup.getDepartmentId());
                                    customerRegItemGroup.setDepartmentCode(itemGroup.getDepartmentCode());
                                    customerRegItemGroup.setDepartmentName(itemGroup.getDepartmentName());
                                    customerRegItemGroup.setClassCode(itemGroup.getClassCode());
                                    customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                                    customerRegItemGroup.setPrice(itemGroup.getPrice());
                                    customerRegItemGroup.setPriceAfterDis(itemGroup.getPrice());
                                    customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                                    customerRegItemGroup.setAttachBaseId(group.getId());

                                    // 设置附属项目的部位信息
                                    setAttachGroupPartInfo(customerRegItemGroup, relation, group);

                                    relationGroupList.add(customerRegItemGroup);
                                }
                            }
                        }

                    });
                }
            }
        });
        return relationGroupList;
    }

    @Override
    public List<CustomerRegItemGroup> getGiftGroups(List<CustomerRegItemGroup> addingItemGroups) {
        Set<String> groupIds = addingItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Map<String, List<ItemGroupRelation>> relationGroupMap = list(new LambdaQueryWrapper<ItemGroupRelation>().in(ItemGroupRelation::getGroupId, groupIds).eq(ItemGroupRelation::getRelation, "赠送")).stream().collect(Collectors.groupingBy(ItemGroupRelation::getGroupId));
        List<CustomerRegItemGroup> relationGroupList = Lists.newArrayList();
        addingItemGroups.forEach(group -> {
            List<ItemGroupRelation> itemGroupRelations = relationGroupMap.get(group.getItemGroupId());
            if (CollectionUtils.isNotEmpty(itemGroupRelations)) {
                // 根据部位逻辑筛选匹配的赠送项目
                List<ItemGroupRelation> matchedRelations = filterGiftGroupsByPart(itemGroupRelations, group);
                itemGroupRelations = matchedRelations;

                if (CollectionUtils.isNotEmpty(itemGroupRelations)) {
                    itemGroupRelations.forEach(relation -> {
                        if (StringUtils.isNotBlank(relation.getRelationGroupId())) {
                            ItemGroup itemGroup = itemGroupMapper.selectById(relation.getRelationGroupId());
                            if (Objects.nonNull(itemGroup)) {
                                Integer quantity = Objects.nonNull(relation.getQuantity()) ? relation.getQuantity() : 1;
                                for (int i = 0; i < quantity; i++) {
                                    CustomerRegItemGroup customerRegItemGroup = new CustomerRegItemGroup();
                                    BeanUtils.copyProperties(group, customerRegItemGroup);
                                    customerRegItemGroup.setId(null);
                                    customerRegItemGroup.setItemGroupId(itemGroup.getId());
                                    customerRegItemGroup.setItemGroupName(itemGroup.getName());
                                    customerRegItemGroup.setHisCode(itemGroup.getHisCode());
                                    customerRegItemGroup.setHisName(itemGroup.getHisName());
                                    customerRegItemGroup.setPlatCode(itemGroup.getPlatCode());
                                    customerRegItemGroup.setPlatName(itemGroup.getPlatName());
                                    customerRegItemGroup.setDepartmentId(itemGroup.getDepartmentId());
                                    customerRegItemGroup.setDepartmentCode(itemGroup.getDepartmentCode());
                                    customerRegItemGroup.setDepartmentName(itemGroup.getDepartmentName());
                                    customerRegItemGroup.setClassCode(itemGroup.getClassCode());
                                    customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                                    customerRegItemGroup.setPrice(itemGroup.getPrice());
                                    customerRegItemGroup.setPriceAfterDis(itemGroup.getPrice());
                                    customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                                    // 赠送项目标记为免费
                                    customerRegItemGroup.setPrice(BigDecimal.ZERO);
                                    customerRegItemGroup.setPriceAfterDis(BigDecimal.ZERO);
                                    customerRegItemGroup.setGiftBaseId(group.getId()); // 设置赠送基础项目ID

                                    // 设置赠送项目的部位信息
                                    setGiftGroupPartInfo(customerRegItemGroup, relation, group);

                                    relationGroupList.add(customerRegItemGroup);
                                }
                            }
                        }

                    });
                }
            }
        });
        return relationGroupList;
    }

    @Override
    public void checkIsHaveMutexes(List<CustomerRegItemGroup> addingItemGroups) {
        Set<String> groupIds = addingItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Set<String> addedGroupIds = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, addingItemGroups.get(0).getCustomerRegId()).ne(CustomerRegItemGroup::getAddMinusFlag, "-1").ne(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功)).stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        //验证是否存在互斥项目
        List<ItemGroupRelation> mutexRelations = list(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getRelation, "互斥"));
        if (CollectionUtils.isEmpty(mutexRelations)) {
            return;
        }

        Set<String> allRelevantGroupIds = new HashSet<>();
        allRelevantGroupIds.addAll(groupIds);
        allRelevantGroupIds.addAll(addedGroupIds);
        List<String> msgs = new ArrayList<>();
        for (ItemGroupRelation relation : mutexRelations) {
            String baseId = relation.getGroupId();
            String relatedId = relation.getRelationGroupId();
            for (String groupId : groupIds) {
                //判断新增项目之间是否存在互斥项目
                if (groupId.equals(baseId)) {
                    //判断新增项目中有无存在relationId
                    if (groupId.contains(relatedId)) {
                        //该组互斥关系存在
                        String detailMsg = "新增项目【" + relation.getGroupName() + "】与【" + relation.getRelationGroupName() + "】冲突";
                        msgs.add(detailMsg);
                    }

                } else if (groupId.equals(relatedId)) {
                    //判断新增项目中有无存在baseId
                    if (groupId.contains(baseId)) {
                        //该组互斥关系存在
                        String detailMsg = "新增项目【" + relation.getRelationGroupName() + "】项目【" + relation.getGroupName() + "】冲突";
                        msgs.add(detailMsg);
                    }
                }
                //判断已添加项目与新增项目之间是否存在互斥项目
                if (groupId.equals(baseId)) {
                    //判断新增项目中有无存在relationId
                    if (addedGroupIds.contains(relatedId)) {
                        //该组互斥关系存在
                        String detailMsg = "新增项目【" + relation.getGroupName() + "】与已有项目【" + relation.getRelationGroupName() + "】冲突";
                        msgs.add(detailMsg);
                    }

                } else if (groupId.equals(relatedId)) {
                    //判断新增项目中有无存在baseId
                    if (addedGroupIds.contains(baseId)) {
                        //该组互斥关系存在
                        String detailMsg = "新增项目【" + relation.getRelationGroupName() + "】与已有项目【" + relation.getGroupName() + "】冲突";
                        msgs.add(detailMsg);
                    }
                }
            }

        }
        if (CollectionUtils.isNotEmpty(msgs)) {
            throw new RuntimeException(StringUtils.join(msgs, ","));
        }

    }

    @Override
    public void checkDependentGroups(List<CustomerRegItemGroup> addingItemGroups) throws Exception {
        Set<String> groupIds = addingItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Set<String> addedGroupIds = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, addingItemGroups.get(0).getCustomerRegId()).ne(CustomerRegItemGroup::getAddMinusFlag, "-1").ne(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功)).stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());

        // 查询依赖关系
        List<ItemGroupRelation> dependentRelations = list(new LambdaQueryWrapper<ItemGroupRelation>().in(ItemGroupRelation::getGroupId, groupIds).eq(ItemGroupRelation::getRelation, "依赖"));

        if (CollectionUtils.isEmpty(dependentRelations)) {
            return;
        }

        List<String> msgs = new ArrayList<>();
        for (ItemGroupRelation relation : dependentRelations) {
            String groupId = relation.getGroupId();
            String relationItemType = relation.getRelationItemType();

            // 检查是否满足依赖条件
            boolean dependencyMet = false;

            if ("GROUP".equals(relationItemType)) {
                // 大项依赖：检查依赖的大项是否已存在
                String dependentGroupId = relation.getRelationGroupId();
                dependencyMet = addedGroupIds.contains(dependentGroupId) || groupIds.contains(dependentGroupId);

                if (!dependencyMet) {
                    String msg = String.format("项目【%s】依赖项目【%s】，请先添加依赖项目", relation.getGroupName(), relation.getRelationGroupName());
                    msgs.add(msg);
                }
            } else if ("ITEM".equals(relationItemType)) {
                // 优化：跳过小项依赖检查，只在项目录入阶段检查大项依赖
                log.info("跳过小项依赖检查: 项目【{}】依赖小项【{}】（属于【{}】）", relation.getGroupName(), relation.getRelationItemName(), relation.getRelationGroupName());
                continue;
            }
        }

        if (CollectionUtils.isNotEmpty(msgs)) {
            throw new RuntimeException(StringUtils.join(msgs, "；"));
        }
    }

    /**
     * 根据部位逻辑筛选匹配的附属项目
     *
     * @param itemGroupRelations 所有附属关系
     * @param mainGroup          主项目
     * @return 匹配的附属关系列表
     */
    private List<ItemGroupRelation> filterAttachGroupsByPart(List<ItemGroupRelation> itemGroupRelations, CustomerRegItemGroup mainGroup) {
        if (CollectionUtils.isEmpty(itemGroupRelations)) {
            return itemGroupRelations;
        }

        String mainCheckPartId = mainGroup.getCheckPartId();

        // 如果主项目没有部位信息，返回所有没有部位限制的附属项目
        if (StringUtils.isBlank(mainCheckPartId)) {
            return itemGroupRelations.stream().filter(relation -> StringUtils.isBlank(relation.getMainCheckPartId())).collect(Collectors.toList());
        }

        // 如果主项目有部位信息，筛选匹配的附属项目
        List<ItemGroupRelation> matchedRelations = itemGroupRelations.stream().filter(relation -> {
            // 如果附属关系没有指定主项目部位，则匹配所有部位
            if (StringUtils.isBlank(relation.getMainCheckPartId())) {
                return true;
            }
            // 如果指定了主项目部位，则必须完全匹配
            return StringUtils.equals(mainCheckPartId, relation.getMainCheckPartId());
        }).collect(Collectors.toList());

        log.info("主项目{}(部位:{})匹配到{}个附属项目", mainGroup.getItemGroupName(), mainGroup.getCheckPartName(), matchedRelations.size());

        return matchedRelations;
    }

    /**
     * 为附属项目设置部位信息
     *
     * @param attachGroup 附属项目
     * @param relation    附属关系
     * @param mainGroup   主项目（用于获取主项目部位信息）
     */
    private void setAttachGroupPartInfo(CustomerRegItemGroup attachGroup, ItemGroupRelation relation, CustomerRegItemGroup mainGroup) {
        // 优先使用配置中指定的附属项目部位
        if (StringUtils.isNotBlank(relation.getRelationCheckPartId())) {
            attachGroup.setCheckPartId(relation.getRelationCheckPartId());
            attachGroup.setCheckPartName(relation.getRelationCheckPartName());
            attachGroup.setCheckPartCode(relation.getRelationCheckPartCode());
        }
        // 如果附属项目没有配置部位，但主项目有部位，则使用主项目的部位
        else if (StringUtils.isNotBlank(mainGroup.getCheckPartId())) {
            attachGroup.setCheckPartId(mainGroup.getCheckPartId());
            attachGroup.setCheckPartName(mainGroup.getCheckPartName());
            attachGroup.setCheckPartCode(mainGroup.getCheckPartCode());
            log.info("附属项目{}使用主项目{}的部位信息: {}", attachGroup.getItemGroupName(), mainGroup.getItemGroupName(), mainGroup.getCheckPartName());
        }
    }

    /**
     * 根据部位逻辑筛选匹配的赠送项目
     *
     * @param itemGroupRelations 所有赠送关系
     * @param mainGroup          主项目
     * @return 匹配的赠送关系列表
     */
    private List<ItemGroupRelation> filterGiftGroupsByPart(List<ItemGroupRelation> itemGroupRelations, CustomerRegItemGroup mainGroup) {
        if (CollectionUtils.isEmpty(itemGroupRelations)) {
            return itemGroupRelations;
        }

        String mainCheckPartId = mainGroup.getCheckPartId();

        // 如果主项目没有部位信息，返回所有没有部位限制的赠送项目
        if (StringUtils.isBlank(mainCheckPartId)) {
            return itemGroupRelations.stream().filter(relation -> StringUtils.isBlank(relation.getMainCheckPartId())).collect(Collectors.toList());
        }

        // 如果主项目有部位信息，筛选匹配的赠送项目
        List<ItemGroupRelation> matchedRelations = itemGroupRelations.stream().filter(relation -> {
            // 如果赠送关系没有指定主项目部位，则匹配所有部位
            if (StringUtils.isBlank(relation.getMainCheckPartId())) {
                return true;
            }
            // 如果指定了主项目部位，则必须完全匹配
            return StringUtils.equals(mainCheckPartId, relation.getMainCheckPartId());
        }).collect(Collectors.toList());

        log.info("主项目{}(部位:{})匹配到{}个赠送项目", mainGroup.getItemGroupName(), mainGroup.getCheckPartName(), matchedRelations.size());

        return matchedRelations;
    }

    /**
     * 为赠送项目设置部位信息
     *
     * @param giftGroup 赠送项目
     * @param relation  赠送关系
     * @param mainGroup 主项目（用于获取主项目部位信息）
     */
    private void setGiftGroupPartInfo(CustomerRegItemGroup giftGroup, ItemGroupRelation relation, CustomerRegItemGroup mainGroup) {
        // 优先使用配置中指定的赠送项目部位
        if (StringUtils.isNotBlank(relation.getRelationCheckPartId())) {
            giftGroup.setCheckPartId(relation.getRelationCheckPartId());
            giftGroup.setCheckPartName(relation.getRelationCheckPartName());
            giftGroup.setCheckPartCode(relation.getRelationCheckPartCode());

            // 更新项目名称包含部位信息，并标记为赠送
            String originalName = giftGroup.getItemGroupName();

            giftGroup.setItemGroupName(originalName + "(赠送)");

        }
        // 如果赠送项目没有配置部位，但主项目有部位，则使用主项目的部位
        else if (StringUtils.isNotBlank(mainGroup.getCheckPartId())) {
            giftGroup.setCheckPartId(mainGroup.getCheckPartId());
            giftGroup.setCheckPartName(mainGroup.getCheckPartName());
            giftGroup.setCheckPartCode(mainGroup.getCheckPartCode());

            // 更新项目名称包含部位信息，并标记为赠送
            String originalName = giftGroup.getItemGroupName();

            giftGroup.setItemGroupName(originalName + "(赠送)");


            log.info("赠送项目{}使用主项目{}的部位信息: {}", giftGroup.getItemGroupName(), mainGroup.getItemGroupName(), mainGroup.getCheckPartName());
        } else {
            // 没有部位信息时也要标记为赠送
            String originalName = giftGroup.getItemGroupName();
            if (!originalName.contains("(赠送)")) {
                giftGroup.setItemGroupName(originalName + "(赠送)");
            }
        }
    }

    /**
     * 批量获取项目关系
     */
    @Override
    public Map<String, GroupRelationVO> batchGetRelationsByMainIds(List<String> mainIds) {
        if (CollectionUtils.isEmpty(mainIds)) {
            return new HashMap<>();
        }

        log.info("批量获取项目关系，项目数量: {}", mainIds.size());

        Map<String, GroupRelationVO> resultMap = new HashMap<>();

        // 批量查询所有关系数据
        List<ItemGroupRelation> allRelations = list(new LambdaQueryWrapper<ItemGroupRelation>()
            .in(ItemGroupRelation::getGroupId, mainIds));

        // 按主项目ID分组
        Map<String, List<ItemGroupRelation>> relationsByMainId = allRelations.stream()
            .collect(Collectors.groupingBy(ItemGroupRelation::getGroupId));

        // 批量查询互斥关系（需要查询反向关系）
        List<ItemGroupRelation> exclusiveRelations = list(new LambdaQueryWrapper<ItemGroupRelation>()
            .eq(ItemGroupRelation::getRelation, "互斥")
            .and(wrapper -> wrapper.in(ItemGroupRelation::getGroupId, mainIds)
                .or().in(ItemGroupRelation::getRelationGroupId, mainIds)));

        // 为每个主项目构建关系VO
        for (String mainId : mainIds) {
            GroupRelationVO groupRelationVO = new GroupRelationVO();
            groupRelationVO.setGroupId(mainId);

            List<ItemGroupRelation> relations = relationsByMainId.getOrDefault(mainId, new ArrayList<>());

            // 设置附属关系
            List<ItemGroupRelation> attachGroups = relations.stream()
                .filter(r -> "附属".equals(r.getRelation()))
                .collect(Collectors.toList());
            groupRelationVO.setAttachGroups(attachGroups);

            // 设置赠送关系
            List<ItemGroupRelation> giftGroups = relations.stream()
                .filter(r -> "赠送".equals(r.getRelation()))
                .collect(Collectors.toList());
            groupRelationVO.setGiftGroups(giftGroups);

            // 设置依赖关系
            List<ItemGroupRelation> dependentGroups = relations.stream()
                .filter(r -> "依赖".equals(r.getRelation()))
                .collect(Collectors.toList());
            groupRelationVO.setDependentGroups(dependentGroups);

            // 设置互斥关系（包括正向和反向）
            List<String> exclusiveGroupIds = new ArrayList<>();
            for (ItemGroupRelation exclusiveRelation : exclusiveRelations) {
                if (mainId.equals(exclusiveRelation.getGroupId())) {
                    exclusiveGroupIds.add(exclusiveRelation.getRelationGroupId());
                } else if (mainId.equals(exclusiveRelation.getRelationGroupId())) {
                    exclusiveGroupIds.add(exclusiveRelation.getGroupId());
                }
            }
            groupRelationVO.setExclusiveGroups(exclusiveGroupIds);

            resultMap.put(mainId, groupRelationVO);
        }

        log.info("批量获取项目关系完成，返回 {} 个项目的关系数据", resultMap.size());

        return resultMap;
    }

    /**
     * 分析项目依赖关系
     */
    @Override
    public List<DependencyAnalysisVO> analyzeDependencies(List<String> itemGroupIds, String customerRegId) {
        if (CollectionUtils.isEmpty(itemGroupIds)) {
            return new ArrayList<>();
        }

        log.info("开始分析项目依赖关系，项目数量: {}, 登记ID: {}", itemGroupIds.size(), customerRegId);

        // 1. 获取现有项目列表
        Set<String> existingItemIds = customerRegItemGroupService.list(
            new LambdaQueryWrapper<CustomerRegItemGroup>()
                .eq(CustomerRegItemGroup::getCustomerRegId, customerRegId)
                .ne(CustomerRegItemGroup::getAddMinusFlag, -1)
                .ne(CustomerRegItemGroup::getPayStatus, "退款成功")
        ).stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());

        // 2. 批量获取项目关系
        Map<String, GroupRelationVO> relationMap = batchGetRelationsByMainIds(itemGroupIds);

        // 3. 获取项目字典
        Map<String, ItemGroup> itemGroupDict = getItemGroupDict(itemGroupIds, relationMap);

        // 4. 分析每个项目的依赖关系
        List<DependencyAnalysisVO> analysisResults = new ArrayList<>();

        for (String itemGroupId : itemGroupIds) {
            DependencyAnalysisVO analysis = new DependencyAnalysisVO();
            analysis.setItemGroupId(itemGroupId);

            ItemGroup itemGroup = itemGroupDict.get(itemGroupId);
            if (itemGroup != null) {
                analysis.setItemGroupName(itemGroup.getName());
            }

            GroupRelationVO relationVO = relationMap.get(itemGroupId);
            if (relationVO != null) {
                // 分析依赖关系
                List<MissingDependencyVO> missingDeps = analyzeMissingDependencies(
                    relationVO.getDependentGroups(), existingItemIds, itemGroupDict);
                analysis.setMissingDependencies(missingDeps);

                // 设置关系信息
                analysis.setDependentGroups(convertToRelationItems(relationVO.getDependentGroups(), itemGroupDict));
                analysis.setAttachGroups(convertToRelationItems(relationVO.getAttachGroups(), itemGroupDict));
                analysis.setGiftGroups(convertToRelationItems(relationVO.getGiftGroups(), itemGroupDict));
                analysis.setExclusiveGroups(relationVO.getExclusiveGroups());

                // 分析项目来源类型
                String sourceType = analyzeItemSourceType(itemGroupId, existingItemIds, relationMap);
                analysis.setSourceType(sourceType);
            }

            analysisResults.add(analysis);
        }

        log.info("项目依赖关系分析完成，返回 {} 个项目的分析结果", analysisResults.size());

        return analysisResults;
    }

    /**
     * 获取项目字典
     */
    private Map<String, ItemGroup> getItemGroupDict(List<String> baseItemIds, Map<String, GroupRelationVO> relationMap) {
        Set<String> allNeededIds = new HashSet<>(baseItemIds);

        // 收集所有关系中涉及的项目ID
        relationMap.values().forEach(relationVO -> {
            if (CollectionUtils.isNotEmpty(relationVO.getDependentGroups())) {
                relationVO.getDependentGroups().forEach(dep -> {
                    if (StringUtils.isNotBlank(dep.getRelationGroupId())) {
                        allNeededIds.add(dep.getRelationGroupId());
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(relationVO.getAttachGroups())) {
                relationVO.getAttachGroups().forEach(attach -> {
                    if (StringUtils.isNotBlank(attach.getRelationGroupId())) {
                        allNeededIds.add(attach.getRelationGroupId());
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(relationVO.getGiftGroups())) {
                relationVO.getGiftGroups().forEach(gift -> {
                    if (StringUtils.isNotBlank(gift.getRelationGroupId())) {
                        allNeededIds.add(gift.getRelationGroupId());
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(relationVO.getExclusiveGroups())) {
                allNeededIds.addAll(relationVO.getExclusiveGroups());
            }
        });

        // 批量查询项目信息
        if (allNeededIds.isEmpty()) {
            return new HashMap<>();
        }

        List<ItemGroup> itemGroups = itemGroupMapper.selectBatchIds(allNeededIds);

        return itemGroups.stream()
            .collect(Collectors.toMap(ItemGroup::getId, Function.identity()));
    }

    /**
     * 分析缺失的依赖项目
     */
    private List<MissingDependencyVO> analyzeMissingDependencies(List<ItemGroupRelation> dependentGroups,
                                                               Set<String> existingItemIds,
                                                               Map<String, ItemGroup> itemGroupDict) {
        List<MissingDependencyVO> missingDeps = new ArrayList<>();

        if (CollectionUtils.isEmpty(dependentGroups)) {
            return missingDeps;
        }

        // 按大项分组依赖关系
        Map<String, List<ItemGroupRelation>> dependentGroupsMap = dependentGroups.stream()
            .collect(Collectors.groupingBy(ItemGroupRelation::getRelationGroupId));

        // 检查每个依赖的大项是否存在
        for (Map.Entry<String, List<ItemGroupRelation>> entry : dependentGroupsMap.entrySet()) {
            String dependentGroupId = entry.getKey();
            List<ItemGroupRelation> dependentItems = entry.getValue();

            if (!existingItemIds.contains(dependentGroupId)) {
                // 构建缺失依赖项目信息
                MissingDependencyVO missingDep = new MissingDependencyVO();
                missingDep.setDependentId(dependentGroupId);
                missingDep.setDependentType("GROUP");

                // 设置依赖项目名称
                ItemGroup dependentGroup = itemGroupDict.get(dependentGroupId);
                if (dependentGroup != null) {
                    missingDep.setDependentName(dependentGroup.getName());
                } else {
                    missingDep.setDependentName("未知项目(" + dependentGroupId + ")");
                }

                // 构建依赖的具体小项信息
                String itemDetails = dependentItems.stream()
                    .map(ItemGroupRelation::getRelationItemName)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining("、"));
                missingDep.setDependentItemDetails(itemDetails);

                missingDeps.add(missingDep);
            }
        }

        return missingDeps;
    }

    /**
     * 转换为关系项目VO
     */
    private List<RelationItemVO> convertToRelationItems(List<ItemGroupRelation> relations, Map<String, ItemGroup> itemGroupDict) {
        if (CollectionUtils.isEmpty(relations)) {
            return new ArrayList<>();
        }

        return relations.stream().map(relation -> {
            RelationItemVO vo = new RelationItemVO();
            vo.setRelationGroupId(relation.getRelationGroupId());
            vo.setRelationItemType(relation.getRelationItemType());
            vo.setRelationItemId(relation.getRelationItemId());
            vo.setRelationItemName(relation.getRelationItemName());
            vo.setQuantity(relation.getQuantity());

            // 设置项目组名称
            ItemGroup itemGroup = itemGroupDict.get(relation.getRelationGroupId());
            if (itemGroup != null) {
                vo.setRelationGroupName(itemGroup.getName());
            } else {
                vo.setRelationGroupName("未知项目(" + relation.getRelationGroupId() + ")");
            }

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 分析项目来源类型
     */
    private String analyzeItemSourceType(String itemGroupId, Set<String> existingItemIds, Map<String, GroupRelationVO> relationMap) {
        // 检查是否是其他项目的依赖项目
        for (Map.Entry<String, GroupRelationVO> entry : relationMap.entrySet()) {
            String otherItemId = entry.getKey();
            GroupRelationVO relationVO = entry.getValue();

            if (otherItemId.equals(itemGroupId)) {
                continue;
            }

            // 检查依赖关系
            if (CollectionUtils.isNotEmpty(relationVO.getDependentGroups())) {
                boolean isDependent = relationVO.getDependentGroups().stream()
                    .anyMatch(r -> itemGroupId.equals(r.getRelationGroupId()));
                if (isDependent) {
                    return "dependent";
                }
            }

            // 检查赠送关系
            if (CollectionUtils.isNotEmpty(relationVO.getGiftGroups())) {
                boolean isGift = relationVO.getGiftGroups().stream()
                    .anyMatch(r -> itemGroupId.equals(r.getRelationGroupId()));
                if (isGift) {
                    return "gift";
                }
            }

            // 检查附属关系
            if (CollectionUtils.isNotEmpty(relationVO.getAttachGroups())) {
                boolean isAttach = relationVO.getAttachGroups().stream()
                    .anyMatch(r -> itemGroupId.equals(r.getRelationGroupId()));
                if (isAttach) {
                    return "attach";
                }
            }
        }

        return "main";
    }
}

