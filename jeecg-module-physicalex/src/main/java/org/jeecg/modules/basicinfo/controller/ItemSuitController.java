package org.jeecg.modules.basicinfo.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.excommons.utils.InitialUtil;
import org.jeecg.modules.basicinfo.bo.ItemGroupLabel;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemSuit;
import org.jeecg.modules.basicinfo.entity.SuitGroup;
import org.jeecg.modules.basicinfo.service.IItemSuitService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.jeecg.modules.basicinfo.entity.GroupRelationVO;
import org.jeecg.modules.mobile.utils.FileUrlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 体检套餐
 * @Author: jeecg-boot
 * @Date: 2024-02-03
 * @Version: V1.0
 */
@Api(tags = "体检套餐")
@RestController
@RequestMapping("/basicinfo/itemSuit")
@Slf4j
public class ItemSuitController extends JeecgController<ItemSuit, IItemSuitService> {
    @Autowired
    private IItemSuitService itemSuitService;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private IItemGroupRelationService itemGroupRelationService;


    @AutoLog(value = "体检套餐-获取套餐内按项目科室分类的项目组合")
    @ApiOperation(value = "体检套餐-获取套餐内按项目科室分类的项目组合", notes = "体检套餐-获取套餐内按项目科室分类的项目组合")
    @GetMapping(value = "/getItemGroupsBySuitId")
    public Result<?> getItemGroupsBySuitId(@RequestParam(name = "suitId") String suitId) {
        List<ItemGroup> itemGroups = itemSuitService.getGroupOfSuit(suitId);
        List<ItemGroupLabel> labels = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(itemGroups)) {
            Map<String, List<ItemGroup>> groupbyDepartMap = itemGroups.stream().collect(Collectors.groupingBy(ItemGroup::getDepartmentName));
            for (String departName : groupbyDepartMap.keySet()) {
                ItemGroupLabel itemGroupLabel = new ItemGroupLabel();
                itemGroupLabel.setLabel(departName);
                itemGroupLabel.setItemList(groupbyDepartMap.get(departName));
                labels.add(itemGroupLabel);
            }
        }
        return Result.OK(labels);
    }

    @AutoLog(value = "体检套餐-根据套餐类别获取套餐")
    @ApiOperation(value = "体检套餐-根据套餐类别获取套餐", notes = "体检套餐-根据套餐类别获取套餐")
    @GetMapping(value = "/getSuitByCategory")
    public Result<?> getSuitByCategory(@RequestParam(name = "categoryId") String categoryId, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        LambdaQueryWrapper<ItemSuit> queryWrapper = new LambdaQueryWrapper<ItemSuit>().eq(ItemSuit::getCategoryId, categoryId).notIn(ItemSuit::getSuitType, "加项", "组单", "系统套餐").orderByAsc(ItemSuit::getSort);
        Page<ItemSuit> page = new Page<ItemSuit>(pageNo, pageSize);
        queryWrapper.eq(ItemSuit::getDelFlag, 0);
        queryWrapper.eq(ItemSuit::getPubAvailable, "1");
        queryWrapper.eq(ItemSuit::getEnableFlag, 1);
        IPage<ItemSuit> pageList = itemSuitService.page(page, queryWrapper);
        String openFileUrl = sysSettingService.getValueByCode("open_file_url");
        pageList.getRecords().forEach(suit -> {
            suit.setSuitPicture(FileUrlUtils.replaceUrl(suit.getSuitPicture(), openFileUrl));
        });
        return Result.OK(pageList);
    }

    @AutoLog(value = "体检套餐-根据套餐类别获取套餐")
    @ApiOperation(value = "体检套餐-根据套餐类别获取套餐", notes = "体检套餐-根据套餐类别获取套餐")
    @GetMapping(value = "/pageSuit")
    public Result<?> pageSuit(String categoryId, String gender, String priceRange, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<ItemSuit> page = new Page<ItemSuit>(pageNo, pageSize);
        itemSuitService.pageSuit4H5(page, categoryId, priceRange, gender);
        return Result.OK(page);
    }

    //getSuitDetail
    @AutoLog(value = "体检套餐-获取套餐详情")
    @ApiOperation(value = "体检套餐-获取套餐详情", notes = "体检套餐-获取套餐详情")
    @GetMapping(value = "/getSuitDetail")
    public Result<?> getSuitDetail(String suitId) {
        ItemSuit itemSuit = itemSuitService.getById(suitId);
        if (Objects.isNull(itemSuit)) {
            return Result.error("未找到对应数据");
        }
        List<ItemGroup> itemGroups = itemSuitService.getGroupsBySuitId(suitId);
        itemSuit.setItemGroupList(itemGroups);
        itemSuit.setSuitPicture(FileUrlUtils.replaceUrl(itemSuit.getSuitPicture(), sysSettingService.getValueByCode("open_file_url")));
        return Result.OK(itemSuit);
    }
   



    @AutoLog(value = "体检套餐-获取套餐内的体检组合")
    @ApiOperation(value = "体检套餐-获取套餐内的体检组合", notes = "体检套餐-获取套餐内的体检组合")
    @GetMapping(value = "/getGroupsBySuitId")
    public Result<?> getGroupsBySuitId(String suitId) {
        List<ItemGroup> itemGroups = itemSuitService.getGroupsBySuitId(suitId);

        return Result.OK(itemGroups);
    }

    /**
     * 分页列表查询
     *
     * @param itemSuit
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "体检套餐-分页列表查询")
    @ApiOperation(value = "体检套餐-分页列表查询", notes = "体检套餐-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ItemSuit>> queryPageList(ItemSuit itemSuit, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<ItemSuit> queryWrapper = QueryGenerator.initQueryWrapper(itemSuit, req.getParameterMap());
        Page<ItemSuit> page = new Page<ItemSuit>(pageNo, pageSize);
        IPage<ItemSuit> pageList = itemSuitService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 根据关键字查询
     *
     * @param keyword
     * @return
     */
    //@AutoLog(value = "体检套餐-分页列表查询")
    @ApiOperation(value = "体检套餐-分页列表查询", notes = "体检套餐-分页列表查询")
    @GetMapping(value = "/listByKeyword")
    public Result<?> listByKeyword(String keyword) {
        List<ItemSuit> list = itemSuitService.listByKeyword(keyword);
        return Result.OK(list);
    }

    @ApiOperation(value = "体检套餐-回收站", notes = "体检套餐-回收站")
    @GetMapping(value = "/listSuitRecycleBin")
    public Result<IPage<ItemSuit>> listRecycleBin(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        String keyword = req.getParameter("keyword");
        Page<ItemSuit> page = new Page<ItemSuit>(pageNo, pageSize);
        Page<ItemSuit> pageList = itemSuitService.pageRecycleBinItemGroup(page, keyword);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "体检套餐-批量回收", notes = "体检套餐-批量回收")
    @GetMapping(value = "/batchRecover")
    public Result<?> batchRecover(String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        itemSuitService.batchRecover(idList);
        itemSuitService.evcitCache();
        return Result.OK("操作成功");
    }

    /**
     * 批量永久删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检套餐-批量永久删除")
    @ApiOperation(value = "体检套餐-批量删除", notes = "体检套餐-批量永久删除")
    @RequiresPermissions("basicinfo:item_suit:deleteBatch")
    @DeleteMapping(value = "/deleteBatchForever")
    public Result<String> deleteBatchForever(@RequestParam(name = "ids", required = true) String ids) {
        try {
            this.itemSuitService.removeForever(Arrays.asList(ids.split(",")));
            itemSuitService.evcitCache();
            return Result.OK("批量删除成功!");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param itemSuit
     * @return
     */
    @AutoLog(value = "体检套餐-添加")
    @ApiOperation(value = "体检套餐-添加", notes = "体检套餐-添加")
    @RequiresPermissions("basicinfo:item_suit:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ItemSuit itemSuit) {
        //生成首拼
        String spelling = InitialUtil.generateInitial(itemSuit.getName());
        itemSuit.setHelpChar(spelling);
        itemSuitService.save(itemSuit);
        itemSuitService.evcitCache();
        return Result.OK("添加成功！", itemSuit.getId());
    }

    /**
     * 编辑
     *
     * @param itemSuit
     * @return
     */
    @AutoLog(value = "体检套餐-编辑")
    @ApiOperation(value = "体检套餐-编辑", notes = "体检套餐-编辑")
    @RequiresPermissions("basicinfo:item_suit:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ItemSuit itemSuit) {
        //生成首拼
        String spelling = InitialUtil.generateInitial(itemSuit.getName());
        itemSuit.setHelpChar(spelling);
        itemSuitService.updateById(itemSuit);
        itemSuitService.evcitCache();
        return Result.OK("编辑成功!", itemSuit.getId());
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "体检套餐-通过id删除")
    @ApiOperation(value = "体检套餐-通过id删除", notes = "体检套餐-通过id删除")
    @RequiresPermissions("basicinfo:item_suit:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        itemSuitService.removeById(id);
        itemSuitService.evcitCache();
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "体检套餐-批量删除")
    @ApiOperation(value = "体检套餐-批量删除", notes = "体检套餐-批量删除")
    @RequiresPermissions("basicinfo:item_suit:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.itemSuitService.removeByIds(Arrays.asList(ids.split(",")));
        itemSuitService.evcitCache();
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "体检套餐-通过id查询")
    @ApiOperation(value = "体检套餐-通过id查询", notes = "体检套餐-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ItemSuit> queryById(@RequestParam(name = "id", required = true) String id) {
        ItemSuit itemSuit = itemSuitService.getById(id);
        if (itemSuit == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(itemSuit);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param itemSuit
     */
    @RequiresPermissions("basicinfo:item_suit:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItemSuit itemSuit) {
        return super.exportXls(request, itemSuit, ItemSuit.class, "体检套餐");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("basicinfo:item_suit:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ItemSuit.class);
    }

    /**
     * 获取下一个排序号
     *
     * @return
     */
    @AutoLog(value = "体检套餐-获取下一个排序号")
    @ApiOperation(value = "体检套餐-获取下一个排序号", notes = "体检套餐-获取下一个排序号")
    @GetMapping(value = "/getNextSort")
    public Result<Integer> getNextSort() {
        Integer nextSort = itemSuitService.getNextSort();
        return Result.OK(nextSort);
    }

    /**
     * 获取套餐内的体检组合（包含关系数据）
     */
    @AutoLog(value = "体检套餐-获取套餐内的体检组合")
    @ApiOperation(value = "体检套餐-获取套餐内的体检组合", notes = "体检套餐-获取套餐内的体检组合")
    @GetMapping(value = "/getGroupBySuit")
    public Result<?> getGroupBySuit(String suitId) {
        try {
            // 1. 获取基础的套餐项目列表
            List<SuitGroup> suitGroupList = itemSuitService.getGroupOfSuit(suitId, true);

            if (CollectionUtils.isNotEmpty(suitGroupList)) {
                // 2. 提取项目ID列表
                List<String> itemGroupIds = suitGroupList.stream()
                    .map(SuitGroup::getGroupId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                // 3. 批量获取项目关系信息
                Map<String, GroupRelationVO> relationMap = itemGroupRelationService.batchGetRelationsByMainIds(itemGroupIds);

                // 4. 为每个项目添加依赖关系信息
                for (SuitGroup suitGroup : suitGroupList) {
                    GroupRelationVO relation = relationMap.get(suitGroup.getGroupId());
                    if (relation != null) {
                        // 转换为Object列表
                        suitGroup.setDependentGroups(new ArrayList<>(relation.getDependentGroups()));
                        suitGroup.setAttachGroups(new ArrayList<>(relation.getAttachGroups()));
                        suitGroup.setGiftGroups(new ArrayList<>(relation.getGiftGroups()));
                        suitGroup.setExclusiveGroups(relation.getExclusiveGroups());
                    }
                }
            }

            JSONObject result = new JSONObject();
            result.put("suitGroupList", suitGroupList);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("获取套餐项目列表失败", e);
            // 降级到原有逻辑
            List<SuitGroup> suitGroupList = itemSuitService.getGroupOfSuit(suitId, true);
            JSONObject result = new JSONObject();
            result.put("suitGroupList", suitGroupList);
            return Result.OK(result);
        }
    }

    /**
     * 获取套餐内的体检组合
     */
    @AutoLog(value = "体检套餐-获取套餐内的体检组合")
    @ApiOperation(value = "体检套餐-获取套餐内的体检组合", notes = "体检套餐-获取套餐内的体检组合")
    @GetMapping(value = "/getSuitGroup")
    public Result<?> getSuitGroupList(String suitId) {
        List<SuitGroup> suitGroupList = itemSuitService.getGroupOfSuit(suitId, false);
        return Result.OK(suitGroupList);
    }

    /**
     * 获取套餐内的体检组合
     */
    @AutoLog(value = "体检套餐-获取套餐内的体检组合")
    @ApiOperation(value = "体检套餐-获取套餐内的体检组合", notes = "体检套餐-获取套餐内的体检组合")
    @GetMapping(value = "/getGroupOfSuit")
    public Result<?> getGroupOfSuit2(String suitId) {
        List<ItemGroup> suitGroupList = itemSuitService.getGroupOfSuit(suitId);
        return Result.OK(suitGroupList);
    }


    //saveGroupOfSuit
    @AutoLog(value = "体检套餐-保存套餐内的体检组合")
    @ApiOperation(value = "体检套餐-保存套餐内的体检组合", notes = "体检套餐-保存套餐内的体检组合")
    @PostMapping(value = "/saveGroupOfSuit")
    public Result<?> saveGroupOfSuit(@RequestBody JSONObject info) {
        String suitId = info.getString("suitId");
        List<SuitGroup> suitGroupList = info.getJSONArray("suitGroupList").toJavaList(SuitGroup.class);
        itemSuitService.updateSuitGroup(suitId, suitGroupList);
        itemSuitService.evcitCache();
        return Result.OK("保存成功!");
    }

    //addItemGroupsToSuit - 专门用于套餐的项目添加接口
    @AutoLog(value = "体检套餐-添加项目到套餐（含附属项目和赠送项目处理）")
    @ApiOperation(value = "体检套餐-添加项目到套餐（含附属项目和赠送项目处理）", notes = "体检套餐-添加项目到套餐（含附属项目和赠送项目处理）")
    @PostMapping(value = "/addItemGroupsToSuit")
    public Result<?> addItemGroupsToSuit(@RequestBody JSONObject params) {
        try {
            String suitId = params.getString("suitId");
            List<SuitGroup> itemGroups = params.getJSONArray("itemGroups").toJavaList(SuitGroup.class);
            // 新增参数：是否处理附属项目和赠送项目，默认为true保持向后兼容
            Boolean processRelatedItems = params.getBoolean("processRelatedItems");
            if (processRelatedItems == null) {
                processRelatedItems = true;
            }

            if (suitId == null || suitId.trim().isEmpty()) {
                return Result.error("套餐ID不能为空");
            }

            if (itemGroups == null || itemGroups.isEmpty()) {
                return Result.error("项目列表不能为空");
            }

            // 调用服务层方法处理项目添加
            itemSuitService.addItemGroupsToSuit(suitId, itemGroups, processRelatedItems);

            // 清除缓存
            itemSuitService.evcitCache();

            String message = processRelatedItems ?
                "项目添加成功，已自动处理附属项目和赠送项目" :
                "项目添加成功";
            return Result.OK(message);

        } catch (Exception e) {
            log.error("添加项目到套餐失败", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }
    @ApiOperation(value = "项目组合-批量更新启用状态", notes = "项目组合-批量更新启用状态")
    @PostMapping(value = "/batchUpdateEnableFlag")
    public Result<?> batchUpdateEnableFlag(@RequestBody JSONObject info) {
        List<String> idList = info.getJSONArray("ids").toJavaList(String.class);
        String enableFlag = info.getString("enableFlag");
        itemSuitService.batchUpdateEnableFlag(idList, enableFlag);
        itemSuitService.evcitCache();
        return Result.OK("操作成功");
    }



}
