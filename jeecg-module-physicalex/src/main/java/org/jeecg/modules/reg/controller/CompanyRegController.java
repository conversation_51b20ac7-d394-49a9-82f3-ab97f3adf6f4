package org.jeecg.modules.reg.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.basicinfo.service.ICompanyService;
import org.jeecg.modules.fee.entity.LimitOperationRecord;
import org.jeecg.modules.fee.entity.TeamCustomerLimitAmount;
import org.jeecg.modules.fee.service.ICompayBenefitsSettingService;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.fee.service.ILimitOperationRecordService;
import org.jeecg.modules.fee.service.ITeamCustomerLimitAmountService;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CompanyTeam;
import org.jeecg.modules.reg.entity.CompanyTeamItemGroup;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICompanyRegService;
import org.jeecg.modules.reg.service.ICompanyTeamService;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.comInterface.service.IEnterpriseNotificationService;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;
import java.util.stream.Collectors;
import org.jeecg.modules.basicinfo.entity.GroupRelationVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 单位预约
 * @Author: jeecg-boot
 * @Date: 2024-02-19
 * @Version: V1.0
 */
@Api(tags = "单位预约")
@RestController
@RequestMapping("/reg/companyReg")
@Slf4j
public class CompanyRegController extends JeecgController<CompanyReg, ICompanyRegService> {

    @Autowired
    private ICompanyRegService companyRegService;

    @Autowired
    private ICompanyTeamService companyTeamService;

    @Autowired
    private ICompayBenefitsSettingService companyBenefitsSettingService;
    @Autowired
    private ITeamCustomerLimitAmountService teamCustomerLimitAmountService;
    @Autowired
    private ILimitOperationRecordService limitOperationRecordService;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private IEnterpriseNotificationService enterpriseNotificationService;
    @Autowired
    private IItemGroupRelationService itemGroupRelationService;

    //searchCompanyReg
    @ApiOperation("单位预约-分页列表查询")
    @GetMapping(value = "/searchCompanyReg")
    public Result<?> searchCompanyReg(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        String keyword = req.getParameter("keyword");
        String id = req.getParameter("id");
        List<String> ids = StringUtils.isNotBlank(id) ? Arrays.asList(id.split(",")) : null;
        Page<CompanyReg> page = new Page<>(pageNo, pageSize);
        List<CompanyReg> list = companyRegService.pageCompanyReg(page, keyword, ids);
        return Result.OK(list);
    }

    @ApiOperation("单位预约-分页查询部门信息")
    @GetMapping(value = "/searchCompanyDeptByPid")
    public Result<?> searchCompanyDeptByPid(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        String keyword = req.getParameter("keyword");
        String id = req.getParameter("id");
        String pid = req.getParameter("pid");
        List<String> ids = StringUtils.isNotBlank(id)? Arrays.asList(id.split(",")):null;
        Page<Company> page = new Page<>(pageNo, pageSize);
        List<Company> list = companyRegService.pageCompanyByPid(page, keyword,ids,pid);
        return Result.OK(list);
    }
    /*---------------------------------主表处理-begin-------------------------------------*/

    /**
     * 分页列表查询
     *
     * @param companyReg
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "单位预约-分页列表查询")
    @ApiOperation(value = "单位预约-分页列表查询", notes = "单位预约-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CompanyReg>> queryPageList(CompanyReg companyReg, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CompanyReg> queryWrapper = QueryGenerator.initQueryWrapper(companyReg, req.getParameterMap());
        Page<CompanyReg> page = new Page<CompanyReg>(pageNo, pageSize);
        IPage<CompanyReg> pageList = companyRegService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param companyReg
     * @return
     */
    @AutoLog(value = "单位预约-添加")
    @ApiOperation(value = "单位预约-添加", notes = "单位预约-添加")
    @RequiresPermissions("reg:company_reg:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody CompanyReg companyReg) {
        companyRegService.save(companyReg);
        return Result.OK("添加成功！", companyReg);
    }

    /**
     * 编辑
     *
     * @param companyReg
     * @return
     */
    @AutoLog(value = "单位预约-编辑")
    @ApiOperation(value = "单位预约-编辑", notes = "单位预约-编辑")
    @RequiresPermissions("reg:company_reg:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody CompanyReg companyReg) {
        companyRegService.updateById(companyReg);
        return Result.OK("编辑成功!", companyReg);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "单位预约-通过id删除")
    @ApiOperation(value = "单位预约-通过id删除", notes = "单位预约-通过id删除")
    @RequiresPermissions("reg:company_reg:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        companyRegService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "单位预约-批量删除")
    @ApiOperation(value = "单位预约-批量删除", notes = "单位预约-批量删除")
    @RequiresPermissions("reg:company_reg:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.companyRegService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     *
     * @return
     */
    @RequiresPermissions("reg:company_reg:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CompanyReg companyReg) {
        return super.exportXls(request, companyReg, CompanyReg.class, "单位预约");
    }

    /**
     * 导入
     *
     * @return
     */
    @RequiresPermissions("reg:company_reg:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CompanyReg.class);
    }
    /*---------------------------------主表处理-end-------------------------------------*/


    /*--------------------------------子表处理-单位分组-begin----------------------------------------------*/

    /**
     * 通过主表ID查询
     *
     * @return
     */
    //@AutoLog(value = "单位分组-通过主表ID查询")
    @ApiOperation(value = "单位分组-通过主表ID查询", notes = "单位分组-通过主表ID查询")
    @GetMapping(value = "/listCompanyTeamByMainId")
    public Result<IPage<CompanyTeam>> listCompanyTeamByMainId(CompanyTeam companyTeam, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CompanyTeam> queryWrapper = QueryGenerator.initQueryWrapper(companyTeam, req.getParameterMap());
        Page<CompanyTeam> page = new Page<CompanyTeam>(pageNo, pageSize);
        IPage<CompanyTeam> pageList = companyTeamService.page(page, queryWrapper);
        String companyRegId=req.getParameter("companyRegId");
        CompanyReg companyReg = companyRegService.getById(companyRegId);
        if(Objects.nonNull(companyReg)){
            pageList.getRecords().forEach(team->{
                team.setCompanyReg(companyReg);
            });
        }
        return Result.OK(pageList);
    }
    @ApiOperation(value = "单位分组-通过主表ID查询", notes = "单位分组-通过主表ID查询")
    @GetMapping(value = "/getCompanyRegById")
    public Result<?> getCompanyRegById( HttpServletRequest req) {
        String companyRegId=req.getParameter("companyRegId");
        CompanyReg companyReg = companyRegService.getById(companyRegId);
        if(Objects.nonNull(companyReg)){
            Company company = companyService.getById(companyReg.getCompanyId());
            companyReg.setCompany(company);
        }
        return Result.OK(companyReg);
    }
    /**
     * 添加
     *
     * @param companyTeam
     * @return
     */
    @AutoLog(value = "单位分组-添加")
    @ApiOperation(value = "单位分组-添加", notes = "单位分组-添加")
    @PostMapping(value = "/addCompanyTeam")
    public Result<?> addCompanyTeam(@RequestBody CompanyTeam companyTeam) {
        companyTeamService.fillTeamNum(companyTeam);
        companyTeamService.save(companyTeam);
        return Result.OK("添加成功！", companyTeam);
    }

    /**
     * 编辑
     *
     * @param companyTeam
     * @return
     */
    @AutoLog(value = "单位分组-编辑")
    @ApiOperation(value = "单位分组-编辑", notes = "单位分组-编辑")
    @RequestMapping(value = "/editCompanyTeam", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> editCompanyTeam(@RequestBody CompanyTeam companyTeam) {
        try {
            CompanyTeam oldCompanyTeam = companyTeamService.getById(companyTeam.getId());
            if (Objects.nonNull(oldCompanyTeam.getLimitAmount())) {
                List<TeamCustomerLimitAmount> limitAmountList = teamCustomerLimitAmountService.list(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getTeamId, oldCompanyTeam.getId()));
                List<String> limitIds = limitAmountList.stream().map(TeamCustomerLimitAmount::getId).toList();
                if (Objects.nonNull(companyTeam.getLimitAmount())) {
                    if (companyTeam.getLimitAmount().compareTo(oldCompanyTeam.getLimitAmount())!=0) {
                        List<LimitOperationRecord> operationRecordList = limitOperationRecordService.list(new LambdaQueryWrapper<LimitOperationRecord>().in(LimitOperationRecord::getLimitId, limitIds).ne(LimitOperationRecord::getOperation, "获取额度").ne(LimitOperationRecord::getOperation, "切换分组，额度变更").ne(LimitOperationRecord::getOperation, "单位分组调整额度"));
                        if (operationRecordList.size() > 0) {
                            throw new RuntimeException("该分组下存在额度消费记录，不允许修改额度!");
                        }
                    }
                    //需更新额度账号
                    if (companyTeam.getLimitAmount().compareTo(oldCompanyTeam.getLimitAmount())!=0 && StringUtils.equals(companyTeam.getAllowSyncAdjustLimit(), "1") && CollectionUtils.isNotEmpty(limitIds)) {
                        //更新额度账号
                        Map<String, String> limitId2Name = limitAmountList.stream().collect(Collectors.toMap(TeamCustomerLimitAmount::getId, TeamCustomerLimitAmount::getName));
                        LoginUser loginUser = null;
                        try {
                            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                        } catch (Exception e) {
                        }
                        String username = Objects.nonNull(loginUser)?loginUser.getUsername():"";
                        Lists.partition(limitIds, 500).forEach(batch -> {
                            teamCustomerLimitAmountService.update(new LambdaUpdateWrapper<TeamCustomerLimitAmount>().in(TeamCustomerLimitAmount::getId, batch).set(TeamCustomerLimitAmount::getAmount, companyTeam.getLimitAmount()));
                            //增加操作记录
                            List<LimitOperationRecord> recordList = batch.stream().map(limitId -> {
                                LimitOperationRecord record = new LimitOperationRecord();
                                record.setName(limitId2Name.get(limitId));
                                record.setCreateTime(new Date());
                                record.setCreateBy(username);
                                record.setBizId(companyTeam.getId());
                                record.setLimitId(limitId);
                                record.setOperation("单位分组调整额度");
                                record.setBusinessDesc("单位分组调整额度");
                                record.setAmount(companyTeam.getLimitAmount());
                                return record;
                            }).toList();
                            limitOperationRecordService.saveBatch(recordList);

                        });
                    }
                }else {
                   if (limitAmountList.size() > 0) {
                       throw new RuntimeException("该分组下存在额度创建记录，不允许修改额度!请重新创建分组");
                   }
                  /*  if (StringUtils.equals(companyTeam.getAllowSyncAdjustLimit(), "1") && CollectionUtils.isNotEmpty(limitIds)) {
                        //需删除额度账号
                        teamCustomerLimitAmountService.removeByIds(limitIds);
                        //删除额度记录
                        limitOperationRecordService.remove(new LambdaQueryWrapper<LimitOperationRecord>().in(LimitOperationRecord::getLimitId, limitIds));
                    }*/

                }
            }
            companyTeamService.fillTeamNum(companyTeam);
            companyTeamService.updateById(companyTeam);
//            companyBenefitsSettingService.saveSetting4CompanyTeam(companyTeam);
            return Result.OK("编辑成功!", companyTeam);
        } catch (Exception e) {
            log.error("编辑单位分组失败", e);
            return Result.error(e.getMessage());
        }

    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "单位分组-通过id删除")
    @ApiOperation(value = "单位分组-通过id删除", notes = "单位分组-通过id删除")
    @DeleteMapping(value = "/deleteCompanyTeam")
    public Result<String> deleteCompanyTeam(@RequestParam(name = "id", required = true) String id) {
        companyTeamService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "单位分组-批量删除")
    @ApiOperation(value = "单位分组-批量删除", notes = "单位分组-批量删除")
    @DeleteMapping(value = "/deleteBatchCompanyTeam")
    public Result<String> deleteBatchCompanyTeam(@RequestParam(name = "ids", required = true) String ids) {
        this.companyTeamService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog(value = "单位分组-复制")
    @ApiOperation(value = "单位分组-复制", notes = "单位分组-复制")
    @GetMapping(value = "/copyCompanyTeam")
    public Result<?> copyCompanyTeam(@RequestParam("teamId") String teamId) {
        try {
             companyTeamService.copyCompanyTeam(teamId);
            return Result.OK("复制成功!");
        } catch (Exception e) {
            log.error("复制单位分组失败", e);
            return Result.error(e.getMessage());
        }

    }
    /**
     * 导出
     *
     * @return
     */
    @RequestMapping(value = "/exportCompanyTeam")
    public ModelAndView exportCompanyTeam(HttpServletRequest request, CompanyTeam companyTeam) {
        // Step.1 组装查询条件
        QueryWrapper<CompanyTeam> queryWrapper = QueryGenerator.initQueryWrapper(companyTeam, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<CompanyTeam> pageList = companyTeamService.list(queryWrapper);
        List<CompanyTeam> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, "单位分组");
        mv.addObject(NormalExcelConstants.CLASS, CompanyTeam.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("单位分组报表", "导出人:" + sysUser.getRealname(), "单位分组"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 导入
     *
     * @return
     */
    @RequestMapping(value = "/importCompanyTeam/{mainId}")
    public Result<?> importCompanyTeam(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<CompanyTeam> list = ExcelImportUtil.importExcel(file.getInputStream(), CompanyTeam.class, params);
                for (CompanyTeam temp : list) {
                    temp.setCompanyRegId(mainId);
                }
                long start = System.currentTimeMillis();
                companyTeamService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-单位分组-end----------------------------------------------*/

    /**
     * 根据ID获取CompanyTeam
     */
    @AutoLog(value = "单位预约-根据ID获取CompanyTeam")
    @ApiOperation(value = "单位预约-根据ID获取CompanyTeam", notes = "单位预约-根据ID获取CompanyTeam")
    @GetMapping(value = "/getCompanyTeam")
    public Result<?> getCompanyTeam(@RequestParam(name = "id") String id) {
        CompanyTeam team = companyTeamService.getById(id);
        return Result.OK(team);
    }

    /**
     * duplicateCheck
     */
    @AutoLog(value = "单位预约-分组重复校验")
    @ApiOperation(value = "单位预约-分组重复校验", notes = "单位预约-分组重复校验")
    @GetMapping(value = "/duplicateCheck")
    public Result<?> duplicateCheck(@RequestParam(name = "id", required = false) String id, @RequestParam(name = "companyId", required = true) String companyId, @RequestParam(name = "teamName", required = true) String teamName) {
        boolean duplicate = companyRegService.teamDuplicateCheck(id, companyId, teamName);
        return duplicate ? Result.error("分组名称重复", false) : Result.OK(true);
    }

    /**
     * 保存单位分组的体检组合
     */
    @AutoLog(value = "单位分组-保存单位分组的体检组合")
    @ApiOperation(value = "单位分组-保存单位分组的体检组合", notes = "单位分组-保存单位分组的体检组合")
    @PostMapping(value = "/saveItemGroupOfTeam")
    public Result<?> saveItemGroupOfTeam(@RequestBody JSONObject info) {
        String teamId = info.getString("teamId");
        List<CompanyTeamItemGroup> itemGroupList = info.getJSONArray("groupList").toJavaList(CompanyTeamItemGroup.class);
        companyRegService.saveItemGroupOfTeam(teamId, itemGroupList);
        return Result.OK("操作成功!");
    }

    /**
     * 保存单位分组的体检组合（包含附属项目和赠送项目处理）
     */
    @AutoLog(value = "单位分组-保存单位分组的体检组合（含附属赠送）")
    @ApiOperation(value = "单位分组-保存单位分组的体检组合（含附属赠送）", notes = "单位分组-保存单位分组的体检组合（含附属赠送）")
    @PostMapping(value = "/saveItemGroupOfTeamWithRelations")
    public Result<?> saveItemGroupOfTeamWithRelations(@RequestBody JSONObject info) {
        String teamId = info.getString("teamId");
        List<CompanyTeamItemGroup> itemGroupList = info.getJSONArray("groupList").toJavaList(CompanyTeamItemGroup.class);
        Boolean skipGiftAndAttach = info.getBoolean("skipGiftAndAttach");
        if (skipGiftAndAttach == null) {
            skipGiftAndAttach = false; // 默认处理附属和赠送项目
        }
        companyRegService.saveItemGroupOfTeam(teamId, itemGroupList, skipGiftAndAttach);
        return Result.OK("操作成功!");
    }

    /**
     * 根据分组ID获取单位分组的体检组合（包含关系数据）
     */
    @AutoLog(value = "单位分组-根据分组ID获取单位分组的体检组合")
    @ApiOperation(value = "单位分组-根据分组ID获取单位分组的体检组合", notes = "单位分组-根据分组ID获取单位分组的体检组合")
    @GetMapping(value = "/getItemGroupOfTeam")
    public Result<?> getItemGroupOfTeam(@RequestParam(name = "teamId", required = true) String teamId) {
        try {
            // 1. 获取基础的团体项目列表
            List<CompanyTeamItemGroup> teamItemGroups = companyRegService.getItemGroupOfTeam(teamId);

            if (CollectionUtils.isEmpty(teamItemGroups)) {
                return Result.OK(Collections.emptyList());
            }

            // 2. 提取项目ID列表
            List<String> itemGroupIds = teamItemGroups.stream()
                .map(CompanyTeamItemGroup::getItemGroupId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

            // 3. 批量获取项目关系信息
            Map<String, GroupRelationVO> relationMap = itemGroupRelationService.batchGetRelationsByMainIds(itemGroupIds);

            // 4. 为每个项目添加依赖关系信息
            for (CompanyTeamItemGroup teamItem : teamItemGroups) {
                GroupRelationVO relation = relationMap.get(teamItem.getItemGroupId());
                if (relation != null) {
                    // 转换为Object列表
                    teamItem.setDependentGroups(new ArrayList<>(relation.getDependentGroups()));
                    teamItem.setAttachGroups(new ArrayList<>(relation.getAttachGroups()));
                    teamItem.setGiftGroups(new ArrayList<>(relation.getGiftGroups()));
                    teamItem.setExclusiveGroups(relation.getExclusiveGroups());
                }
            }

            return Result.OK(teamItemGroups);

        } catch (Exception e) {
            log.error("获取团体分组项目列表失败", e);
            // 降级到原有逻辑
            return Result.OK(companyRegService.getItemGroupOfTeam(teamId));
        }
    }

    /**
     * 根据分组ID获取分组详情
     */
    @AutoLog(value = "单位分组-根据ID获取详情")
    @ApiOperation(value = "单位分组-根据ID获取详情", notes = "单位分组-根据ID获取详情")
    @GetMapping(value = "/getCompanyReg")
    public Result<?> getById(@RequestParam(name = "regId", required = true) String regId) {
        return Result.OK(companyRegService.getById(regId));
    }


    /**
     * 根据分组ID获取分组详情
     */
    @AutoLog(value = "单位分组-根据分组ID获取分组详情")
    @ApiOperation(value = "单位分组-根据分组ID获取分组详情", notes = "单位分组-根据分组ID获取分组详情")
    @GetMapping(value = "/getCompanyTeamDetail")
    public Result<?> getCompanyTeamDetail(@RequestParam(name = "teamId", required = true) String teamId) {
        return Result.OK(companyRegService.getCompanyTeam(teamId));
    }

    /**
     * 根据ID获取单位预约详情
     */
    @AutoLog(value = "单位分组-根据ID获取单位预约详情")
    @ApiOperation(value = "单位分组-根据ID获取单位预约详情", notes = "单位分组-根据ID获取单位预约详情")
    @GetMapping(value = "/getCompanyRegDetail")
    public Result<?> getCompanyRegDetail(@RequestParam(name = "regId", required = true) String regId) {
        return Result.OK(companyRegService.getCompanyRegDetail(regId));
    }

    /**
     * 批量删除单位分组的体检组合
     */
    @AutoLog(value = "单位分组-批量删除单位分组的体检组合")
    @ApiOperation(value = "单位分组-批量删除单位分组的体检组合", notes = "单位分组-批量删除单位分组的体检组合")
    @DeleteMapping(value = "/deleteBatchItemGroupOfTeam")
    public Result<?> deleteBatchItemGroup(@RequestBody JSONObject info) {
        String teamId = info.getString("teamId");
        String companyRegId = info.getString("companyRegId");
        JSONArray ids = info.getJSONArray("ids");
        companyTeamService.deleteBatchItemGroup(companyRegId, teamId, ids.toJavaList(String.class));
        return Result.OK("操作成功!");
    }

    /**
     * 通知企业端
     */
    @AutoLog(value = "单位预约-通知企业端")
    @ApiOperation(value = "单位预约-通知企业端", notes = "单位预约-通知企业端")
    @PostMapping(value = "/notifyEnterprise")
    public Result<?> notifyEnterprise(@RequestParam(name = "id", required = true) String id) {
        try {
            // 1. 获取单位预约信息
            CompanyReg companyReg = companyRegService.getById(id);
            if (companyReg == null) {
                return Result.error("单位预约记录不存在!");
            }

            // 2. 检查数据来源是否为企业端
            if (!"企业端".equals(companyReg.getSource())) {
                return Result.error("只能通知来源为企业端的预约记录!");
            }

            // 3. 查询预约下的所有customerReg记录
            LambdaQueryWrapper<CustomerReg> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CustomerReg::getCompanyRegId, id);
            List<CustomerReg> customerRegList = customerRegService.list(queryWrapper);

            // 4. 构建BatchResultVO消息体
            BatchResultVO<CustomerReg> batchResult = new BatchResultVO<>();
            batchResult.setRegId(id);
            batchResult.setTotal(customerRegList.size());
            batchResult.setSuccessCount(customerRegList.size());
            batchResult.setFailureCount(0);
            batchResult.setSuccessList(customerRegList);
            batchResult.setFailureList(new ArrayList<>());
            batchResult.setMessage("单位预约数据通知");
            batchResult.setIsAsync(false);

            // 5. 调用企业通知服务
            enterpriseNotificationService.notifyBatchProgress(id, batchResult, null);

            return Result.OK("通知企业端成功!");

        } catch (Exception e) {
            log.error("通知企业端失败", e);
            return Result.error("通知企业端失败: " + e.getMessage());
        }
    }





}
