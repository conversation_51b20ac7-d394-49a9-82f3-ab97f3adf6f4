package org.jeecg.modules.reg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;
import org.jeecg.modules.reg.entity.FieldDisplayConfig;

import java.util.List;

/**
 * 字段显示配置Mapper
 */
@Mapper
public interface FieldDisplayConfigMapper extends BaseMapper<FieldDisplayConfig> {

    /**
     * 根据配置ID查询字段列表
     */
    @Select("SELECT * FROM field_display_config WHERE config_id = #{configId} ORDER BY sort_order ASC, field_key ASC")
    List<FieldDisplayConfig> selectByConfigId(@Param("configId") String configId);

    /**
     * 批量删除字段配置
     */
    @Delete("DELETE FROM field_display_config WHERE config_id = #{configId}")
    int deleteByConfigId(@Param("configId") String configId);

    /**
     * 批量插入字段配置
     */
    @Insert("<script>" +
            "INSERT INTO field_display_config (id, config_id, field_key, field_name, is_visible, display_location, group_name, sort_order, is_required, field_description) VALUES " +
            "<foreach collection='fields' item='field' separator=','>" +
            "(#{field.id}, #{field.configId}, #{field.fieldKey}, #{field.fieldName}, #{field.isVisible}, #{field.displayLocation}, #{field.groupName}, #{field.sortOrder}, #{field.isRequired}, #{field.fieldDescription})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("fields") List<FieldDisplayConfig> fields);
}
