package org.jeecg.modules.reg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 表单显示配置
 */
@Data
@TableName("form_display_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FormDisplayConfig", description = "表单显示配置")
public class FormDisplayConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 配置名称
     */
    @ApiModelProperty(value = "配置名称")
    private String configName;

    /**
     * 体检中心ID
     */
    @ApiModelProperty(value = "体检中心ID")
    private String centerId;

    /**
     * 体检中心名称
     */
    @ApiModelProperty(value = "体检中心名称")
    private String centerName;

    /**
     * 表单类型
     */
    @ApiModelProperty(value = "表单类型")
    private String formType;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 删除标志
     */
    @ApiModelProperty(value = "删除标志：0正常，1删除")
    private Boolean delFlag;

    /**
     * 字段配置列表
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "字段配置列表")
    private List<FieldDisplayConfig> fields;
}