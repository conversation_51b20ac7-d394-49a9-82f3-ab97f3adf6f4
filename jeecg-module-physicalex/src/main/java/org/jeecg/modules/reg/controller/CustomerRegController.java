package org.jeecg.modules.reg.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.GBKChecker;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.fee.bo.PaymentAnalysis;
import org.jeecg.modules.fee.entity.TeamCustomerLimitAmount;
import org.jeecg.modules.fee.mapper.TeamCustomerLimitAmountMapper;
import org.jeecg.modules.fee.service.ICustomerRegBillService;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.jeecg.modules.fee.service.ILimitOperationRecordService;
import org.jeecg.modules.reg.bo.DepartGroupTree;
import org.jeecg.modules.reg.bo.GuidanceSheet;
import org.jeecg.modules.reg.bo.RegTemplate;
import org.jeecg.modules.reg.bo.StatBean;
import org.jeecg.modules.reg.dto.AddItemGroupWithCheckPartsRequest;
import org.jeecg.modules.reg.dto.DependentItemResultDTO;
import org.jeecg.modules.reg.entity.*;
import org.jeecg.modules.reg.service.*;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.jeecg.modules.basicinfo.entity.GroupRelationVO;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.basicinfo.entity.RelationItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 客户登记
 * @Author: jeecg-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Api(tags = "客户登记")
@RestController
@RequestMapping("/reg/customerReg")
@Slf4j
public class CustomerRegController extends JeecgController<CustomerReg, ICustomerRegService> {
    @Autowired
    private ICustomerRegService customerRegService;
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private IFeePayRecordService feePayRecordService;
    @Autowired
    private ICustomerRegBillService billService;
    @Autowired
    private ICustomerRegBillService customerRegBillService;
    @Autowired
    private TeamCustomerLimitAmountMapper teamCustomerLimitAmountMapper;
    @Autowired
    private ICompanyRegService companyRegService;
    @Autowired
    private IItemGroupRelationService itemGroupRelationService;


    /**
     * 更新登记记录的报告ID
     */
    @ApiOperation(value = "客户登记-更新登记记录的报告ID", notes = "客户登记-更新登记记录的报告ID")
    @GetMapping(value = "/updateReportId")
    public Result<?> updateReportId(String regId, String templateId) {
        try {
            customerRegService.updateCustomerReportTemplate(regId, templateId);
            return Result.OK("操作成功！");
        } catch (Exception e) {
            log.error("操作失败", e);
            return Result.error("操作失败！详细原因：" + e.getMessage());
        }
    }


    /**
     * 客户登记-获取过去n年的登记记录
     */
    @ApiOperation(value = "客户登记-获取过去n年的登记记录", notes = "客户登记-获取过去n年的登记记录")
    @GetMapping(value = "/getLastNYearsReg")
    public Result<?> getLastNYearsReg(String regId, int years) {
        List<CustomerReg> list = customerRegService.getLastNYearsReg(regId, years);
        return Result.OK(list);
    }

    /**
     * 客户登记-获取过去n年的登记记录
     */
    @ApiOperation(value = "客户登记-根据档案Id获取过去n年的登记记录", notes = "客户登记-根据档案Id获取过去n年的登记记录")
    @GetMapping(value = "/getLastNYearsRegByIdcard")
    public Result<?> getLastNYearsRegByIdcard(String idCard, int years) {
        List<CustomerReg> list = customerRegService.getLastNYearsRegByIdcard(idCard, years);
        return Result.OK(list);
    }

    /**
     * 客户登记-获取过去n年的登记记录
     */
    @ApiOperation(value = "客户登记-根据档案Id获取过去n年的登记记录", notes = "客户登记-根据档案Id获取过去n年的登记记录")
    @GetMapping(value = "/getLastNYearsRegByCustomer")
    public Result<?> getLastNYearsRegByCustomer(String customerId, String years) {
        Integer year = null;
        try {
            year = Integer.parseInt(years);
        } catch (Exception e) {
            year = 1;
        }

        List<CustomerReg> list = customerRegService.getLastNYearsRegByCustomer(customerId, null, year);
        return Result.OK(list);
    }

    /**
     * 客户登记-获取过去n年的登记记录
     */
    @ApiOperation(value = "客户登记-根据档案Id获取过去n年的登记记录", notes = "客户登记-根据档案Id获取过去n年的登记记录")
    @GetMapping(value = "/getLastNYearsLiteRegByCustomer")
    public Result<?> getLastNYearsLiteRegByCustomer(String customerId, String years) {
        Integer year = null;
        try {
            year = Integer.parseInt(years);
        } catch (Exception e) {
            year = 1;
        }

        List<CustomerReg> list = customerRegService.getLastNYearsLiteRegByCustomer(customerId, null, year);
        return Result.OK(list);
    }


    /**
     * 客户登记-处理加项。为加项打印申请单、自动处理单位付款部分金额
     */
    @ApiOperation(value = "客户登记-处理加项", notes = "客户登记-处理加项")
    @GetMapping(value = "/handleAddItem")
    public Result<?> handleAddItem(String id, HttpServletRequest request) {
        try {
            PaymentAnalysis paymentAnalysis = customerRegService.handleAddItem(id, request.getRemoteAddr());
            String msg = "操作成功！";
            if (paymentAnalysis != null) {
                BigDecimal personAmount = paymentAnalysis.getPersonAmount() != null ? paymentAnalysis.getPersonAmount() : BigDecimal.ZERO;
                msg += personAmount.compareTo(BigDecimal.ZERO) == 0 ? "不用支付，可直接体检！" : "需要支付：" + personAmount + "元";
            }
            return Result.OK(msg);
        } catch (Exception e) {
            log.error("操作失败", e);
            return Result.error("操作失败！详细原因：" + e.getMessage());
        }
    }

    /**
     * 客户登记-指定总检医生
     */
    @ApiOperation(value = "客户登记-指定总检医生", notes = "客户登记-指定总检医生")
    @PostMapping(value = "/assignSummaryDoctorBatch")
    public Result<?> assignSummaryDoctorBatch(@RequestBody JSONObject data) {
        try {
            String doctorId = data.getString("doctorId");
            String doctorName = data.getString("doctorName");
            JSONArray ids = data.getJSONArray("ids");
            List<String> idList = ids.toJavaList(String.class);
            customerRegService.assignSummaryDoctorBatch(idList, doctorId, doctorName);
            return Result.OK("操作成功!");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据身份证号和单位分组ID获取已经消费的总金额
     */
    @ApiOperation(value = "客户登记-根据身份证号和单位分组ID获取已经消费的总金额", notes = "客户登记-根据身份证号和单位分组ID获取已经消费的总金额")
    @GetMapping(value = "/getSpendAmount")
    public Result<?> getSpendAmount(String idCard, String teamId) {
        try {
            BigDecimal spendAmount = billService.getSpendAmountOfTeamByFeePayRecord(idCard, teamId);
            return Result.OK(spendAmount);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    /**
     * 根据身份证号和单位分组ID获取已经消费的总金额
     */
    @ApiOperation(value = "客户登记-根据身份证号和单位分组ID获取已经消费的总金额", notes = "客户登记-根据身份证号和单位分组ID获取已经消费的总金额")
    @GetMapping(value = "/getRemainingAmount")
    public Result<?> getRemainingAmount(String customerId, String teamId, String originCustomerLimitAmountId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (StringUtils.isBlank(originCustomerLimitAmountId)) {
                TeamCustomerLimitAmount   customerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(teamId,customerId,null);
                resultMap.put("selfLimitAmount", Objects.nonNull(customerLimitAmount)?customerLimitAmount.getAmount():BigDecimal.ZERO);
                resultMap.put("shareLimitAmount", null);
                resultMap.put("totalLimitAmount", Objects.nonNull(customerLimitAmount)?customerLimitAmount.getAmount():BigDecimal.ZERO);
                return Result.OK(resultMap);
            }else{
                //查询自己有无可用额度
                BigDecimal totalLimitAmount = BigDecimal.ZERO;
                TeamCustomerLimitAmount   selfLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(teamId,customerId,null);
                TeamCustomerLimitAmount  shareLimitAmount = teamCustomerLimitAmountMapper.selectById(originCustomerLimitAmountId);
                if(Objects.nonNull(selfLimitAmount)){
                    resultMap.put("selfLimitAmount", selfLimitAmount.getAmount());
                    totalLimitAmount=totalLimitAmount.add(selfLimitAmount.getAmount());
                }else{
                    resultMap.put("selfLimitAmount", null);
                }
                if (Objects.nonNull(shareLimitAmount)){
                    resultMap.put("shareLimitAmount", shareLimitAmount.getAmount());
                    totalLimitAmount=totalLimitAmount.add(shareLimitAmount.getAmount());
                }else{
                    resultMap.put("shareLimitAmount", null);
                }
                resultMap.put("totalLimitAmount", totalLimitAmount);
                return Result.OK(resultMap);
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据身份证号获取团检分组信息
     *
     * @return
     */
    @ApiOperation(value = "客户登记-根据身份证号获取团检分组信息", notes = "客户登记-根据身份证号获取团检分组信息")
    @GetMapping(value = "/getTeamInfoByIdCard")
    public Result<?> getTeamInfoByIdCard(String idCard) {
        try {
            CompanyTeam team = customerRegService.getTeamByIdcrad(idCard);
            return Result.OK(team);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    /**
     * 根据身份证号获取团检和限额信息
     *
     * @return
     */
    @ApiOperation(value = "客户登记-根据身份证号获取团检和限额信息", notes = "客户登记-根据身份证号获取团检和限额信息")
    @GetMapping(value = "/getTeamAndLimitInfoByIdCard")
    public Result<?> getTeamAndLimitInfoByIdCard(String idCard) {
        try {
            CompanyTeam team = customerRegService.getTeamAndLimitInfoByIdCard(idCard);
            return Result.OK(team);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    @ApiOperation(value = "客户登记-体检统计", notes = "客户登记-体检统计")
    @GetMapping(value = "/stat")
    public Result<?> stat() {
        Map<String, StatBean> statBeanMap = customerRegService.statReg();
        return Result.OK(statBeanMap);
    }

    /*  */

    /**
     * 分页列表���询
     *
     * @param customerReg
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "客户登记-分页列表查询")
    @ApiOperation(value = "客户登记-分页列表查询", notes = "客户登记-分页列表查询")
    @GetMapping(value = "/commonList")
    public Result<IPage<CustomerReg>> queryPageList(CustomerReg customerReg, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CustomerReg> queryWrapper = QueryGenerator.initQueryWrapper(customerReg, req.getParameterMap());
        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);
        queryWrapper.orderByDesc("create_time");
        IPage<CustomerReg> pageList = customerRegService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "客户登记-分页列表查询")
    @ApiOperation(value = "客户登记-分页列表查询", notes = "客户登记-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerReg>> queryPageList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);

        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String regTimeStart = StringUtils.trimToNull(req.getParameter("regTimeStart"));
        String regTimeEnd = StringUtils.trimToNull(req.getParameter("regTimeEnd"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String status = StringUtils.trimToNull(req.getParameter("status"));
        String retrieveStatus = StringUtils.trimToNull(req.getParameter("retrieveStatus"));
        String emplyee = StringUtils.trimToNull(req.getParameter("creatorBy"));
        String paymentState = StringUtils.trimToNull(req.getParameter("paymentState"));
        String dateType = StringUtils.trimToNull(req.getParameter("dateType"));
        dateType = StringUtils.isBlank(dateType) ? "预约" : dateType;
        String customerId = StringUtils.trimToNull(req.getParameter("customerId"));

        //按照体检号精准搜索时，将其他属性���空
        if (StringUtils.isNotBlank(examNo)|| StringUtils.isNotBlank(idCard)|| StringUtils.isNotBlank(name)) {
            regTimeStart = null;
            regTimeEnd = null;
            companyRegId = null;
            teamId = null;
            status = null;
            emplyee = null;
            paymentState = null;
            dateType = null;
        }

        String companyNotifyFlag = StringUtils.trimToNull(req.getParameter("companyNotifyFlag"));
        customerRegService.pageCustomerReg(page, idCard, examNo, dateType, regTimeStart, regTimeEnd, companyRegId, teamId, name, status, retrieveStatus, emplyee, paymentState, companyNotifyFlag,customerId);

        return Result.OK(page);
    }
    @ApiOperation(value = "客户登记-分页列表查询", notes = "客户登记-分页列表查询")
    @GetMapping(value = "/list4Occu")
    public Result<IPage<?>> list4Occu(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);
        Page<CompanyReg> companyPage = new Page<CompanyReg>(pageNo, pageSize);

        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String regTimeStart = StringUtils.trimToNull(req.getParameter("regTimeStart"));
        String regTimeEnd = StringUtils.trimToNull(req.getParameter("regTimeEnd"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String status = StringUtils.trimToNull(req.getParameter("status"));
        String retrieveStatus = StringUtils.trimToNull(req.getParameter("retrieveStatus"));
        String emplyee = StringUtils.trimToNull(req.getParameter("creatorBy"));
        String paymentState = StringUtils.trimToNull(req.getParameter("paymentState"));
        String dateType = StringUtils.trimToNull(req.getParameter("dateType"));
        dateType = StringUtils.isBlank(dateType) ? "预约" : dateType;
        String customerId = StringUtils.trimToNull(req.getParameter("customerId"));
        String riskFactor = StringUtils.trimToNull(req.getParameter("riskFactor"));
        String examCategory = StringUtils.trimToNull(req.getParameter("examCategory"));
        String occuReportResultStatus = StringUtils.trimToNull(req.getParameter("occuReportResultStatus"));
        String occuReportUploadTimeStart = StringUtils.trimToNull(req.getParameter("occuReportUploadTimeStart"));
        String occuReportUploadTimeEnd = StringUtils.trimToNull(req.getParameter("occuReportUploadTimeEnd"));
        String summaryStatus= StringUtils.trimToNull(req.getParameter("summaryStatus"));
        String type= StringUtils.trimToNull(req.getParameter("type"));

        //按照体检号精准搜索时，将其他属性置空
        if (StringUtils.isNotBlank(examNo)|| StringUtils.isNotBlank(idCard)|| StringUtils.isNotBlank(name)) {
            regTimeStart = null;
            regTimeEnd = null;
            companyRegId = null;
            teamId = null;
            status = null;
            emplyee = null;
            paymentState = null;
            dateType = null;
        }

        String companyNotifyFlag = StringUtils.trimToNull(req.getParameter("companyNotifyFlag"));
        if(StringUtils.equals(type,"personal")) {
            customerRegService.pageCustomerReg4Occu(page, idCard, examNo, dateType, regTimeStart, regTimeEnd, companyRegId, teamId, name, status, retrieveStatus, emplyee, paymentState, companyNotifyFlag, customerId, riskFactor, examCategory, occuReportResultStatus, occuReportUploadTimeStart, occuReportUploadTimeEnd, summaryStatus);
            return Result.OK(page);
        }else{
            companyRegService.pageCompanyReg4Occu(companyPage, companyRegId, teamId, examCategory, occuReportResultStatus, occuReportUploadTimeStart, occuReportUploadTimeEnd, summaryStatus);
            return Result.OK(companyPage);
        }
    }
    @ApiOperation(value = "客户登记-分页列表查询", notes = "客户登记-分页列表查询")
    @GetMapping(value = "/list4CompanyReg")
    public Result<IPage<CustomerReg>> list4CompanyReg(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);

        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String regTimeStart = StringUtils.trimToNull(req.getParameter("regTimeStart"));
        String regTimeEnd = StringUtils.trimToNull(req.getParameter("regTimeEnd"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String status = StringUtils.trimToNull(req.getParameter("status"));
        String retrieveStatus = StringUtils.trimToNull(req.getParameter("retrieveStatus"));
        String emplyee = StringUtils.trimToNull(req.getParameter("creatorBy"));
        String paymentState = StringUtils.trimToNull(req.getParameter("paymentState"));
        String dateType = StringUtils.trimToNull(req.getParameter("dateType"));
        dateType = StringUtils.isBlank(dateType) ? "预约" : dateType;
        String customerId = StringUtils.trimToNull(req.getParameter("customerId"));

        String companyNotifyFlag = StringUtils.trimToNull(req.getParameter("companyNotifyFlag"));
        customerRegService.pageCustomerReg(page, idCard, examNo, dateType, regTimeStart, regTimeEnd, companyRegId, teamId, name, status, retrieveStatus, emplyee, paymentState, companyNotifyFlag,customerId);

        return Result.OK(page);
    }

    /**
     * 批量将符合条件的记录进行登记
     *
     * @param req
     * @return
     */
    @AutoLog(value = "客户登记-批量将符合条件的记录进行登记")
    @ApiOperation(value = "客户登记-批量将符合条件的记录进行登记", notes = "客户登记-批量将符合条件的记录进行登记")
    @GetMapping(value = "/regBatchByCondition")
    public Result<?> regBatchByCondition(HttpServletRequest req) {

        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String regTimeStart = StringUtils.trimToNull(req.getParameter("regTimeStart"));
        String regTimeEnd = StringUtils.trimToNull(req.getParameter("regTimeEnd"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String status = StringUtils.trimToNull(req.getParameter("status"));
        String retrieveStatus = StringUtils.trimToNull(req.getParameter("retrieveStatus"));
        String emplyee = StringUtils.trimToNull(req.getParameter("creatorBy"));
        String paymentState = StringUtils.trimToNull(req.getParameter("paymentState"));

        List<CustomerReg> regList = customerRegService.listCustomerReg(idCard, examNo, regTimeStart, regTimeEnd, companyRegId, teamId, name, status, retrieveStatus, emplyee, paymentState);

        try {
            BatchResult<CustomerReg> batchResult = customerRegService.regBatch(regList, req.getRemoteAddr());
            return Result.OK(batchResult);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取符合条件的记录
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "客户登记-列表查询", notes = "客户登记-列表查询")
    @GetMapping(value = "/listByCondition")
    public Result<?> queryList(HttpServletRequest req) {
        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String regTimeStart = StringUtils.trimToNull(req.getParameter("regTimeStart"));
        String regTimeEnd = StringUtils.trimToNull(req.getParameter("regTimeEnd"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String status = StringUtils.trimToNull(req.getParameter("status"));
        String retrieveStatus = StringUtils.trimToNull(req.getParameter("retrieveStatus"));
        String emplyee = StringUtils.trimToNull(req.getParameter("creatorBy"));
        String paymentState = StringUtils.trimToNull(req.getParameter("paymentState"));
        List<CustomerReg> list = customerRegService.listCustomerReg(idCard, examNo, regTimeStart, regTimeEnd, companyRegId, teamId, name, status, retrieveStatus, emplyee, paymentState);

        return Result.OK(list);
    }

    /**
     * 获取给定id的记录
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "客户登记-获取给定id的记录", notes = "客户登记-获取给定id的记录")
    @GetMapping(value = "/getByIds")
    public Result<?> getByIds(HttpServletRequest req) {
        String ids = StringUtils.trimToNull(req.getParameter("ids"));
        List<CustomerReg> list = customerRegService.listByIds(Arrays.asList(StringUtils.split(ids, ",")));
        return Result.OK(list);
    }

    /**
     * 批量将符合条件的费用发��到HIS
     *
     * @param req
     * @return
     */
    @AutoLog(value = "客户登记-批量将符合条件的费用发送到HIS")
    @ApiOperation(value = "客户登记-批量将符合条件的费用发送到HIS", notes = "客户登记-批量将符合条件的费用发送到HIS")
    @GetMapping(value = "/sendFee2His")
    public Result<?> sendFee2His(HttpServletRequest req) {

        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String regTimeStart = StringUtils.trimToNull(req.getParameter("regTimeStart"));
        String regTimeEnd = StringUtils.trimToNull(req.getParameter("regTimeEnd"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String status = StringUtils.trimToNull(req.getParameter("status"));
        String retrieveStatus = StringUtils.trimToNull(req.getParameter("retrieveStatus"));
        String emplyee = StringUtils.trimToNull(req.getParameter("creatorBy"));
        String paymentState = StringUtils.trimToNull(req.getParameter("paymentState"));

        List<CustomerReg> regList = customerRegService.listCustomerReg(idCard, examNo, regTimeStart, regTimeEnd, companyRegId, teamId, name, status, retrieveStatus, emplyee, paymentState);

        try {
            BatchResult<CustomerReg> batchResult = feePayRecordService.sendFee2HisBatch(regList, req.getRemoteAddr());
            return Result.OK(batchResult);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量将符合条件的费用发送到HIS
     *
     * @param req
     * @return
     */
    @AutoLog(value = "客户登记-批量将给定的体检登记费用发送到HIS")
    @ApiOperation(value = "客户登记-批量将给定的体检登记费用发送到HIS", notes = "客户登记-批量将给定的体检登记费用发送到HIS")
    @PostMapping(value = "/sendFee2HisByIds")
    public Result<?> sendFee2HisByIds(@RequestBody JSONObject data, HttpServletRequest req) {
        List<String> idList = data.getJSONArray("ids").toJavaList(String.class);
        List<CustomerReg> regList = customerRegService.listByIds(idList);

        try {
            BatchResult<CustomerReg> batchResult = customerRegBillService.sendFee2HisBatch(regList, req.getRemoteAddr());
            return Result.OK(batchResult);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param customerReg
     * @return
     */
    @AutoLog(value = "客户登记-添加")
    @ApiOperation(value = "客户登记-添加", notes = "客户登��-添加")
    @RequiresPermissions("reg:customer_reg:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody CustomerReg customerReg) {
        try {
            if (!GBKChecker.isAllCharactersGBK(customerReg.getName())) {
                return Result.error("姓名中包含非GBK字符，请检查！");
            }
            customerRegService.addCustomerReg(customerReg);
            return Result.OK("添加成功！", customerReg);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param customerReg
     * @return
     */
    @AutoLog(value = "客户登记-��辑")
    @ApiOperation(value = "客户登记-编辑", notes = "客户登记-编辑")
    @RequiresPermissions("reg:customer_reg:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody CustomerReg customerReg) {
        try { //long beforeMemory = beforeUsage.getUsed();
        if (!GBKChecker.isAllGBK(customerReg.getName())) {
            return Result.error("姓名中包含非GBK字符，请检查！");
        }
        customerRegService.updateCustomerReg(customerReg);
        return Result.OK("编辑成功!", customerReg);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 登记
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-登记")
    @ApiOperation(value = "客户登记-登记", notes = "客户登记-登记")
    @RequiresPermissions("reg:customer_reg:reg")
    @PostMapping(value = "/regBatch")
    public Result<?> regBatch(@RequestBody JSONObject info, HttpServletRequest request) {

        JSONArray jsonArray = info.getJSONArray("ids");
        List<String> idList = jsonArray.toJavaList(String.class);
        BatchResult<CustomerReg> batchResult = null;
        try {
            batchResult = customerRegService.regBatchByIds(idList, request.getRemoteAddr());
        } catch (Exception e) {
            log.error("登记失败", e);
            return Result.error("登记失败！详细原因：" + e.getMessage());
        }

        return Result.OK(batchResult);
    }

    /**
     * 登记
     *
     * @param id
     * @return
     */
    @AutoLog(value = "客户登记-登记")
    @ApiOperation(value = "客户登记-登记", notes = "客户登记-登记")
    @RequiresPermissions("reg:customer_reg:reg")
    @GetMapping(value = "/reg")
    public Result<?> reg(String id, HttpServletRequest request) {
        try {
            PaymentAnalysis paymentAnalysis = customerRegService.regById(id, request.getRemoteAddr());
            String msg = "登记成功！";
            if (paymentAnalysis != null) {
                BigDecimal personAmount = paymentAnalysis.getPersonAmount() != null ? paymentAnalysis.getPersonAmount() : BigDecimal.ZERO;
                msg += personAmount.compareTo(BigDecimal.ZERO) == 0 ? "不用支付，可直接体检！" : "需要支付：" + personAmount + "元";
            }
            return Result.OK(msg);
        } catch (Exception e) {
            log.error("登记失败", e);
            return Result.error("登记失败！详细原因：" + e.getMessage());
        }
    }

    /**
     * 取消登记
     *
     * @return
     */
    @AutoLog(value = "客户登记-取消登记")
    @ApiOperation(value = "客户登记-取消登记", notes = "客户登记-取消登记")
    @RequiresPermissions("reg:customer_reg:reg")
    @GetMapping(value = "/unRegCustomerReg")
    public Result<?> unRegCustomerReg(String id) {

        CustomerReg customerReg = customerRegService.getById(id);
        customerReg.setStatus(ExConstants.REG_STATUS_WAIT);
        customerReg.setRegTime(null);
        customerRegService.updateById(customerReg);
        return Result.OK("操作成功！");
    }
    /**
     * 发送心理测评短信
     *
     * @param customerRegId
     * @return
     */
    @AutoLog(value = "发送心理测评短信")
    @ApiOperation(value = "发送心理测评短信", notes = "发送心理测评短信")
    @GetMapping(value = "/sendPsyNotify")
    public Result<?> sendPsyNotify(String customerRegId, HttpServletRequest request) {
        try {
            customerRegService.sendPsyNotify(customerRegId);
            return Result.OK("发送成功！");
        } catch (Exception e) {
            log.error("心理测评短信发送失败", e);
            return Result.error("心理测评短信发送失败！详细原因：" + e.getMessage());
        }
    }
    /**
     * 更新指导单打印次数
     *
     * @param regId
     * @return
     */
    //@AutoLog(value = "客户登记-更新指导单打印次数")
    @ApiOperation(value = "客户登记-更新指导单打印次数", notes = "客户登记-更新指导单打印次数")
    @GetMapping(value = "/updateGuidancePrintTimes")
    public Result<?> updateGuidancePrintTimes(String regId) {
        customerRegService.updateGuidancePrintTimes(regId);
        return Result.OK();
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "客户登记-通过id删除")
    @ApiOperation(value = "客户登记-通过id删除", notes = "客户登记-通过id删除")
    @RequiresPermissions("reg:customer_reg:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerRegService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "客户登记-批量删除")
    @ApiOperation(value = "客户登记-批量删除", notes = "客户登记-批量删除")
    @RequiresPermissions("reg:customer_reg:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerRegService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "客户登记-通过id查询")
    @ApiOperation(value = "客户登记-通过id查询", notes = "客户登记-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerReg> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerReg customerReg = customerRegService.getById(id);
        if (customerReg == null) {
            return Result.error("未找到对应数据");
        }

        return Result.OK(customerReg);
    }

    /**
     * 通过id查询登记详情
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "客户登记-通过id查询")
    @ApiOperation(value = "客户登记-通过id查询登记详情", notes = "客户登记-通过id查询登记详情")
    @GetMapping(value = "/getCustomerRegDetail")
    public Result<CustomerReg> getCustomerRegDetail(@RequestParam(name = "id", required = true) String id) {
        CustomerReg customerReg = customerRegService.getById(id);
        if (customerReg == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerReg);
    }

    /**
     * 通过id查询登记详情
     **/
    @ApiOperation(value = "客户登记-为交表获取组合列表", notes = "客户登记-为交表获取组合列表")
    @GetMapping(value = "/getGroupList4Retrieve")
    public Result<?> getGroupList4Retrieve(@RequestParam(name = "customerRegId", required = true) String customerRegId) {
        List<DepartGroupTree> groupList = customerRegItemGroupService.listDepartGroupTree(customerRegId);
        return Result.OK(groupList);
    }

    /**
     * 交表
     **/
    @ApiOperation(value = "客户登记-交表", notes = "客户登记-交表")
    @PostMapping(value = "/retrieve")
    public Result<?> retrieve(@RequestBody JSONObject info) {
        String id = info.getString("customerRegId");
        String retrieveImg = info.getString("retrieveImg");
        try {
            customerRegService.retrieve(id, retrieveImg);
            return Result.OK("操作成功!");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 取消交表
     **/
    @ApiOperation(value = "客户登记-交表", notes = "客户登记-交表")
    @GetMapping(value = "/undoRetrieve")
    public Result<?> undoRetrieve(String id) {
        try {
            customerRegService.unRetrieve(id);
            return Result.OK("操作成功!");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过id查询登记详情
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "客户登记-通过id查询")
    @ApiOperation(value = "客户登记-获取导引单数据", notes = "客户登记-获取导引单数据")
    @GetMapping(value = "/getGuidanceSheet")
    public Result<GuidanceSheet> getGuidanceSheet(@RequestParam(name = "id", required = true) String id) {
        GuidanceSheet guidanceSheet = customerRegService.getGuidanceSheet(id);

        return Result.OK(guidanceSheet);
    }


    /**
     * 通过id查询（包含关系数据）
     *
     * @param regId
     * @return
     */
    //@AutoLog(value = "客户登记-通过id查询")
    @ApiOperation(value = "客户登记-根据登记ID获取体检项目组合列表", notes = "客户登记-根据登记ID获取体检项目组合列表")
    @GetMapping(value = "/getItemGroupByCustomerRegId")
    public Result<?> getItemGroupByCustomerRegId(@RequestParam(name = "regId", required = true) String regId) {
        try {
            // 1. 获取基础的项目列表
            List<CustomerRegItemGroup> customerRegItemGroupList = customerRegService.getItemGroupByCustomerRegId(regId);

            if (CollectionUtils.isEmpty(customerRegItemGroupList)) {
                return Result.OK(Collections.emptyList());
            }

            // 2. 提取项目ID列表
            List<String> itemGroupIds = customerRegItemGroupList.stream()
                .map(CustomerRegItemGroup::getItemGroupId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

            // 3. 批量获取项目关系信息
            Map<String, GroupRelationVO> relationMap = itemGroupRelationService.batchGetRelationsByMainIds(itemGroupIds);

            // 4. 为每个项目添加依赖关系信息
            for (CustomerRegItemGroup item : customerRegItemGroupList) {
                GroupRelationVO relation = relationMap.get(item.getItemGroupId());
                if (relation != null) {
                    // 转换 ItemGroupRelation 为 RelationItemVO
                    item.setDependentGroups(convertToRelationItemVOs(relation.getDependentGroups()));
                    item.setAttachGroups(convertToRelationItemVOs(relation.getAttachGroups()));
                    item.setGiftGroups(convertToRelationItemVOs(relation.getGiftGroups()));
                    item.setExclusiveGroups(relation.getExclusiveGroups());
                }
            }

            return Result.OK(customerRegItemGroupList);

        } catch (Exception e) {
            log.error("获取体检项目列表失败", e);
            // 降级到原有逻辑
            List<CustomerRegItemGroup> customerRegItemGroupList = customerRegService.getItemGroupByCustomerRegId(regId);
            return Result.OK(customerRegItemGroupList);
        }
    }

    /**
     * 新增：获取带完整依赖关系分析的项目列表
     */
    @ApiOperation(value = "客户登记-获取带依赖关系分析的项目列表", notes = "客户登记-获取带依赖关系分析的项目列表")
    @GetMapping(value = "/getItemGroupWithDependencyAnalysis")
    public Result<?> getItemGroupWithDependencyAnalysis(@RequestParam(name = "regId", required = true) String regId) {
        try {
            log.info("获取登记ID: {} 的完整依赖关系分析", regId);

            CustomerRegItemGroupAnalysisVO result = customerRegService.getItemGroupWithDependencyAnalysis(regId);

            log.info("返回 {} 个项目的完整分析数据", result.getItems().size());

            return Result.OK(result);
        } catch (Exception e) {
            log.error("获取项目依赖关系分析失败: {}", e.getMessage(), e);
            return Result.error("获取项目依赖关系分析失败: " + e.getMessage());
        }
    }

    /**
     * 添加组合
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-添加组合")
    @ApiOperation(value = "客户登记-添加组合", notes = "客户登记-添加组合")
    @PostMapping(value = "/addItemGroup")
    public Result<?> addItemGroup(@RequestBody JSONArray info) {

        List<CustomerRegItemGroup> list = info.toJavaList(CustomerRegItemGroup.class);
        try {
            customerRegService.addItemGroup(list);
            return Result.OK("添加成功!", list);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 添加组合（支持跳过赠送和附属项目处理）
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-添加组合（套餐专用）")
    @ApiOperation(value = "客户登记-添加组合（套餐专用）", notes = "客户登记-添加组合，跳过赠送和附属项目处理")
    @PostMapping(value = "/addItemGroupForSuit")
    public Result<?> addItemGroupForSuit(@RequestBody JSONArray info) {

        List<CustomerRegItemGroup> list = info.toJavaList(CustomerRegItemGroup.class);
        try {
            // 跳过赠送和附属项目处理
            customerRegService.addItemGroup(list, true);
            return Result.OK("添加成功!", list);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 智能添加组合（自动处理子项目）
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-智能添加组合")
    @ApiOperation(value = "客户登记-智能添加组合", notes = "客户登记-智能添加组合，自动处理依赖、附属、赠送项目")
    @PostMapping(value = "/addItemGroupWithAutoSubItems")
    public Result<?> addItemGroupWithAutoSubItems(@RequestBody JSONArray info) {

        List<CustomerRegItemGroup> list = info.toJavaList(CustomerRegItemGroup.class);
        try {
            customerRegService.addItemGroupWithAutoSubItems(list);
            return Result.OK("添加成功!", list);
        } catch (Exception e) {
            log.error("智能添加项目组合失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 智能添加组合（自动处理子项目，套餐专用）
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-智能添加组合（套餐专用）")
    @ApiOperation(value = "客户登记-智能添加组合（套餐专用）", notes = "客户登记-智能添加组合，自动处理依赖项目，跳过赠送和附属项目")
    @PostMapping(value = "/addItemGroupWithAutoSubItemsForSuit")
    public Result<?> addItemGroupWithAutoSubItemsForSuit(@RequestBody JSONArray info) {

        List<CustomerRegItemGroup> list = info.toJavaList(CustomerRegItemGroup.class);
        try {
            // 跳过赠送和附属项目处理，但仍处理依赖项目
            customerRegService.addItemGroupWithAutoSubItems(list, true);
            return Result.OK("添加成功!", list);
        } catch (Exception e) {
            log.error("智能添加项目组合（套餐专用）失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更换项目组合
     *
     * @param
     * @return
     */
    @AutoLog(value = "客户登记-更换项目组合")
    @ApiOperation(value = "客户登记-更换项目组合", notes = "客户登记-更换项目组合")
    @GetMapping(value = "/changeItemGroupByCompanyTeam")
    public Result<?> changeItemGroupByCompanyTeam(@RequestParam("customerRegId") String customerRegId, @RequestParam("companyTeamId") String companyTeamId) {
        try {
            customerRegService.changeItemGroupByCompanyTeam(customerRegId, companyTeamId);
            return Result.OK("更换成功!");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    /**
     * 删除组合
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-删除组合")
    @ApiOperation(value = "客户登记-删除组合", notes = "客户登记-删除组合")
    @PostMapping(value = "/removeItemGroup")
    public Result<?> removeItemGroup(@RequestBody JSONObject info) {
        String regId = info.getString("regId");
        JSONArray ids = info.getJSONArray("ids");
        List<String> idList = ids.toJavaList(String.class);
        try {
            List<String> successList = customerRegService.removeItemGroupBatch(regId, idList);
            return Result.OK("删除成功!", successList);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    /**
     * 对组合进行减项
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-对组合进行减项")
    @ApiOperation(value = "客户登记-对组合进行减项", notes = "客户登记-对组合进行减项")
    @PostMapping(value = "/minusItemGroup")
    public Result<?> minusItemGroup(@RequestBody JSONObject info) {
        String regId = info.getString("regId");
        JSONArray ids = info.getJSONArray("ids");
        List<String> idList = ids.toJavaList(String.class);
        try {
            List<String> successList = customerRegService.minusItemGroupBatch(regId, idList);
            return Result.OK("操���成功!", successList);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 对组合进行反减项
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-对组合进行反减项")
    @ApiOperation(value = "客户登记-对组合进行反减项", notes = "客户登记-对组合进行反减项")
    @PostMapping(value = "/undoMinusItemGroup")
    public Result<?> undoMinusItemGroup(@RequestBody JSONObject info) {
        String regId = info.getString("regId");
        JSONArray ids = info.getJSONArray("ids");
        List<String> idList = ids.toJavaList(String.class);
        try {
            List<String> successList = customerRegService.undoMinusItemGroupBatch(regId, idList);
            return Result.OK("操作成功!", successList);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据套餐设置组合
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-根据套餐设置组合")
    @ApiOperation(value = "客户登记-根据套餐设置组合", notes = "客户登记-根据套餐设置组合")
    @PostMapping(value = "/setItemGroupBySuit")
    public Result<?> setItemGroupBySuit(@RequestBody JSONObject info) {
        String regId = info.getString("regId");
        JSONArray list = info.getJSONArray("groupList");
        List<CustomerRegItemGroup> groupList = list.toJavaList(CustomerRegItemGroup.class);
        try {
            customerRegService.setItemGroupBySuit(regId, groupList);
            return Result.OK("操作成功!");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }


    /**
     * 对组合进行更新
     *
     * @param info
     * @return
     */
    @AutoLog(value = "客户登记-对组合进行更新")
    @ApiOperation(value = "客户登记-对组合进行更新", notes = "客户登记-对组合进行更新")
    @PostMapping(value = "/updateItemGroup")
    public Result<?> updateItemGroup(@RequestBody JSONObject info) {

        JSONArray ids = info.getJSONArray("data");
        List<CustomerRegItemGroup> list = ids.toJavaList(CustomerRegItemGroup.class);
        try {
            customerRegService.updateItemGroupBatch(list);
            return Result.OK("操作成功!");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
	 * 根据工种和岗位类型自动获取危害因素和必检项目
	 *
	 * @param worktypeId 工种ID
	 * @param jobStatus 岗位类别
	 * @return 危害因素和必检项目信息
	 */
	@AutoLog(value = "客户登记-根据工种和岗位类别获取危害因素和必检项目")
	@ApiOperation(value="客户登记-根据工种和岗位类别获取危害因素和必检项目", notes="客户登记-根据工种和岗位类别获取危害因素和必检项目")
	@GetMapping(value = "/getRegInfoByWorktype")
	public Result<JSONObject> getRegInfoByWorktype(
			@RequestParam(name="worktypeId",required=true) String worktypeId,
			@RequestParam(name="jobStatus",required=false) String jobStatus) {

		JSONObject result = new JSONObject();

		try {
			// 构建API调用URL，包含岗位类别参数
			String riskFactorUrl = "/occu/zyRiskFactor/getRiskFactorsByWorktypeAndJobStatus?worktypeId=" + worktypeId;
			if (StringUtils.isNotBlank(jobStatus)) {
				riskFactorUrl += "&jobStatus=" + jobStatus;
			}

			String itemGroupUrl = "/occu/zyRiskFactor/getItemGroupsByWorktype?worktypeId=" + worktypeId;
			if (StringUtils.isNotBlank(jobStatus)) {
				itemGroupUrl += "&jobStatus=" + jobStatus;
			}

			result.put("success", true);
			result.put("message", "请通过前端调用危害因素接口获取数据");
			result.put("riskFactorUrl", riskFactorUrl);
			result.put("itemGroupUrl", itemGroupUrl);
			result.put("hasJobStatus", StringUtils.isNotBlank(jobStatus));
			result.put("jobStatus", jobStatus);

		} catch (Exception e) {
			log.error("根据工种和岗位类别获取危害因素和必检项目失败", e);
			result.put("success", false);
			result.put("message", "获取数据失败：" + e.getMessage());
		}

		return Result.OK(result);
	}

    /**
     * 导出excel
     *
     * @param request
     * @param customerReg
     */
    @RequiresPermissions("reg:customer_reg:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerReg customerReg) {
        String templateOnly = request.getParameter("templateOnly");
        String companyRegId = request.getParameter("companyRegId");
        if (!oConvertUtils.isEmpty(templateOnly)) {
            request.setAttribute("description", customerRegService.getDictText4ExcelTemplate(companyRegId));
        }

        return super.exportXls(request, customerReg, CustomerReg.class, "客户名单");
    }


    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("reg:customer_reg:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {

        try {
            return customerRegService.importExcel(request, response);
        } catch (Exception e) {
            log.error("导入失败", e);
            return Result.error("导入失败");
        }
    }

    /**
     * 保存报告
     */
    @ApiOperation(value = "登记记录-保存报告", notes = "登记记录-保存报告")
    @PostMapping(value = "/verifyAndSaveReport")
    public Result<?> verifyAndSaveReport(@RequestBody JSONObject info) {
        try {
            String customerRegId = info.getString("customerRegId");
            String reportUrl = info.getString("pdfUrl");
            customerRegService.verifyAndSaveReport(customerRegId, reportUrl);
        } catch (Exception e) {
            log.error("档案表-保存报告异常", e);
            return Result.error("保存报告失败！");
        }
        return Result.OK("保存报告成功！");
    }

    /**
     * 批量审阅
     */
    @ApiOperation(value = "登记记录-批量审阅", notes = "登记记录-批量��阅")
    @PostMapping(value = "/verifyBatch")
    public Result<?> verifyBatch(@RequestBody JSONObject info) {
        try {
            JSONArray ids = info.getJSONArray("ids");
            List<RegTemplate> idList = ids.toJavaList(RegTemplate.class);
            customerRegService.verifyBatch(idList);
        } catch (Exception e) {
            log.error("档案表-批量审阅异常", e);
            return Result.error("批量审阅失败！");
        }
        return Result.OK("批量审阅成功！");
    }


    /**
     * 批量标记取走
     */
    @ApiOperation(value = "登记记录-批量标记取走", notes = "登记记录-批量标记取走")
    @PostMapping(value = "/markAsTakenBatch")
    public Result<?> markAsTakenBatch(@RequestBody JSONObject info) {
        try {
            JSONArray ids = info.getJSONArray("ids");
            List<String> idList = ids.toJavaList(String.class);
            String signBase64 = info.getString("signBase64");
            customerRegService.markTakenBatch(idList, signBase64);
        } catch (Exception e) {
            log.error("档案表-批量标记取走异常", e);
            return Result.error("批量标记取走失败！");
        }
        return Result.OK("批量标记取走成功！");
    }

    /**
     * 批量标记取走
     */
    @ApiOperation(value = "登记记录-重置状态重新生成pdf", notes = "登记记录-重置状态重新生成pdf")
    @PostMapping(value = "/resetState4ReGeneratePdf")
    public Result<?> resetState4ReGeneratePdf(@RequestBody JSONObject info) {
        try {
            JSONArray ids = info.getJSONArray("ids");
            List<String> idList = ids.toJavaList(String.class);
            customerRegService.resetState4ReGeneratePdf(idList);
        } catch (Exception e) {
            log.error("档案表-批量标记取走异常", e);
            return Result.error("批量标记取走失败！");
        }
        return Result.OK("批量标记取走成功！");
    }

    /**
     * 保存报告
     */
    @ApiOperation(value = "登记记录-更新报告打印状态", notes = "登记记录-更新报告打印状态")
    @PostMapping(value = "/updateReportPrintStatus")
    public Result<?> updateReportPrintStatus(@RequestBody JSONObject info) {
        try {
            String customerRegId = info.getString("customerRegId");
            customerRegService.updateReportPrintStatus(customerRegId);
        } catch (Exception e) {
            log.error("档案表-保存报告异常", e);
            return Result.error("保存报告失败！");
        }
        return Result.OK("保存报告成功！");
    }


    /**
     * 更新报告打印状态
     */
    @ApiOperation(value = "登记记录-更新报告打印状态", notes = "登记记录-更新报告打印状态")
    @GetMapping(value = "/updateReportPrintStatusById")
    public Result<?> updateReportPrintStatusById(@RequestParam(name = "id", required = true) String id) {
        try {
            customerRegService.updateReportPrintStatus(id);
        } catch (Exception e) {
            log.error("档案表-保存报告异常", e);
            return Result.error("保存报告失败！");
        }
        return Result.OK("保存报告成功！");
    }

    /**
     * 标记报告已取走
     */
    @ApiOperation(value = "登记记录-标记报告已取走", notes = "登记记录-标记报告已取走")
    @PostMapping(value = "/markReportAsTaken")
    public Result<?> markReportAsTaken(@RequestBody JSONObject info) {
        try {
            String id = info.getString("id");
            String signBase64 = info.getString("signBase64");

            customerRegService.markReportAsTaken(id, signBase64);
        } catch (Exception e) {
            log.error("档案表-保存报告异常", e);
            return Result.error("保存报告失败！");
        }
        return Result.OK("保存报告成功！");
    }

    /**
     * 发送报告通知
     */
    @ApiOperation(value = "登记记录-发送报告通知", notes = "登记记录-发送报告通知")
    @GetMapping(value = "/sendReportNotify")
    public Result<?> sendReportNotify(String customerRegId) {
        try {
            customerRegService.sendReportNotify(customerRegId);
        } catch (Exception e) {
            log.error("档案表-保存报告异常", e);
            return Result.error("保存报告失败！");
        }
        return Result.OK("保存报告成功！");
    }

    /**
     * regenratePdf
     */
    @ApiOperation(value = "登记记录-重新生成pdf", notes = "登记记录-重新生成pdf")
    @GetMapping(value = "/regeneratePdf")
    public Result<?> regeneratePdf(String customerRegId) {
        try {
            customerRegService.reGeneratePdf(customerRegId);
        } catch (Exception e) {
            log.error("档案表-重新生成pdf异常", e);
            return Result.error("重新生成pdf失败！");
        }
        return Result.OK("重新生成pdf成功！");
    }

    /**
     * 更新健康问卷ID
     */
    @ApiOperation(value = "登记记录-更新健康问卷ID", notes = "登记记录-更新健康问卷ID")
    @GetMapping(value = "/updateHealthQuestId")
    public Result<?> updateHealthQuestId(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "personalQuestId", required = true) String personalQuestId) {
        try {
            customerRegService.updateHealthQuestId(id, personalQuestId);
        } catch (Exception e) {
            log.error("档案表-更新健康问卷ID异常", e);
            return Result.error("更新健康问卷ID失败！");
        }
        return Result.OK("更新健康问卷ID成功！");
    }

    /**
     * 更新申请单打印次数
     *
     * @param info
     * @return
     */
    @ApiOperation(value = "客户登记-更新申请单打印次数", notes = "客户登记-更新申请单打印次数")
    @PostMapping(value = "/updateApplyPrintTimes")
    public Result<?> updateApplyPrintTimes(@RequestBody JSONObject info) {
        JSONArray regGroupIds = info.getJSONArray("regGroupIds");
        List<String> idList = regGroupIds.toJavaList(String.class);
        customerRegItemGroupService.updateApplyPrintTimes(idList);
        return Result.OK();
    }

    /**
     * 发送团检通知
     *
     * @param reg
     * @return
     */
    @ApiOperation(value = "客户登记-发送团检通知", notes = "客户登记-发送团检通知")
    @PostMapping(value = "/companyNotify")
    public Result<?> companyNotify(@RequestBody CustomerReg reg) {
        try {
            customerRegService.companyNotify(reg);
            return Result.OK();
        } catch (Exception e) {
            return Result.error(e.getMessage());

        }
    }

    @ApiOperation(value = "客户登记-根据身份证号查询手机号", notes = "客户登记-根据身份证号查询手机号")
    @PostMapping(value = "/getPhoneByIdCard")
    public Result<?> companyNotify(@RequestParam("idCard") String idCard) {
        try {
            Customer customer = customerService.getOne(new LambdaQueryWrapper<Customer>().eq(Customer::getIdCard, idCard));
            if (Objects.nonNull(customer)) {
                return Result.OK(customer.getPhone());
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());

        }
        return Result.error("未查询到手机号");
    }
    @ApiOperation(value = "客户登记-更新数据管理状态", notes = "客户登记-更新数据管理状态")
    @PostMapping(value = "/updateStatus")
    public Result<?> updateStatus(@RequestBody CustomerRegItemGroup customerRegItemGroup) {
        try {
            customerRegItemGroupService.updateStatus(customerRegItemGroup);
            return Result.OK("更新成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());

        }
    }
    @ApiOperation(value = "客户登记-向his推送项目", notes = "客户登记-向his推送项目")
    @PostMapping(value = "/sendItemGroup2Interface")
    public Result<?> sendItemGroup2Interface(@RequestBody  JSONObject info) {
        try {
            String regId = info.getString("regId");
            JSONArray ids = info.getJSONArray("ids");
            List<String> idList = ids.toJavaList(String.class);
            List<CustomerRegItemGroup> itemGroups = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, regId).in(CustomerRegItemGroup::getId, idList));
            customerRegItemGroupService.sendItemGroup2Interface(itemGroups);
            return Result.OK("推送成功！");
        } catch (Exception e) {
            return Result.error("推送失败！"+e.getMessage());

        }
    }

    @ApiOperation(value = "客户登记-根据身份证号或体检号查询登记信息", notes = "客户登记-根据身份证号或体检号查询登记信息")
    @GetMapping(value = "/getRegInfoByIdCardOrExamNo")
    public Result<?> getRegInfoByIdCardOrExamNo(@RequestParam("idCard") String idCard,@RequestParam("examNo") String examNo) {
        try {
            CustomerReg reg=null;
            if (StringUtils.isNotBlank(idCard) ) {
                 reg = customerRegService.getOne(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getIdCard, idCard).orderByDesc(CustomerReg::getRegTime).last("limit 1"));
                if (Objects.nonNull(reg)) {
                    List<CustomerRegItemGroup> itemGroups = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, reg.getId()));
                    reg.setItemGroupList(itemGroups);

                }
            }else if (StringUtils.isNotBlank(examNo)) {
                 reg = customerRegService.getOne(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getExamNo, examNo));
            }
            if (Objects.nonNull(reg)) {
                List<CustomerRegItemGroup> itemGroups = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, reg.getId()));
                reg.setItemGroupList(itemGroups);

            }
            return Result.OK(reg);
        } catch (Exception e) {
            return Result.error("查询失败！"+e.getMessage());

        }
    }

    @ApiOperation(value = "客户登记-根据身份证号或体检号查询登记信息", notes = "客户登记-根据身份证号或体检号查询登记信息")
    @GetMapping(value = "/getReportsByIdCardOrExamNo")
    public Result<?> getReportsByIdCardOrExamNo(@RequestParam("idCard") String idCard,@RequestParam("examNo") String examNo) {
        try {
            List<CustomerReg> regs= Lists.newArrayList();
            if (StringUtils.isNotBlank(idCard) ) {
                regs = customerRegService.list(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getIdCard, idCard).eq(CustomerReg::getStatus,ExConstants.REG_STATUS_REGED).orderByDesc(CustomerReg::getRegTime));
            }else if (StringUtils.isNotBlank(examNo)) {
                regs = customerRegService.list(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getExamNo, examNo).eq(CustomerReg::getStatus,ExConstants.REG_STATUS_REGED));
            }
            return Result.OK(regs);
        } catch (Exception e) {
            return Result.error("查询失败！"+e.getMessage());

        }
    }

    /**
     * 查询单个项目的依赖项目结果
     *
     * @param customerRegId 体检登记ID
     * @param groupId 项目组ID
     * @return 依赖项目结果列表
     */
    @AutoLog(value = "查询单个项目的依赖项目结果")
    @ApiOperation(value = "查询单个项目的依赖项目结果", notes = "根据体检登记ID和项目组ID查询其依赖项目的结果")
    @GetMapping(value = "/getDependentItemResults")
    public Result<List<DependentItemResultDTO>> getDependentItemResults(
            @RequestParam(name = "customerRegId", required = true) String customerRegId,
            @RequestParam(name = "groupId", required = true) String groupId) {
        try {
            List<DependentItemResultDTO> results = customerRegService.getDependentItemResults(customerRegId, groupId);
            return Result.OK(results);
        } catch (Exception e) {
            log.error("Query dependent item results failed for customerRegId: {}, groupId: {}", customerRegId, groupId, e);
            return Result.error("Query dependent item results failed: " + e.getMessage());
        }
    }

    /**
     * 批量查询多个项目的依赖项目结果
     *
     * @return 依赖项目结果映射
     */
    @AutoLog(value = "批量查询多个项目的依赖项目结果")
    @ApiOperation(value = "批量查询多个项目的依赖项目结果", notes = "根据体检登记ID和多个项目组ID批量查询依赖项目结果")
    @PostMapping(value = "/getDependentItemResultsBatch")
    public Result<Map<String, List<DependentItemResultDTO>>> getDependentItemResultsBatch(@RequestBody JSONObject info) {
        try {
            String customerRegId = info.getString("customerRegId");
            JSONArray groupIdsArray = info.getJSONArray("groupIds");
            List<String> groupIds = groupIdsArray.toJavaList(String.class);
            Map<String, List<DependentItemResultDTO>> results = customerRegService.getDependentItemResultsBatch(customerRegId, groupIds);
            return Result.OK(results);
        } catch (Exception e) {
            return Result.error("批量查询依赖项目结果失败: " + e.getMessage());
        }
    }

    /**
     * 查询体检人员的所有依赖项目结果
     *
     * @param customerRegId 体检登记ID
     * @return 所有依赖项目结果映射
     */
    @AutoLog(value = "查询体检人员的所有依赖项目结果")
    @ApiOperation(value = "查询体检人员的所有依赖项目结果", notes = "根据体检登记ID查询该人员所有项目的依赖关系和结果")
    @GetMapping(value = "/getAllDependentItemResults")
    public Result<Map<String, List<DependentItemResultDTO>>> getAllDependentItemResults(
            @RequestParam(name = "customerRegId", required = true) String customerRegId) {
        try {
            Map<String, List<DependentItemResultDTO>> results = customerRegService.getAllDependentItemResults(customerRegId);
            return Result.OK(results);
        } catch (Exception e) {
            log.error("Query all dependent item results failed for customerRegId: {}", customerRegId, e);
            return Result.error("Query all dependent item results failed: " + e.getMessage());
        }
    }

    /**
     * 添加带检查部位的项目组合
     *
     * @param request
     * @return
     */
    @AutoLog(value = "体检登记-添加带检查部位的项目组合")
    @ApiOperation(value = "添加带检查部位的项目组合", notes = "添加带检查部位的项目组合")
    @PostMapping(value = "/addItemGroupWithCheckParts")
    public Result<String> addItemGroupWithCheckParts(@RequestBody AddItemGroupWithCheckPartsRequest request) {
        try {
            customerRegService.addItemGroupWithCheckParts(request);
            return Result.OK("添加成功");
        } catch (Exception e) {
            log.error("添加带检查部位的项目组合失败", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 转换 ItemGroupRelation 列表为 RelationItemVO 列表
     */
    private List<RelationItemVO> convertToRelationItemVOs(List<ItemGroupRelation> relations) {
        if (relations == null) {
            return new ArrayList<>();
        }

        return relations.stream().map(relation -> {
            RelationItemVO vo = new RelationItemVO();
            vo.setRelationGroupId(relation.getRelationGroupId());
            vo.setRelationGroupName(relation.getRelationGroupName());
            vo.setRelationItemType(relation.getRelationItemType());
            vo.setRelationItemId(relation.getRelationItemId());
            vo.setRelationItemName(relation.getRelationItemName());
            vo.setQuantity(relation.getQuantity());
            return vo;
        }).collect(Collectors.toList());
    }

}

