package org.jeecg.modules.reg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.*;
import org.jeecg.modules.reg.entity.FormDisplayConfig;
import org.jeecg.modules.reg.entity.FieldDisplayConfig;
import org.jeecg.modules.reg.vo.FormDisplayConfigQueryParam;

import java.util.List;

/**
 * 表单显示配置Mapper
 */
@Mapper
public interface FormDisplayConfigMapper extends BaseMapper<FormDisplayConfig> {

    /**
     * 获取配置详情（包含字段列表）
     */
    @Select("SELECT * FROM form_display_config WHERE id = #{id}")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "fields", column = "id",
                    javaType = List.class,
                    many = @Many(select = "org.jeecg.modules.reg.mapper.FieldDisplayConfigMapper.selectByConfigId"))
    })
    FormDisplayConfig getConfigWithFields(@Param("id") String id);

    /**
     * 获取当前生效的配置
     */
    @Select("SELECT * FROM form_display_config WHERE center_id = #{centerId} AND form_type = #{formType} AND is_active = 1 LIMIT 1")
    FormDisplayConfig getActiveConfig(@Param("centerId") String centerId, @Param("formType") String formType);

    /**
     * 获取当前生效的配置（包含字段）
     */
    @Select("SELECT * FROM form_display_config WHERE form_type = #{formType} AND is_active = 1 AND del_flag = 0 LIMIT 1")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "fields", column = "id",
                    javaType = List.class,
                    many = @Many(select = "org.jeecg.modules.reg.mapper.FieldDisplayConfigMapper.selectByConfigId"))
    })
    FormDisplayConfig getActiveConfigWithFields(@Param("formType") String formType);

    /**
     * 分页查询配置列表
     */
    @Select("<script>" +
            "SELECT * FROM form_display_config WHERE del_flag = 0 " +
            "<if test='param.configName != null and param.configName != \"\"'>" +
            "AND config_name LIKE CONCAT('%', #{param.configName}, '%') " +
            "</if>" +
            "<if test='param.formType != null and param.formType != \"\"'>" +
            "AND form_type = #{param.formType} " +
            "</if>" +
            "<if test='param.isActive != null'>" +
            "AND is_active = #{param.isActive} " +
            "</if>" +
            "<if test='param.centerId != null and param.centerId != \"\"'>" +
            "AND center_id = #{param.centerId} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<FormDisplayConfig> selectConfigPage(Page<FormDisplayConfig> page, @Param("param") FormDisplayConfigQueryParam param);

    /**
     * 获取配置版本列表
     */
    @Select("<script>" +
            "SELECT * FROM form_display_config WHERE del_flag = 0 " +
            "AND form_type = #{formType} " +
            "<if test='centerId != null and centerId != \"\"'>" +
            "AND center_id = #{centerId} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<FormDisplayConfig> selectConfigVersions(@Param("formType") String formType, @Param("centerId") String centerId);
}