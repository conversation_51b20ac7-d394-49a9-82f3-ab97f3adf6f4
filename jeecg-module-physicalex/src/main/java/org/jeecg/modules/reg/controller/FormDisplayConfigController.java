package org.jeecg.modules.reg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.util.JwtUtil;
import cn.hutool.core.util.StrUtil;
import org.jeecg.modules.reg.entity.FormDisplayConfig;
import org.jeecg.modules.reg.service.IFormDisplayConfigService;
import org.jeecg.modules.reg.vo.FormDisplayConfigQueryParam;
import org.jeecg.modules.reg.vo.ConfigValidationResult;
import org.jeecg.modules.reg.vo.FieldConfigRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 表单字段配置管理
 */
@RestController
@RequestMapping("/reg/formFieldConfig")
@Api(tags = "表单字段配置管理")
@Slf4j
public class FormDisplayConfigController {

    @Autowired
    private IFormDisplayConfigService configService;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @GetMapping("/list")
    @ApiOperation("获取配置列表")
    public Result<IPage<FormDisplayConfig>> getConfigList(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            FormDisplayConfigQueryParam param) {

        Page<FormDisplayConfig> page = new Page<>(pageNo, pageSize);
        IPage<FormDisplayConfig> result = configService.getConfigList(page, param);
        return Result.OK(result);
    }

    @GetMapping("/active")
    @ApiOperation("获取当前生效的配置")
    public Result<FormDisplayConfig> getActiveConfig(
            @ApiParam("表单类型") @RequestParam String formType,
            @ApiParam("体检中心ID") @RequestParam(required = false) String centerId,
            @ApiParam("API版本") @RequestParam(defaultValue = "v1") String version) {

        FormDisplayConfig config = configService.getActiveConfig(formType, centerId);
        return Result.OK(config);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation("获取配置详情")
    public Result<FormDisplayConfig> getConfigDetail(@PathVariable String id) {
        FormDisplayConfig config = configService.getConfigWithFields(id);
        return Result.OK(config);
    }

    @PostMapping("/save")
    @ApiOperation("保存配置")
    public Result<String> saveConfig(@RequestBody FormDisplayConfig config) {
        boolean success = configService.saveConfigWithFields(config);
        if (success) {
            return Result.OK("保存成功");
        } else {
            return Result.error("保存失败");
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除配置")
    public Result<String> deleteConfig(@PathVariable String id) {
        boolean success = configService.removeById(id);
        if (success) {
            return Result.OK("删除成功");
        } else {
            return Result.error("删除失败");
        }
    }

    @GetMapping("/default")
    @ApiOperation("获取默认配置模板")
    public Result<FormDisplayConfig> getDefaultConfig(@RequestParam String formType) {
        FormDisplayConfig config = configService.getDefaultConfig(formType);
        return Result.OK(config);
    }

    // ==================== 新增接口 ====================

    @PostMapping("/validate")
    @ApiOperation("验证字段配置")
    public Result<ConfigValidationResult> validateConfig(@RequestBody FieldConfigRequest request) {
        try {
            ConfigValidationResult result = configService.validateFieldConfig(request);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("验证配置失败", e);
            return Result.error("验证配置失败: " + e.getMessage());
        }
    }

    @GetMapping("/versions")
    @ApiOperation("获取配置版本列表")
    public Result<List<FormDisplayConfig>> getConfigVersions(
            @ApiParam("表单类型") @RequestParam String formType,
            @ApiParam("体检中心ID") @RequestParam(required = false) String centerId) {

        List<FormDisplayConfig> versions = configService.getConfigVersions(formType, centerId);
        return Result.OK(versions);
    }

    @PostMapping("/copy")
    @ApiOperation("复制配置")
    public Result<String> copyConfig(
            @ApiParam("源配置ID") @RequestParam String sourceId,
            @ApiParam("新配置名称") @RequestParam String newConfigName,
            @ApiParam("目标中心ID") @RequestParam(required = false) String targetCenterId) {

        try {
            String newConfigId = configService.copyConfig(sourceId, newConfigName, targetCenterId);
            return Result.OK(newConfigId, "配置复制成功");
        } catch (Exception e) {
            log.error("复制配置失败", e);
            return Result.error("复制配置失败: " + e.getMessage());
        }
    }

    @PostMapping("/activate")
    @ApiOperation("激活配置")
    public Result<String> activateConfig(@ApiParam("配置ID") @RequestParam String configId) {
        try {
            boolean success = configService.activateConfig(configId);
            if (success) {
                return Result.OK("配置激活成功");
            } else {
                return Result.error("配置激活失败");
            }
        } catch (Exception e) {
            log.error("激活配置失败", e);
            return Result.error("激活配置失败: " + e.getMessage());
        }
    }

    @GetMapping("/permissions")
    @ApiOperation("获取用户字段配置权限")
    public Result<Map<String, Boolean>> getUserPermissions(HttpServletRequest request) {
        try {
            String username = JwtUtil.getUserNameByToken(request);
            if (StrUtil.isBlank(username)) {
                return Result.error("用户未登录");
            }
            LoginUser loginUser = sysBaseAPI.getUserByName(username);
            if (loginUser == null) {
                return Result.error("用户信息不存在");
            }
            Map<String, Boolean> permissions = configService.getUserFieldConfigPermissions(loginUser);
            return Result.OK(permissions);
        } catch (Exception e) {
            log.error("获取用户权限失败", e);
            return Result.error("获取用户权限失败: " + e.getMessage());
        }
    }

    @PostMapping("/batch-update")
    @ApiOperation("批量更新字段配置")
    public Result<String> batchUpdateFields(@RequestBody List<FieldConfigRequest> requests) {
        try {
            boolean success = configService.batchUpdateFieldConfigs(requests);
            if (success) {
                return Result.OK("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新失败", e);
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }

    @GetMapping("/export")
    @ApiOperation("导出配置")
    public Result<String> exportConfig(
            @ApiParam("配置ID") @RequestParam String configId,
            @ApiParam("导出格式") @RequestParam(defaultValue = "json") String format) {

        try {
            String exportData = configService.exportConfig(configId, format);
            return Result.OK(exportData, "导出成功");
        } catch (Exception e) {
            log.error("导出配置失败", e);
            return Result.error("导出配置失败: " + e.getMessage());
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入配置")
    public Result<String> importConfig(
            @ApiParam("配置数据") @RequestBody String configData,
            @ApiParam("导入格式") @RequestParam(defaultValue = "json") String format,
            @ApiParam("目标中心ID") @RequestParam(required = false) String targetCenterId) {

        try {
            String configId = configService.importConfig(configData, format, targetCenterId);
            return Result.OK(configId, "导入成功");
        } catch (Exception e) {
            log.error("导入配置失败", e);
            return Result.error("导入配置失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    @ApiOperation("获取配置统计信息")
    public Result<Map<String, Object>> getConfigStatistics(
            @ApiParam("表单类型") @RequestParam(required = false) String formType,
            @ApiParam("体检中心ID") @RequestParam(required = false) String centerId) {

        try {
            Map<String, Object> statistics = configService.getConfigStatistics(formType, centerId);
            return Result.OK(statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    @PostMapping("/init-default-data")
    @ApiOperation("初始化默认配置数据")
    public Result<String> initDefaultData() {
        try {
            boolean success = configService.initDefaultConfigData();
            if (success) {
                return Result.OK("默认配置数据初始化成功");
            } else {
                return Result.error("默认配置数据初始化失败");
            }
        } catch (Exception e) {
            log.error("初始化默认配置数据失败", e);
            return Result.error("初始化失败: " + e.getMessage());
        }
    }
}