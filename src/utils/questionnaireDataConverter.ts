/**
 * 问卷数据格式转换工具
 * 用于新旧问卷系统之间的数据格式转换
 */

export interface LegacyQuestionnaireData {
  // 基本信息
  customerName?: string
  customerAge?: number
  customerGender?: string
  
  // 症状信息
  symptoms?: Array<{
    symptom: string
    severity: string
    remark?: string
  }>
  
  // 疾病史
  diseaseHistory?: Array<{
    disease: string
    diagnosisTime?: string
    treatment?: string
  }>
  
  // 家族史
  familyHistory?: string
  
  // 职业史
  occupationHistory?: Array<{
    company: string
    position: string
    workYears: number
    hazardFactors?: string[]
  }>
  
  // 婚姻状况
  maritalStatus?: string
  
  // 放射史
  radiationHistory?: Array<{
    exposureType: string
    exposureTime: string
    protectionMeasures?: string
  }>
}

export interface ElegantQuestionnaireData {
  // 基本信息
  name: string
  age?: number
  gender: string
  
  // 工作环境
  workPosition: string
  workYears?: number
  hazardFactors: string[]
  
  // 症状
  symptoms: string[]
  
  // 生活习惯
  smokingHabit: string
  drinkingHabit: string
  exerciseHabit: string
  
  // 既往病史
  pastDiseases: string[]
  familyHistory: string
  
  // 其他
  additionalInfo: string
}

export class QuestionnaireDataConverter {
  
  /**
   * 新版数据转换为旧版格式
   * @param newData 新版问卷数据
   * @returns 旧版格式数据
   */
  static newToLegacy(newData: ElegantQuestionnaireData): LegacyQuestionnaireData {
    const legacyData: LegacyQuestionnaireData = {
      // 基本信息映射
      customerName: newData.name,
      customerAge: newData.age,
      customerGender: newData.gender,
      
      // 症状数据转换 - 新版只存储症状ID，需要转换为旧版格式
      symptoms: newData.symptoms?.map(symptomId => ({
        symptom: symptomId,
        severity: this.getDefaultSeverity(symptomId),
        remark: ''
      })) || [],
      
      // 既往疾病转换为疾病史格式
      diseaseHistory: newData.pastDiseases?.map(disease => ({
        disease: disease,
        diagnosisTime: '',
        treatment: ''
      })) || [],
      
      // 家族史直接映射
      familyHistory: newData.familyHistory || '',
      
      // 工作信息转换为职业史格式
      occupationHistory: newData.workPosition ? [{
        company: '', // 新版没有公司信息，留空
        position: newData.workPosition,
        workYears: newData.workYears || 0,
        hazardFactors: newData.hazardFactors || []
      }] : [],
      
      // 婚姻状况 - 新版暂无此字段，设置默认值
      maritalStatus: '未知',
      
      // 放射史 - 新版暂无此字段，设置空数组
      radiationHistory: []
    }
    
    return legacyData
  }
  
  /**
   * 旧版数据转换为新版格式
   * @param legacyData 旧版问卷数据
   * @returns 新版格式数据
   */
  static legacyToNew(legacyData: LegacyQuestionnaireData): ElegantQuestionnaireData {
    const newData: ElegantQuestionnaireData = {
      // 基本信息映射
      name: legacyData.customerName || '',
      age: legacyData.customerAge,
      gender: legacyData.customerGender || '',
      
      // 工作信息从职业史中提取
      workPosition: legacyData.occupationHistory?.[0]?.position || '',
      workYears: legacyData.occupationHistory?.[0]?.workYears,
      hazardFactors: legacyData.occupationHistory?.[0]?.hazardFactors || [],
      
      // 症状信息转换 - 只取症状ID
      symptoms: legacyData.symptoms?.map(item => item.symptom) || [],
      
      // 生活习惯 - 旧版没有这些字段，设置默认值
      smokingHabit: '不吸烟',
      drinkingHabit: '不饮酒',
      exerciseHabit: '偶尔运动',
      
      // 既往疾病从疾病史中提取
      pastDiseases: legacyData.diseaseHistory?.map(item => item.disease) || [],
      familyHistory: legacyData.familyHistory || '',
      
      // 其他信息 - 新版字段，旧版没有对应数据
      additionalInfo: ''
    }
    
    return newData
  }
  
  /**
   * 根据症状ID获取默认严重程度
   * @param symptomId 症状ID
   * @returns 默认严重程度
   */
  static getDefaultSeverity(symptomId: string): string {
    // 症状严重程度映射规则
    const severityMap: Record<string, string> = {
      // 神经系统症状
      '10002': '轻', // 头晕
      '10003': '中', // 头痛
      '10004': '中', // 眩晕
      '10005': '轻', // 失眠
      '10006': '轻', // 嗜睡
      '10007': '轻', // 多梦
      '10008': '中', // 记忆力减退
      '10009': '轻', // 易激动
      '10010': '中', // 疲乏无力
      '10011': '中', // 四肢麻木
      '10012': '中', // 动作不灵活
      '10013': '重', // 肌肉抽搐
      
      // 呼吸系统症状
      '10015': '中', // 胸痛
      '10016': '中', // 胸闷
      '10017': '轻', // 咳嗽
      '10018': '轻', // 咳痰
      '10019': '重', // 咯血
      '10020': '中', // 气促
      '10021': '中', // 气短
      
      // 心血管系统症状
      '10023': '中', // 心悸
      '10024': '轻', // 心前区不适
      '10025': '中', // 心前区疼痛
      
      // 消化系统症状
      '10027': '轻', // 食欲不振
      '10028': '轻', // 恶心
      '10029': '中', // 呕吐
      '10030': '轻', // 腹胀
      '10031': '中', // 腹痛
      '10032': '中', // 肝区疼痛
      '10033': '轻'  // 便秘
    }
    
    return severityMap[symptomId] || '中' // 默认为中等严重程度
  }
  
  /**
   * 验证新版数据格式
   * @param data 待验证的数据
   * @returns 验证结果
   */
  static validateElegantData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!data.name || typeof data.name !== 'string') {
      errors.push('姓名不能为空')
    }
    
    if (!data.gender || !['男', '女'].includes(data.gender)) {
      errors.push('性别必须为男或女')
    }
    
    if (!data.workPosition || typeof data.workPosition !== 'string') {
      errors.push('工作岗位不能为空')
    }
    
    if (data.age && (typeof data.age !== 'number' || data.age < 0 || data.age > 120)) {
      errors.push('年龄必须为0-120之间的数字')
    }
    
    if (!Array.isArray(data.symptoms)) {
      errors.push('症状必须为数组格式')
    }
    
    if (!Array.isArray(data.hazardFactors)) {
      errors.push('危害因素必须为数组格式')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 验证旧版数据格式
   * @param data 待验证的数据
   * @returns 验证结果
   */
  static validateLegacyData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!data.customerName || typeof data.customerName !== 'string') {
      errors.push('客户姓名不能为空')
    }
    
    if (!data.customerGender || !['男', '女'].includes(data.customerGender)) {
      errors.push('客户性别必须为男或女')
    }
    
    if (data.symptoms && !Array.isArray(data.symptoms)) {
      errors.push('症状必须为数组格式')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 数据清理和标准化
   * @param data 原始数据
   * @returns 清理后的数据
   */
  static sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return {}
    }
    
    const sanitized = { ...data }
    
    // 清理字符串字段
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'string') {
        sanitized[key] = sanitized[key].trim()
      }
    })
    
    // 确保数组字段为数组
    const arrayFields = ['symptoms', 'hazardFactors', 'pastDiseases']
    arrayFields.forEach(field => {
      if (sanitized[field] && !Array.isArray(sanitized[field])) {
        sanitized[field] = []
      }
    })
    
    return sanitized
  }
}

export default QuestionnaireDataConverter