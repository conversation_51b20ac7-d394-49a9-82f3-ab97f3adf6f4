<template>
  <a-card title="婚姻生育史" size="small">
    <template #extra>
      <a-button type="primary" @click="handleAdd" :disabled="disabled">
        <template #icon>
          <plus-outlined />
        </template>
        添加婚姻状况记录
      </a-button>
    </template>
    <div class="marital-status-list">
      <div v-if="!dataSource.length && !loading" class="empty-state">
        <a-empty description="暂无婚姻状况记录" />
      </div>

      <a-spin :spinning="loading">
        <div class="inline-form-list">
          <div
            v-for="(record, index) in dataSource"
            :key="record.uuid || record.id || `new_${index}`"
            class="form-item-row"
            :class="{ editing: record._editMode, 'new-record': record._isNew }"
          >
            <a-form :ref="(el) => setFormRef(el, index)" :model="record" layout="horizontal">
              <div class="form-content">
                <a-row :gutter="[16, 16]" align="middle">
                  <a-col :span="6">
                    <a-form-item
                      label="结婚日期"
                      name="marriageDate"
                      :rules="[{ required: true, message: '请输入结婚日期' }]"
                      :label-col="{ span: 8 }"
                      :wrapper-col="{ span: 16 }"
                      class="form-field"
                    >
                      <template v-if="record._editMode">
                        <div class="ant-form-item-control-input">
                          <div class="ant-form-item-control-input-content">
                            <input
                              v-cleave="{ date: true, delimiter: '-', datePattern: ['Y', 'm', 'd'] }"
                              v-model="record.marriageDate"
                              class="ant-input css-dev-only-do-not-override-udyjmm"
                              placeholder="年-月-日"
                            />
                          </div>
                        </div>
                      </template>
                      <span v-else>{{ record.marriageDate || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item
                      label="配偶接触放射线情况"
                      name="partnerRadiation"
                      :label-col="{ span: 12 }"
                      :wrapper-col="{ span: 12 }"
                      class="form-field"
                    >
                      <a-input v-if="record._editMode" v-model:value="record.partnerRadiation" placeholder="请输入配偶接触放射线情况" />
                      <span v-else>{{ record.partnerRadiation || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item
                      label="配偶职业及健康情况"
                      name="partnerHealth"
                      :label-col="{ span: 12 }"
                      :wrapper-col="{ span: 12 }"
                      class="form-field"
                    >
                      <a-input v-if="record._editMode" v-model:value="record.partnerHealth" placeholder="请输入配偶职业及健康情况" />
                      <span v-else>{{ record.partnerHealth || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="6">
                    <a-form-item label="备注" name="remark" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" class="form-field">
                      <a-input v-if="record._editMode" v-model:value="record.remark" placeholder="请输入备注" />
                      <span v-else>{{ record.remark || '-' }}</span>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
              <div class="form-actions">
                <template v-if="record._editMode">
                  <a-button type="primary" size="small" @click="handleSave(record, index)" :loading="record._saving" :disabled="disabled">
                    保存
                  </a-button>
                  <a-button size="small" @click="handleCancel(record, index)" style="margin-left: 8px"> 取消 </a-button>
                </template>
                <template v-else>
                  <a-button type="link" size="small" @click="handleEdit(record, index)" :disabled="disabled"> 编辑 </a-button>
                  <a-popconfirm title="确定要删除这条记录吗？" @confirm="handleDelete(record, index)" :disabled="disabled">
                    <a-button type="link" danger size="small" :disabled="disabled"> 删除 </a-button>
                  </a-popconfirm>
                </template>
              </div>
            </a-form>
          </div>
        </div>
      </a-spin>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, reactive, inject, watch, onMounted, nextTick } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { zyInquiryMaritalStatusList, zyInquiryMaritalStatusSaveOrUpdate, zyInquiryMaritalStatusDelete } from '../ZyInquiry.api';
  import { buildUUID } from '@/utils/uuid';

  /**日期计算*/
  const dayjs = inject('$dayjs');

  interface MaritalStatusRecord {
    id?: string;
    uuid?: string; // 添加UUID字段用于卡片管理
    marriageDate: string;
    partnerRadiation: string;
    partnerHealth: string;
    remark: string;
    inquiryId: string;
    _editMode?: boolean;
    _isNew?: boolean;
    _saving?: boolean;
    _originalData?: any;
  }

  // Props
  interface Props {
    disabled?: boolean;
  }
  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
  });

  // Injected values
  const inquiryMainId = inject('inquiryMainId', ref(''));
  const inquiryReady = inject('inquiryReady', ref(false));

  // State
  const loading = ref(false);
  const dataSource = ref<MaritalStatusRecord[]>([]);
  const formRefs = ref<any[]>([]);

  // Set form ref for specific index
  const setFormRef = (el: any, index: number) => {
    if (el) {
      formRefs.value[index] = el;
    }
  };

  // Load data
  async function loadData() {
    if (!inquiryMainId.value) return;

    try {
      loading.value = true;
      const result = await zyInquiryMaritalStatusList({ inquiryId: inquiryMainId.value });
      if (result && result.records) {
        dataSource.value = result.records.map((item: any) => ({
          ...item,
          uuid: item.uuid || buildUUID(), // 为回显数据生成UUID
          _editMode: false,
          _isNew: false,
          _saving: false,
        }));
      } else {
        dataSource.value = [];
      }

      // 如果没有历史记录，自动添加一个空卡片
      if (dataSource.value.length === 0) {
        addEmptyCard();
      }
    } catch (error) {
      console.error('加载婚姻状况记录失败:', error);
      message.error('加载数据失败');
      dataSource.value = [];
    } finally {
      loading.value = false;
    }
  }

  // Add new record
  function handleAdd() {
    const newRecord: MaritalStatusRecord = {
      uuid: buildUUID(), // 为新记录生成UUID
      marriageDate: '',
      partnerRadiation: '',
      partnerHealth: '',
      remark: '',
      inquiryId: inquiryMainId.value,
      _editMode: true,
      _isNew: true,
      _saving: false,
    };
    dataSource.value.push(newRecord);
  }

  // 添加空卡片的方法，供父组件调用
  async function addEmptyCard() {
    // 等待数据加载完成
    if (loading.value) {
      // 如果正在加载，等待加载完成
      await new Promise((resolve) => {
        const unwatch = watch(
          () => loading.value,
          (isLoading) => {
            if (!isLoading) {
              unwatch();
              resolve(true);
            }
          }
        );
      });
    }

    // 如果已经有记录了，就不添加空卡片
    if (dataSource.value.length > 0) {
      return;
    }
    handleAdd();
  }

  // Edit record
  function handleEdit(record: MaritalStatusRecord, index: number) {
    // Store original data for cancel operation
    record._originalData = { ...record };
    record._editMode = true;
  }

  // Save record
  async function handleSave(record: MaritalStatusRecord, index: number) {
    try {
      // Validate form
      const formRef = formRefs.value[index];
      if (formRef) {
        await formRef.validate();
      }

      // 校验结婚日期的合法性
      if (record.marriageDate) {
        if (!dayjs(record.marriageDate).isValid()) {
          message.error('结婚日期格式不正确');
          return;
        }

        const marriageDate = dayjs(record.marriageDate);
        const currentDate = dayjs();
        if (marriageDate.isAfter(currentDate)) {
          message.error('结婚日期不能晚于当前日期');
          return;
        }

        const minDate = dayjs('1900-01-01');
        if (marriageDate.isBefore(minDate)) {
          message.error('结婚日期不能早于1900年');
          return;
        }
      }

      record._saving = true;
      const isUpdate = !!record.id;
      const saveData = {
        id: record.id,
        marriageDate: record.marriageDate,
        partnerRadiation: record.partnerRadiation,
        partnerHealth: record.partnerHealth,
        remark: record.remark,
        inquiryId: inquiryMainId.value,
      };

      const result = await zyInquiryMaritalStatusSaveOrUpdate(saveData, isUpdate);
      if (result && result.success) {
        // 处理新增记录的ID更新
        if (!isUpdate && result.result) {
          // 根据后端返回的数据结构更新ID
          if (typeof result.result === 'string') {
            record.id = result.result;
          } else if (result.result.id) {
            record.id = result.result.id;
            // 如果后端返回了完整对象，更新其他字段
            Object.keys(result.result).forEach(key => {
              if (key !== 'uuid' && record.hasOwnProperty(key)) {
                record[key] = result.result[key];
              }
            });
          }
          console.log(`✅ 新增婚姻状况记录成功，ID: ${record.id}, UUID: ${record.uuid}`);
        } else if (isUpdate) {
          console.log(`✅ 更新婚姻状况记录成功，ID: ${record.id}, UUID: ${record.uuid}`);
        }

        record._editMode = false;
        record._isNew = false;
        record._originalData = undefined;
        message.success(isUpdate ? '更新成功' : '保存成功');
      } else {
        throw new Error(result?.message || '保存失败');
      }
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error.message || '保存失败');
    } finally {
      record._saving = false;
    }
  }

  // Cancel edit
  function handleCancel(record: MaritalStatusRecord, index: number) {
    if (record._isNew) {
      // Remove new record
      dataSource.value.splice(index, 1);
    } else {
      // Restore original data
      if (record._originalData) {
        Object.assign(record, record._originalData);
        record._originalData = undefined;
      }
      record._editMode = false;
    }
  }

  // Delete record
  async function handleDelete(record: MaritalStatusRecord, index: number) {
    try {
      // 如果是新记录（没有ID），直接从列表中移除
      if (record._isNew || !record.id) {
        console.log(`🗑️ 删除新建的婚姻状况记录，UUID: ${record.uuid}`);
        dataSource.value.splice(index, 1);
        message.success('删除成功');
        return;
      }

      // 如果是已保存的记录，调用后端删除接口
      console.log(`🗑️ 删除婚姻状况记录，ID: ${record.id}, UUID: ${record.uuid}`);
      await zyInquiryMaritalStatusDelete({ id: record.id }, () => {
        dataSource.value.splice(index, 1);
        message.success('删除成功');
      });
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }

  // Watch for inquiry ready state changes
  watch(
    () => inquiryReady.value,
    (ready) => {
      if (ready) {
        loadData();
      }
    },
    { immediate: true }
  );

  // Watch for inquiry main ID changes
  watch(
    () => inquiryMainId.value,
    (newId) => {
      if (newId && inquiryReady.value) {
        loadData();
      }
    }
  );

  onMounted(() => {
    if (inquiryMainId.value && inquiryReady.value) {
      loadData();
    }
  });

  // 暴露方法给父组件
  defineExpose({
    addEmptyCard,
  });
</script>

<style lang="less" scoped>
  .marital-status-list {
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      border-radius: 8px;
      border: 1px dashed #d9d9d9;
    }
  }

  .inline-form-list {
    .form-item-row {
      background: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
      }

      &.editing {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      &.new-record {
        border-color: #52c41a;
        background: #f6ffed;
      }

      .ant-form {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .form-content {
        width: 100%;
      }

      .form-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 8px;
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }
</style>
