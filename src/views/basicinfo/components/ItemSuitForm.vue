<template>
  <a-spin :spinning="confirmLoading">
    <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-row>
        <a-col :span="12">
          <a-form-item label="排序号" v-bind="validateInfos.sort">
            <a-input-number v-model:value="model.sort" placeholder="请输入排序号" style="width: 100%" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="名称" v-bind="validateInfos.name">
            <a-input v-model:value="model.name" placeholder="请输入名称" @change="setHelpChar" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="所属分类" v-bind="validateInfos.categoryId">
            <j-async-search-select
              placeholder="请选择所属分类"
              v-model:value="model.categoryId"
              dict="suit_category,category_name,id"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="启用" v-bind="validateInfos.enableFlag">
            <j-switch v-model:value="model.enableFlag" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="对外开放" v-bind="validateInfos.pubAvailable">
            <j-switch v-model:value="model.pubAvailable" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="助记码" v-bind="validateInfos.helpChar">
            <a-input v-model:value="model.helpChar" placeholder="请输入助记码" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="性别限制" v-bind="validateInfos.sexLimit">
            <j-dict-select-tag v-model:value="model.sexLimit" dictCode="sexLimit" placeholder="请选择性别限制" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="最小年龄" v-bind="validateInfos.minAge">
            <a-input-number v-model:value="model.minAge" placeholder="请输入最小年龄" style="width: 100%" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="最大年龄" v-bind="validateInfos.maxAge">
            <a-input-number v-model:value="model.maxAge" placeholder="请输入最大年龄" style="width: 100%" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <!--        <a-col :span="12">
          <a-form-item label="折后价" v-bind="validateInfos.price">
            <a-input-number
              v-model:value="model.price"
              placeholder="请输入折后价"
              @change="computeDiffPrice"
              style="width: 100%"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="价格" v-bind="validateInfos.costPrice">
            <a-input-number
              v-model:value="model.costPrice"
              placeholder="请输入原价"
              @change="computeDiffPrice"
              style="width: 100%"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="差价" v-bind="validateInfos.diffPrice">
            <a-input-number readonly v-model:value="model.diffPrice" placeholder="请输入差价" style="width: 100%" :disabled="disabled" />
          </a-form-item>
        </a-col>-->

        <a-col :span="12">
          <a-form-item label="套餐类型" v-bind="validateInfos.suitType">
            <j-dict-select-tag v-model:value="model.suitType" dictCode="suit_type" placeholder="请选择套餐类型" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="套餐图片" v-bind="validateInfos.suitPicture">
            <j-image-upload :fileMax="1" v-model:value="model.suitPicture" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="体检类别" v-bind="validateInfos.examinationType">
            <j-dict-select-tag v-model:value="model.examinationType" dictCode="examination_type" placeholder="请选择体检类别" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="健康证行业" v-bind="validateInfos.healthCertIndustry">
            <j-async-search-select
              placeholder="请选择健康证行业"
              v-model:value="model.healthCertIndustry"
              dict="zy_industry where del_flag=0,name,id"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="危害因素" v-bind="validateInfos.riskFactor">
            <j-async-search-select
              placeholder="请选择危害因素"
              v-model:value="model.riskFactor"
              dict="zy_risk_factor,name,code"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <!--<a-col :span="12">
          <a-form-item label="问卷" v-bind="validateInfos.questId">
            <j-dict-select-tag v-model:value="formData.questId" dictCode="" placeholder="请选择问卷" :disabled="disabled" />
          </a-form-item>
        </a-col>-->
        <a-col :span="12">
          <a-form-item label="岗位" v-bind="validateInfos.job">
            <j-dict-select-tag v-model:value="model.job" dict="job_status" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备孕禁忌" v-bind="validateInfos.pregnancyFlag">
            <j-switch v-model:value="model.pregnancyFlag" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12" />
        <a-col :span="12">
          <a-form-item label="注意事项" v-bind="validateInfos.notice">
            <a-textarea v-model:value="model.notice" :rows="4" placeholder="请输入注意事项" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" v-bind="validateInfos.remark">
            <a-textarea v-model:value="model.remark" :rows="4" placeholder="请输入备注" :disabled="disabled" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import { computed, defineExpose, nextTick, reactive, ref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import { getValueType } from '/@/utils';
  import { getNextSort, saveOrUpdate } from '../ItemSuit.api';
  import { Form } from 'ant-design-vue';
  import { duplicateValidate } from '/@/utils/helper/validator';
  import { JAsyncSearchSelect } from '@/components/Form';
  import JImageUpload from '@/components/Form/src/jeecg/components/JImageUpload.vue';

  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => {} },
    formBpm: { type: Boolean, default: true },
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const model = reactive<Record<string, any>>({
    id: '',
    sort: 1,
    name: '',
    helpChar: '',
    sexLimit: '不限',
    minAge: 0,
    maxAge: 120,
    price: undefined,
    costPrice: undefined,
    diffPrice: undefined,
    examinationType: '',
    suitType: '',
    healthCertIndustry: '',
    riskFactor: '',
    questId: '',
    job: '',
    pregnancyFlag: 0,
    notice: '',
    remark: '',
    enableFlag: 1,
    categoryId: null,
    pubAvailable: '1',
    suitPicture: null,
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    categoryId: [{ required: true, message: '请选择所属分类!' }],
    sort: [{ required: true, message: '请输入排序号!' }],
    name: [{ required: true, message: '请输入名称!' }, { validator: nameDuplicatevalidate }],
    sexLimit: [{ required: true, message: '请输入性别限制!' }],
    minAge: [{ required: true, message: '请输入最小年龄!' }],
    maxAge: [{ required: true, message: '请输入最大年龄!' }],
    /* price: [{ required: true, message: '请输入折后价!' }],
    costPrice: [{ required: true, message: '请输入原价!' }],
    diffPrice: [{ required: true, message: '请输入差价!' }],*/
    examinationType: [{ required: true, message: '请输入体检类别!' }],
    healthCertIndustry: [{ required: false, message: '请输入健康证行业!' }],
    suitType: [{ required: true, message: '请选择套餐类型!' }],
  });
  const { resetFields, validate, validateInfos } = useForm(model, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  function setHelpChar() {
    if (model.name) {
      //model.helpChar = PinyinUtil.getInitials(model.name);
    }
  }

  function computeDiffPrice() {
    if (model.price && model.costPrice) {
      model.diffPrice = model.price - model.costPrice;
    }
  }
  /**
   * 新增
   */
  function add() {
    getNextSort().then((res) => {
      model.sort = res;
    });
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      //赋值
      Object.assign(model, record);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化

    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          model.id = res.result;
          emit('ok', { id: res.result });
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  async function nameDuplicatevalidate(_r, value) {
    return duplicateValidate('item_suit', 'name', value, model.id || '');
  }
  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    height: 500px !important;
    overflow-y: auto;
    padding: 14px;
  }
</style>
