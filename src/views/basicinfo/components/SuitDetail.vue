<template>
  <a-modal
    title="套餐详情"
    :width="width"
    :open="visible"
    @ok="close"
    :okButtonProps="{ class: { 'jee-hidden': true } }"
    @cancel="close"
    cancelText="关闭"
  >
    <div style="height: 70vh; overflow-y: scroll; padding: 10px">
      <a-card size="small" title="基本信息">
        <a-descriptions size="small" bordered>
          <!--          <a-descriptions-item label="排序号">{{ model.sort }}</a-descriptions-item>-->
          <a-descriptions-item label="名称">
            <span>{{ model.name }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="启用">
            <a-tag color="success" v-if="model.enableFlag === 1">启用</a-tag>
            <a-tag color="error" v-else>禁用</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="折后价">
            <span style="color: #f5222d">{{ model.price }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="原价">
            <span>{{ model.costPrice }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="差价">
            <span>{{ model.diffPrice }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="体检类别">
            <span>{{ model.examinationType }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="套餐类型">{{ model.suitType }}</a-descriptions-item>
          <a-descriptions-item label="助记码">{{ model.helpChar }}</a-descriptions-item>
          <a-descriptions-item label="性别限制">{{ model.sexLimit }}</a-descriptions-item>
          <a-descriptions-item label="最小年龄">{{ model.minAge }}</a-descriptions-item>
          <a-descriptions-item label="最大年龄">{{ model.maxAge }}</a-descriptions-item>
          <a-descriptions-item label="健康证行业">{{ model.healthCertIndustry_dictText }}</a-descriptions-item>
          <a-descriptions-item label="危害因素">{{ model.riskFactor }}</a-descriptions-item>
          <a-descriptions-item label="岗位">{{ model.job }}</a-descriptions-item>
          <a-descriptions-item label="备孕禁忌">{{ model.pregnancyFlag }}</a-descriptions-item>
          <a-descriptions-item label="注意事项">{{ model.notice }}</a-descriptions-item>
          <a-descriptions-item label="备注">{{ model.remark }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card size="small" title="项目列表" style="margin-top: 10px; padding: 0">
        <a-table :dataSource="dataSource" :columns="columns" size="small" />
      </a-card>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
  import { ref, reactive, nextTick, h } from 'vue';
  import { getGroupSuit, getItemGroupBySuit } from '@/views/basicinfo/ItemSuit.api';
  import { Tag } from 'ant-design-vue';

  const width = ref<string>('80%');
  const visible = ref<boolean>(false);
  const tableLoading = ref<boolean>(false);
  const model = reactive<Record<string, any>>({});
  const dataSource = ref<Record<string, any>[]>([]);
  const columns = [
    {
      title: '组合名称',
      dataIndex: 'groupName',
      ellipsis: false,
      width: '35%',
      customRender: ({ record }) => {
        // 根据项目来源类型添加缩进和标识
        const indent = record.sourceType === 'main' ? 0 : 20;
        const badge = getSourceTypeBadge(record.sourceType);
        return h('div', { style: { paddingLeft: `${indent}px`, display: 'flex', alignItems: 'center' } }, [
          badge,
          h('span', { style: { marginLeft: badge ? '8px' : '0' } }, record.groupName)
        ]);
      }
    },
    {
      title: '项目关系',
      dataIndex: 'sourceType',
      width: '15%',
      customRender: ({ record }) => getSourceTypeTag(record.sourceType)
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: '15%',
    },
    {
      title: '折后价',
      dataIndex: 'priceAfterDis',
      width: '12%',
    },
    {
      title: '原价',
      dataIndex: 'price',
      width: '10%',
    },
    {
      title: '折扣率',
      dataIndex: 'disRate',
      width: '13%',
    },
  ];

  // 获取项目来源类型标识
  function getSourceTypeBadge(sourceType) {
    if (!sourceType || sourceType === 'main') return null;

    const badgeMap = {
      dependent: h('span', { style: { color: '#1890ff', fontSize: '12px' } }, '●'),
      attach: h('span', { style: { color: '#52c41a', fontSize: '12px' } }, '▲'),
      gift: h('span', { style: { color: '#faad14', fontSize: '12px' } }, '★'),
      suit: h('span', { style: { color: '#722ed1', fontSize: '12px' } }, '◆')
    };

    return badgeMap[sourceType] || null;
  }

  // 获取项目来源类型标签
  function getSourceTypeTag(sourceType) {
    if (!sourceType || sourceType === 'main') {
      return h(Tag, { color: 'blue' }, '主项目');
    }

    const tagMap = {
      dependent: h(Tag, { color: 'cyan' }, '依赖项目'),
      attach: h(Tag, { color: 'green' }, '附属项目'),
      gift: h(Tag, { color: 'gold' }, '赠送项目'),
      suit: h(Tag, { color: 'purple' }, '套餐项目')
    };

    return tagMap[sourceType] || h(Tag, { color: 'default' }, '未知');
  }

  // 优化后的数据获取函数：使用后端统一依赖分析
  async function fetchTableData() {
    tableLoading.value = true;

    try {
      console.log('🚀 调用套餐项目依赖分析接口，套餐ID:', model.id);

      // 使用现有的套餐项目列表接口（已包含关系数据）
      const response = await getItemGroupBySuit({ suitId: model.id });
      const analysisResult = response.result || response;

      if (analysisResult && analysisResult.suitGroupList) {
        // 使用后端返回的完整数据
        const rawData = analysisResult.suitGroupList;

        // 建立父子关系映射
        const parentChildMap = buildParentChildMapForSuit(rawData);

        // 构建层级结构
        dataSource.value = buildHierarchicalStructureForSuit(rawData, parentChildMap);

        console.log('✅ 套餐项目依赖关系分析完成');
        console.log('   - 项目数量:', dataSource.value.length);
        console.log('   - 父子关系映射:', parentChildMap.size, '个父项目');
      } else {
        console.warn('⚠️ 后端接口返回数据格式异常，降级到旧逻辑');
        await fetchTableDataLegacy();
      }
    } catch (error) {
      console.error('❌ 获取套餐项目依赖分析失败，降级到旧逻辑:', error);
      await fetchTableDataLegacy();
    } finally {
      tableLoading.value = false;
    }
  }

  // 降级函数：保持原有逻辑作为备用
  async function fetchTableDataLegacy() {
    console.log('使用旧版套餐项目获取逻辑');
    try {
      const res = await getGroupSuit({ suitId: model.id });
      // 为旧数据添加默认的sourceType
      dataSource.value = res.map(item => ({
        ...item,
        sourceType: 'main' // 旧逻辑中所有项目都视为主项目
      }));
    } catch (error) {
      console.error('旧版套餐项目获取也失败:', error);
      dataSource.value = [];
    }
  }

  // 为套餐项目建立父子关系映射
  function buildParentChildMapForSuit(data) {
    const parentChildMap = new Map();

    console.log('🔍 开始为套餐项目建立父子关系映射...');

    data.forEach(item => {
      const sourceType = item.sourceType || 'main';

      if (sourceType !== 'main') {
        // 这是一个子项目，需要找到它的父项目
        const parentItem = findParentItemForSuit(item, data);
        if (parentItem) {
          const parentId = parentItem.id;
          if (!parentChildMap.has(parentId)) {
            parentChildMap.set(parentId, []);
          }
          parentChildMap.get(parentId).push(item);
          console.log(`   ✅ 套餐父子关系: ${parentItem.groupName} -> ${item.groupName} (${sourceType})`);
        } else {
          console.log(`   ⚠️ 套餐项目未找到父项目: ${item.groupName} (${sourceType})`);
        }
      }
    });

    console.log(`🎯 套餐父子关系映射完成，共找到 ${parentChildMap.size} 个父项目`);
    return parentChildMap;
  }

  // 为套餐项目找到父项目
  function findParentItemForSuit(childItem, allItems) {
    const childSourceType = childItem.sourceType || 'main';

    // 遍历所有项目，找到包含此子项目的父项目
    for (const potentialParent of allItems) {
      if (potentialParent.id === childItem.id) continue;

      const parentSourceType = potentialParent.sourceType || 'main';
      if (parentSourceType !== 'main') continue;

      // 检查父项目的依赖关系数据
      if (isChildOfParentForSuit(childItem, potentialParent, childSourceType)) {
        return potentialParent;
      }
    }

    return null;
  }

  // 检查套餐子项目是否属于父项目
  function isChildOfParentForSuit(childItem, parentItem, childSourceType) {
    // 基于后端返回的依赖关系数据进行判断
    switch (childSourceType) {
      case 'dependent':
        return parentItem.dependentGroups &&
               parentItem.dependentGroups.some(dep => dep.relationGroupId === childItem.itemGroupId);

      case 'attach':
        return parentItem.attachGroups &&
               parentItem.attachGroups.some(attach => attach.relationGroupId === childItem.itemGroupId);

      case 'gift':
        return parentItem.giftGroups &&
               parentItem.giftGroups.some(gift => gift.relationGroupId === childItem.itemGroupId);

      default:
        // 降级到原有的字段判断逻辑
        return childItem.attachBaseId === parentItem.id ||
               childItem.parentGroupId === parentItem.itemGroupId ||
               (childItem.itemSuitId && parentItem.itemSuitId && childItem.itemSuitId === parentItem.itemSuitId);
    }
  }

  // 构建套餐项目的层级结构
  function buildHierarchicalStructureForSuit(data, parentChildMap) {
    console.log('=== buildHierarchicalStructureForSuit 开始 ===');

    const result = [];
    const processedItems = new Set();

    // 找出所有主项目
    const mainItems = data.filter(item => (item.sourceType || 'main') === 'main');
    console.log('套餐主项目数量:', mainItems.length);

    // 处理每个主项目及其子项目
    mainItems.forEach(mainItem => {
      if (processedItems.has(mainItem.id)) return;

      console.log(`--- 处理套餐主项目: ${mainItem.groupName} (${mainItem.id}) ---`);

      // 添加主项目
      result.push(mainItem);
      processedItems.add(mainItem.id);

      // 查找并添加该主项目的所有子项目
      const childItems = parentChildMap.get(mainItem.id) || [];
      console.log(`找到 ${childItems.length} 个子项目:`, childItems.map(child => child.groupName));

      // 对子项目进行排序
      const sortedChildren = sortChildItemsForSuit(childItems);

      // 添加排序后的子项目
      sortedChildren.forEach(childItem => {
        if (!processedItems.has(childItem.id)) {
          result.push(childItem);
          processedItems.add(childItem.id);
        }
      });
    });

    // 处理孤立的子项目（没有找到父项目的）
    const orphanItems = data.filter(item =>
      (item.sourceType || 'main') !== 'main' && !processedItems.has(item.id)
    );

    if (orphanItems.length > 0) {
      console.log('发现孤立的子项目:', orphanItems.map(item => item.groupName));
      orphanItems.forEach(item => {
        result.push(item);
        processedItems.add(item.id);
      });
    }

    console.log('=== buildHierarchicalStructureForSuit 完成 ===');
    console.log('最终项目顺序:', result.map(item => `${item.groupName} (${item.sourceType || 'main'})`));

    return result;
  }

  // 对套餐子项目进行排序
  function sortChildItemsForSuit(childItems) {
    return childItems.sort((a, b) => {
      const aType = a.sourceType || 'main';
      const bType = b.sourceType || 'main';

      const typeOrder = { dependent: 1, gift: 2, attach: 3, suit: 4 };
      const aOrder = typeOrder[aType] || 999;
      const bOrder = typeOrder[bType] || 999;

      if (aOrder !== bOrder) {
        return aOrder - bOrder;
      }

      // 同类型按创建时间排序
      const aTime = a.createTime ? new Date(a.createTime).getTime() : 0;
      const bTime = b.createTime ? new Date(b.createTime).getTime() : 0;
      return aTime - bTime;
    });
  }

  /**
   * 编辑
   */
  function showDetail(record) {
    visible.value = true;
    nextTick(() => {
      //赋值
      Object.assign(model, record);
      fetchTableData();
    });
  }

  function close() {
    visible.value = false;
  }

  defineExpose({
    showDetail,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    height: 500px !important;
    overflow-y: auto;
    padding: 14px;
  }
</style>
