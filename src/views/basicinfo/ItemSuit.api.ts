import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/basicinfo/itemSuit/list',
  listByKeyword = '/basicinfo/itemSuit/listByKeyword',
  save = '/basicinfo/itemSuit/add',
  edit = '/basicinfo/itemSuit/edit',
  deleteOne = '/basicinfo/itemSuit/delete',
  deleteBatch = '/basicinfo/itemSuit/deleteBatch',
  importExcel = '/basicinfo/itemSuit/importExcel',
  exportXls = '/basicinfo/itemSuit/exportXls',
  getNextSort = '/basicinfo/itemSuit/getNextSort',
  getItemGroupBySuit = '/basicinfo/itemSuit/getGroupBySuit',
  getSuitGroup = '/basicinfo/itemSuit/getSuitGroup',
  getGroupOfSuit = '/basicinfo/itemSuit/getGroupOfSuit',
  saveItemGroupOfSuit = '/basicinfo/itemSuit/saveGroupOfSuit',
  addItemGroupsToSuit = '/basicinfo/itemSuit/addItemGroupsToSuit',
  listSuitRecycleBin = '/basicinfo/itemSuit/listSuitRecycleBin',
  deleteBatchForever = '/basicinfo/itemSuit/deleteBatchForever',
  batchRecovery = '/basicinfo/itemSuit/batchRecover',
  batchUpdateEnableFlag = '/basicinfo/itemSuit/batchUpdateEnableFlag',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 获取下一个排序号
 */
export const getNextSort = () => defHttp.get({ url: Api.getNextSort });

/**
 * 获取套餐内的体检组合
 */
export const getItemGroupBySuit = (params) => defHttp.get({ url: Api.getItemGroupBySuit, params });

export const getGroupSuit = (params) => defHttp.get({ url: Api.getSuitGroup, params });

export const getGroupOfSuit = (params) => defHttp.get({ url: Api.getGroupOfSuit, params });

/**
 * 保存套餐内的体检组合
 */
export const saveItemGroupOfSuit = (params) => defHttp.post({ url: Api.saveItemGroupOfSuit, params });

/**
 * 添加项目到套餐（含附属项目和赠送项目处理）
 */
export const addItemGroupsToSuit = (params) => defHttp.post({ url: Api.addItemGroupsToSuit, params },{ isTransformResponse: false });

/**
 * 根据关键字查询
 * @param params
 */
export const listSuitByKeyword = (params) => defHttp.get({ url: Api.listByKeyword, params }, { isTransformResponse: false });



export const listSuitRecycleBin = (params) => defHttp.get({ url: Api.listSuitRecycleBin, params });

export const recoverBatch = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认恢复',
    content: '是否恢复选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp
        .get({
          url: Api.batchRecovery,
          params,
        })
        .then(() => {
          handleSuccess();
        });
    },
  });
};

export const deleteBatchForever = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认永久删除',
    content: '是否永久删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp
        .delete(
          {
            url: Api.deleteBatchForever,
            data: params,
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};
export const batchUpdateEnableFlag = (params) => defHttp.post({ url: Api.batchUpdateEnableFlag, params });
