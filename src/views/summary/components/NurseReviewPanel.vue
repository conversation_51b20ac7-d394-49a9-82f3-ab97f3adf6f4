<template>
  <div class="nurse-review-panel">
    <a-card size="small" :bordered="false">
      <template #title>
        <div class="review-header">
          <span class="review-title">护士审阅总检内容</span>
          <a-tag v-if="reviewRecord?.status" :color="getStatusColor(reviewRecord.status)">
            {{ reviewRecord.status }}
          </a-tag>
        </div>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="submitReview" :disabled="!hasAnnotations" v-if="!isReadOnly">
            <CheckOutlined />
            提交审阅
          </a-button>
          <a-button @click="saveReview" v-if="!isReadOnly">
            <SaveOutlined />
            保存
          </a-button>
          <a-button @click="closeReview">
            <CloseOutlined />
            关闭
          </a-button>
        </a-space>
      </template>

      <div class="review-content">
        <!-- 体检人员信息 -->
        <div class="patient-info" v-if="currentReg">
          <a-descriptions size="small" :column="4" bordered>
            <a-descriptions-item label="姓名">{{ currentReg.name }}</a-descriptions-item>
            <a-descriptions-item label="性别">{{ currentReg.gender }}</a-descriptions-item>
            <a-descriptions-item label="年龄">{{ currentReg.age }}{{ currentReg.ageUnit }}</a-descriptions-item>
            <a-descriptions-item label="体检号">{{ currentReg.examNo }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 总检内容审阅区域 -->
        <a-row :gutter="16" style="margin-top: 16px">
          <!-- 异常汇总审阅 -->
          <a-col :span="8">
            <a-card size="small" title="异常汇总" class="review-section">
              <div class="review-content-area">
                <AbnormalSummaryReviewEditor
                  ref="abnormalReviewRef"
                  :abnormal-summaries="abnormalSummaries"
                  :annotations="abnormalAnnotations"
                  :read-only="isReadOnly"
                  @add-annotation="handleAddAbnormalAnnotation"
                  @remove-annotation="handleRemoveAbnormalAnnotation"
                />
              </div>
            </a-card>
          </a-col>

          <!-- 诊断审阅 -->
          <a-col :span="8">
            <a-card size="small" title="诊断" class="review-section">
              <div class="review-content-area">
                <DiagnosisReviewEditor
                  ref="diagnosisReviewRef"
                  :diagnosis-list="diagnosisList"
                  :annotations="diagnosisAnnotations"
                  :read-only="isReadOnly"
                  @add-annotation="handleAddDiagnosisAnnotation"
                  @remove-annotation="handleRemoveDiagnosisAnnotation"
                />
              </div>
            </a-card>
          </a-col>

          <!-- 建议审阅 -->
          <a-col :span="8">
            <a-card size="small" title="总检建议" class="review-section">
              <div class="review-content-area">
                <AdviceReviewEditor
                  ref="adviceReviewRef"
                  :advice-list="adviceList"
                  :annotations="adviceAnnotations"
                  :read-only="isReadOnly"
                  @add-annotation="handleAddAdviceAnnotation"
                  @remove-annotation="handleRemoveAdviceAnnotation"
                />
              </div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 审阅意见 -->
        <a-card size="small" title="审阅意见" style="margin-top: 16px" v-if="!isReadOnly || reviewRecord?.reviewComment">
          <a-textarea v-model:value="reviewComment" placeholder="请输入审阅意见..." :rows="4" :disabled="isReadOnly" />
        </a-card>

        <!-- 标注历史 -->
        <a-card size="small" title="标注记录" style="margin-top: 16px" v-if="allAnnotations.length > 0">
          <a-list size="small" :data-source="allAnnotations">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <span class="annotation-type">{{ getAnnotationTypeText(item.type) }}</span>
                    <a-tag size="small" color="orange">{{ item.severity }}</a-tag>
                  </template>
                  <template #description>
                    <div>{{ item.content }}</div>
                    <div class="annotation-meta">
                      <span>位置: {{ item.position }}</span>
                      <span style="margin-left: 16px">时间: {{ item.createTime }}</span>
                      <span style="margin-left: 16px">审阅人: {{ item.reviewer }}</span>
                    </div>
                  </template>
                </a-list-item-meta>
                <template #actions v-if="!isReadOnly">
                  <a @click="removeAnnotation(item)">删除</a>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </div>
    </a-card>

    <!-- 标注弹窗 -->
    <AnnotationModal ref="annotationModalRef" @confirm="handleAnnotationConfirm" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { CheckOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons-vue';
  import AbnormalSummaryReviewEditor from './AbnormalSummaryReviewEditor.vue';
  import DiagnosisReviewEditor from './DiagnosisReviewEditor.vue';
  import AdviceReviewEditor from './AdviceReviewEditor.vue';
  import AnnotationModal from './AnnotationModal.vue';
  import { saveNurseReview, submitNurseReview, getNurseReviewRecord, getAnnotationsByRegId } from '../NurseReview.api';

  interface Props {
    currentReg?: any;
    customerSummary?: any;
    abnormalSummaries?: any[];
    diagnosisList?: string[];
    adviceList?: any[];
    visible?: boolean;
    readOnly?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    readOnly: false,
    abnormalSummaries: () => [],
    diagnosisList: () => [],
    adviceList: () => [],
  });

  const emit = defineEmits(['close', 'success']);

  // 组件引用
  const abnormalReviewRef = ref();
  const diagnosisReviewRef = ref();
  const adviceReviewRef = ref();
  const annotationModalRef = ref();

  // 数据状态
  const reviewRecord = ref<any>(null);
  const reviewComment = ref('');
  const abnormalAnnotations = ref<any[]>([]);
  const diagnosisAnnotations = ref<any[]>([]);
  const adviceAnnotations = ref<any[]>([]);

  // 计算属性
  const isReadOnly = computed(() => props.readOnly || reviewRecord.value?.status === '已提交');
  const hasAnnotations = computed(
    () => abnormalAnnotations.value.length > 0 || diagnosisAnnotations.value.length > 0 || adviceAnnotations.value.length > 0
  );

  const allAnnotations = computed(() => [...abnormalAnnotations.value, ...diagnosisAnnotations.value, ...adviceAnnotations.value]);

  // 方法
  const getStatusColor = (status: string) => {
    const colorMap = {
      草稿: 'default',
      已提交: 'success',
      已处理: 'blue',
    };
    return colorMap[status] || 'default';
  };

  const getAnnotationTypeText = (type: string) => {
    const typeMap = {
      abnormal: '异常汇总',
      diagnosis: '诊断',
      advice: '建议',
    };
    return typeMap[type] || type;
  };

  // 标注处理方法
  const handleAddAbnormalAnnotation = (annotation: any) => {
    abnormalAnnotations.value.push({
      ...annotation,
      type: 'abnormal',
      id: Date.now().toString(),
      createTime: new Date().toLocaleString(),
      reviewer: '当前用户', // 实际应该从用户信息获取
    });
  };

  const handleRemoveAbnormalAnnotation = (annotationId: string) => {
    abnormalAnnotations.value = abnormalAnnotations.value.filter((item) => item.id !== annotationId);
  };

  const handleAddDiagnosisAnnotation = (annotation: any) => {
    diagnosisAnnotations.value.push({
      ...annotation,
      type: 'diagnosis',
      id: Date.now().toString(),
      createTime: new Date().toLocaleString(),
      reviewer: '当前用户',
    });
  };

  const handleRemoveDiagnosisAnnotation = (annotationId: string) => {
    diagnosisAnnotations.value = diagnosisAnnotations.value.filter((item) => item.id !== annotationId);
  };

  const handleAddAdviceAnnotation = (annotation: any) => {
    adviceAnnotations.value.push({
      ...annotation,
      type: 'advice',
      id: Date.now().toString(),
      createTime: new Date().toLocaleString(),
      reviewer: '当前用户',
    });
  };

  const handleRemoveAdviceAnnotation = (annotationId: string) => {
    adviceAnnotations.value = adviceAnnotations.value.filter((item) => item.id !== annotationId);
  };

  const removeAnnotation = (annotation: any) => {
    if (annotation.type === 'abnormal') {
      handleRemoveAbnormalAnnotation(annotation.id);
    } else if (annotation.type === 'diagnosis') {
      handleRemoveDiagnosisAnnotation(annotation.id);
    } else if (annotation.type === 'advice') {
      handleRemoveAdviceAnnotation(annotation.id);
    }
  };

  const handleAnnotationConfirm = (annotation: any) => {
    // 根据当前选择的内容类型添加标注
    // 这里需要根据实际情况确定是哪种类型的标注
  };

  // 保存和提交方法
  const saveReview = async () => {
    try {
      const reviewData = {
        customerRegId: props.currentReg?.id,
        summaryId: props.customerSummary?.id,
        reviewComment: reviewComment.value,
        annotations: allAnnotations.value,
        status: '草稿',
      };

      await saveNurseReview(reviewData);
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败');
    }
  };

  const submitReview = async () => {
    if (!hasAnnotations.value) {
      message.warning('请至少添加一个标注后再提交');
      return;
    }

    try {
      const reviewData = {
        customerRegId: props.currentReg?.id,
        summaryId: props.customerSummary?.id,
        reviewComment: reviewComment.value,
        annotations: allAnnotations.value,
        status: '已提交',
      };

      await submitNurseReview(reviewData);
      message.success('提交成功');
      emit('success');
    } catch (error) {
      message.error('提交失败');
    }
  };

  const closeReview = () => {
    emit('close');
  };

  // 初始化数据
  const loadReviewData = async () => {
    if (!props.currentReg?.id) return;

    try {
      // 加载审阅记录
      const reviewRes = await getNurseReviewRecord(props.currentReg.id);
      if (reviewRes.success && reviewRes.result) {
        reviewRecord.value = reviewRes.result;
        reviewComment.value = reviewRes.result.reviewComment || '';
      }

      // 加载标注记录
      const annotationsRes = await getAnnotationsByRegId(props.currentReg.id);
      if (annotationsRes.success && annotationsRes.result) {
        const annotations = annotationsRes.result;
        abnormalAnnotations.value = annotations.filter((item) => item.type === 'abnormal');
        diagnosisAnnotations.value = annotations.filter((item) => item.type === 'diagnosis');
        adviceAnnotations.value = annotations.filter((item) => item.type === 'advice');
      }
    } catch (error) {
      console.error('加载审阅数据失败:', error);
    }
  };

  // 监听props变化
  watch(
    () => props.currentReg?.id,
    () => {
      if (props.visible) {
        loadReviewData();
      }
    }
  );

  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        loadReviewData();
      }
    }
  );

  onMounted(() => {
    if (props.visible) {
      loadReviewData();
    }
  });
</script>

<style scoped>
  .nurse-review-panel {
    height: 100%;
  }

  .review-header {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .review-title {
    font-weight: 600;
    font-size: 16px;
  }

  .patient-info {
    margin-bottom: 16px;
  }

  .review-section {
    height: 400px;
  }

  .review-content-area {
    height: 350px;
    overflow-y: auto;
  }

  .annotation-type {
    font-weight: 600;
  }

  .annotation-meta {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }
</style>
