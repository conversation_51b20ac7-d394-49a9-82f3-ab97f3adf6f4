<template>
  <a-card
    size="small"
    :class="['conclusion-card', { editing: record.editable, 'main-factor': record.mainFlag === '1' }]"
    :hoverable="!record.editable"
  >
    <!-- 卡片头部 -->
    <template #title>
      <div class="card-header">
        <div class="factor-info">
          <a-tag :color="record.mainFlag === '1' ? 'red' : 'blue'" size="small">
            {{ record.mainFlag === '1' ? '主要' : '次要' }}
          </a-tag>
          <a-tag v-if="record.autoCreated" color="green" size="small"> 自动创建 </a-tag>
          <a-tag v-if="record.manualCreated" color="orange" size="small"> 手动创建 </a-tag>
          <span class="risk-factor">{{ record.riskFactorText || record.riskFactor || '未设置危害因素' }}</span>
        </div>
        <div class="card-actions">
          <!-- 非编辑状态：显示编辑和删除按钮 -->
          <template v-if="!record.editable">
            <a-space size="small">
              <a-button type="primary" size="small" @click="$emit('edit', record)" :loading="loading">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>

              <a-popconfirm title="确定要删除这条记录吗？" ok-text="确定" cancel-text="取消" @confirm="$emit('delete', record)" placement="topLeft">
                <a-button type="primary" size="small" danger>
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>

          <!-- 编辑状态：显示保存和取消按钮，以及状态提示 -->
          <template v-else>
            <div class="edit-actions">
              <a-space size="small">
                <a-button type="primary" size="small" @click="$emit('save', record)" :loading="saving">
                  <template #icon><CheckOutlined /></template>
                  保存结论
                </a-button>

                <a-button size="small" @click="$emit('cancel', record)" :disabled="saving">
                  <template #icon><CloseOutlined /></template>
                  取消
                </a-button>
              </a-space>
            </div>
          </template>
        </div>
      </div>
    </template>

    <!-- 卡片内容 -->
    <div class="card-content">
      <a-row :gutter="[12, 8]">
        <!-- 主要/次要 -->
        <a-col :span="8">
          <div class="field-item">
            <div class="field-value">
              <template v-if="record.editable">
                <a-radio-group v-model:value="record.mainFlag" button-style="solid">
                  <a-radio value="1" class="square-radio">主要</a-radio>
                  <a-radio value="0" class="square-radio">次要</a-radio>
                </a-radio-group>
              </template>
              <template v-else>
                <a-tag :color="record.mainFlag === '1' ? 'red' : 'blue'" size="small">
                  {{ record.mainFlag === '1' ? '主要' : '次要' }}
                </a-tag>
              </template>
            </div>
          </div>
        </a-col>

        <!-- 危害因素 -->
        <a-col :span="8">
          <div class="field-item">
            <label class="field-label">危害因素:</label>
            <div class="field-value">
              <template v-if="record.editable">
                <JAsyncSearchSelect
                  v-model:value="record.riskCode"
                  dict="zy_risk_factor,name,code"
                  placeholder="请选择危害因素"
                  size="small"
                  style="width: 180px"
                />
              </template>
              <template v-else>
                <span>{{ record.riskFactorText || record.riskFactor || '-' }}</span>
              </template>
            </div>
          </div>
        </a-col>

        <!-- 工种 -->
        <!--        <a-col :span="6">
          <div class="field-item">
            <label class="field-label">工种:</label>
            <div class="field-value">
              <template v-if="record.editable">
                <a-input 
                  v-model:value="record.workType" 
                  placeholder="请输入工种"
                  size="small"
                />
              </template>
              <template v-else>
                <span>{{ record.workType || '-' }}</span>
              </template>
            </div>
          </div>
        </a-col>-->

        <!-- 职业检结论 -->
        <a-col :span="8">
          <div class="field-item">
            <label class="field-label required">结论:</label>
            <div class="field-value">
              <template v-if="record.editable">
                <JDictSelectTag
                  v-model:value="record.conclusion"
                  dictCode="zy_conclusion_dict,dict_text,code"
                  placeholder="请选择职业检结论"
                  size="small"
                  style="width: 200px"
                />
              </template>
              <template v-else>
                <a-tag v-if="record.conclusion" color="green" size="small">
                  {{ record.conclusionText || record.conclusion }}
                </a-tag>
                <span v-else>-</span>
              </template>
            </div>
          </div>
        </a-col>
        <!-- 职业病 -->
        <a-col :span="8">
          <div class="field-item">
            <label class="field-label">职业病:</label>
            <div class="field-value">
              <template v-if="record.editable">
                <JDictSelectTag
                  v-model:value="record.zyDisease"
                  dictCode="zy_disease_dict,dict_text,code"
                  placeholder="请选择职业病"
                  :triggerChange="false"
                  style="width: 200px"
                />
              </template>
              <template v-else>
                <div v-if="record.zyDisease">
                  <!-- 优先使用zyDiseaseText，如果没有则使用原始代码 -->
                  <template v-if="record.zyDiseaseText">
                    <a-tag v-for="diseaseText in record.zyDiseaseText.split('，')" :key="diseaseText" size="small" color="orange">
                      {{ diseaseText }}
                    </a-tag>
                  </template>
                  <template v-else>
                    <a-tag v-for="disease in (record.zyDisease || '').split(',')" :key="disease" size="small" color="orange">
                      {{ disease }}
                    </a-tag>
                  </template>
                </div>
                <span v-else>-</span>
              </template>
            </div>
          </div>
        </a-col>

        <!-- 职业禁忌证 -->
        <a-col :span="8">
          <div class="field-item">
            <label class="field-label">禁忌证:</label>
            <div class="field-value">
              <template v-if="record.editable">
                <JDictSelectTag
                  v-model:value="record.zySymptom"
                  dictCode="zy_taboo_symptom,name,code"
                  placeholder="请选择职业禁忌证"
                  :triggerChange="false"
                  style="width: 200px"
                />
              </template>
              <template v-else>
                <div v-if="record.zySymptom">
                  <!-- 优先使用zySymptomText，如果没有则使用原始代码 -->
                  <template v-if="record.zySymptomText">
                    <a-tag v-for="symptomText in record.zySymptomText.split('，')" :key="symptomText" size="small" color="purple">
                      {{ symptomText }}
                    </a-tag>
                  </template>
                  <template v-else>
                    <a-tag v-for="symptom in (record.zySymptom || '').split(',')" :key="symptom" size="small" color="purple">
                      {{ symptom }}
                    </a-tag>
                  </template>
                </div>
                <span v-else>-</span>
              </template>
            </div>
          </div>
        </a-col>

        <!-- 结论依据 -->
        <a-col :span="8">
          <div class="field-item">
            <label class="field-label required">结论依据:</label>
            <div class="field-value">
              <template v-if="record.editable">
                <JDictSelectTag
                  v-model:value="record.according"
                  dictCode="zy_conclusion_according,content,content"
                  placeholder="请选择结论依据"
                  size="small"
                  style="width: 200px"
                />
              </template>
              <template v-else>
                <span>{{ record.according || '-' }}</span>
              </template>
            </div>
          </div>
        </a-col>

        <!-- 处理意见 -->
        <a-col :span="12">
          <div class="field-item">
            <label class="field-label">处理意见:</label>
            <div class="field-value">
              <template v-if="record.editable">
                <a-textarea v-model:value="record.advice" placeholder="请输入处理意见" :rows="2" size="small" style="width: 500px" />
              </template>
              <template v-else>
                <span class="advice-text">{{ record.advice || '-' }}</span>
              </template>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <!-- 保存状态指示器 -->
    <div v-if="saving" class="saving-indicator">
      <a-spin size="small" />
      <span class="ml-2">保存中...</span>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { EditOutlined, CheckOutlined, CloseOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { JAsyncSearchSelect } from '@/components/Form';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSelectMultiple from '@/components/Form/src/jeecg/components/JSelectMultiple.vue';

  interface Props {
    record: any;
    loading?: boolean;
    saving?: boolean;
  }

  interface Emits {
    (e: 'edit', record: any): void;
    (e: 'save', record: any): void;
    (e: 'cancel', record: any): void;
    (e: 'delete', record: any): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    saving: false,
  });

  const emit = defineEmits<Emits>();
</script>

<style lang="less" scoped>
  .conclusion-card {
    margin-bottom: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);

      .card-actions {
        opacity: 1;
      }
    }

    &.editing {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      background-color: #f6ffed;

      &:hover {
        box-shadow:
          0 0 0 2px rgba(24, 144, 255, 0.3),
          0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }

    &.main-factor {
      border-left: 4px solid #ff4d4f;
    }

    :deep(.ant-card-head) {
      padding: 8px 16px;
      min-height: auto;
      border-bottom: 1px solid #f0f0f0;
    }

    :deep(.ant-card-body) {
      padding: 12px 16px;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .factor-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .risk-factor {
        font-weight: 500;
        color: #262626;
      }
    }

    .card-actions {
      display: flex;
      align-items: center;
      gap: 4px;
      opacity: 0.7;
      transition: opacity 0.3s ease;

      &:hover {
        opacity: 1;
      }

      .edit-actions {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 4px;

        .edit-status {
          font-size: 11px;
          color: #8c8c8c;
          white-space: nowrap;
        }
      }
    }
  }

  .card-content {
    .field-item {
      display: flex;
      //flex-direction: column;
      gap: 4px;

      .field-label {
        font-size: 12px;
        color: #8c8c8c;
        font-weight: 500;

        &.required {
          position: relative;

          &::after {
            content: '*';
            color: #ff4d4f;
            margin-left: 2px;
            font-size: 14px;
            line-height: 1;
          }
        }
      }

      .field-value {
        font-size: 13px;
        color: #262626;

        .advice-text {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          line-height: 1.4;
        }
      }
    }
  }

  .saving-indicator {
    position: absolute;
    top: 8px;
    right: 60px;
    display: flex;
    align-items: center;
    color: #1890ff;
    font-size: 12px;
  }
</style>
