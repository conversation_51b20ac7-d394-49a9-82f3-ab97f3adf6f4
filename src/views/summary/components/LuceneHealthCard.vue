<template>
  <!-- 操作按钮区域 -->
  <div class="health-card-header">
    <a-space>
      <a-button size="small" @click="refreshHealth" :loading="loading" type="primary" ghost>
        <Icon icon="ant-design:reload-outlined" />
        刷新状态
      </a-button>
      <a-button size="small" @click="performHealthCheck" :loading="checkLoading">
        <Icon icon="ant-design:check-circle-outlined" />
        健康检查
      </a-button>
    </a-space>
  </div>

  <!-- 核心指标卡片 -->
  <a-card size="small" class="metrics-card" :bordered="false">
    <a-row :gutter="[16, 16]">
      <!-- 整体健康状态 -->
      <a-col :xs="24" :sm="12" :md="6">
        <div class="metric-item">
          <a-statistic
            title="整体状态"
            :value="getOverallStatusText()"
            :value-style="{ color: getOverallStatusColor(), fontSize: '16px', fontWeight: 'bold' }"
          >
            <template #prefix>
              <Icon :icon="getOverallStatusIcon()" :style="{ color: getOverallStatusColor(), fontSize: '18px' }" />
            </template>
          </a-statistic>
        </div>
      </a-col>

      <!-- 索引状态 -->
      <a-col :xs="24" :sm="12" :md="6">
        <div class="metric-item">
          <a-statistic title="索引状态" :value="getIndexStatusText()" :value-style="{ color: getIndexStatusColor(), fontSize: '14px' }">
            <template #prefix>
              <Icon
                :icon="healthData.indexExists ? 'ant-design:database-outlined' : 'ant-design:exclamation-circle-outlined'"
                :style="{ color: getIndexStatusColor() }"
              />
            </template>
          </a-statistic>
          <div class="metric-subtitle"> 文档数: {{ formatNumber(healthData.indexDocumentCount) }} </div>
        </div>
      </a-col>

      <!-- 搜索成功率 -->
      <a-col :xs="24" :sm="12" :md="6">
        <div class="metric-item">
          <a-statistic
            title="搜索成功率"
            :value="searchSuccessRate"
            suffix="%"
            :value-style="{ color: getSuccessRateColor(healthData.searchSuccessRate), fontSize: '14px' }"
          >
            <template #prefix>
              <Icon icon="ant-design:search-outlined" :style="{ color: getSuccessRateColor(healthData.searchSuccessRate) }" />
            </template>
          </a-statistic>
          <div class="metric-subtitle"> {{ healthData.totalSearchCount || 0 }} 次搜索 </div>
        </div>
      </a-col>

      <!-- 索引成功率 -->
      <a-col :xs="24" :sm="12" :md="6">
        <div class="metric-item">
          <a-statistic
            title="索引成功率"
            :value="indexSuccessRate"
            suffix="%"
            :value-style="{ color: getSuccessRateColor(healthData.indexSuccessRate), fontSize: '14px' }"
          >
            <template #prefix>
              <Icon icon="ant-design:file-sync-outlined" :style="{ color: getSuccessRateColor(healthData.indexSuccessRate) }" />
            </template>
          </a-statistic>
          <div class="metric-subtitle"> {{ healthData.totalIndexCount || 0 }} 次索引 </div>
        </div>
      </a-col>
    </a-row>
  </a-card>

  <!-- 详细信息区域 -->
  <a-row :gutter="16" style="margin-top: 16px">
    <!-- 时间信息 -->
    <a-col :xs="24" :md="8">
      <a-card size="small" title="时间信息" class="info-card">
        <a-descriptions :column="1" size="small">
          <a-descriptions-item label="最后检查">
            <a-tooltip :title="lastCheckTimeTooltip">
              {{ lastCheckTime }}
            </a-tooltip>
          </a-descriptions-item>
          <a-descriptions-item label="检查间隔">
            {{ getTimeSinceLastCheck() }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </a-col>

    <!-- 索引信息 -->
    <a-col :xs="24" :md="8">
      <a-card size="small" title="索引详情" class="info-card">
        <a-descriptions :column="1" size="small">
          <a-descriptions-item label="索引存在">
            <a-tag :color="healthData.indexExists ? 'success' : 'error'">
              {{ healthData.indexExists ? '已创建' : '未创建' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="文档数量">
            <span class="number-highlight">{{ formatNumber(healthData.indexDocumentCount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="索引大小">
            <span class="size-info">{{ formatIndexSize(healthData.indexSizeInfo) }}</span>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </a-col>

    <!-- 操作统计 -->
    <a-col :xs="24" :md="8">
      <a-card size="small" title="操作统计" class="info-card">
        <a-descriptions :column="1" size="small">
          <a-descriptions-item label="搜索统计">
            <div class="stat-line">
              <span
                >成功: <span class="success-count">{{ getSuccessCount('search') }}</span></span
              >
              <span
                >失败: <span class="error-count">{{ healthData.failedSearchCount || 0 }}</span></span
              >
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="索引统计">
            <div class="stat-line">
              <span
                >成功: <span class="success-count">{{ getSuccessCount('index') }}</span></span
              >
              <span
                >失败: <span class="error-count">{{ healthData.failedIndexCount || 0 }}</span></span
              >
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </a-col>
  </a-row>

  <!-- 操作按钮区域 -->
  <a-card size="small" title="索引操作" style="margin-top: 16px" class="operation-card">
    <a-space wrap>
      <a-button type="primary" size="small" @click="handleCreateIndex" :loading="operationLoading.create" :disabled="healthData.indexExists">
        <Icon icon="ant-design:plus-outlined" />
        创建索引
      </a-button>
      <a-button size="small" @click="handleUpdateIndex" :loading="operationLoading.update" :disabled="!healthData.indexExists">
        <Icon icon="ant-design:sync-outlined" />
        更新索引
      </a-button>
      <a-button size="small" @click="handleRebuildIndex" :loading="operationLoading.rebuild" danger :disabled="!healthData.indexExists">
        <Icon icon="ant-design:reload-outlined" />
        重建索引
      </a-button>
      <a-button size="small" @click="handleRepairIndex" :loading="operationLoading.repair" :disabled="!healthData.indexExists">
        <Icon icon="ant-design:tool-outlined" />
        修复索引
      </a-button>
    </a-space>

    <!-- 操作提示 -->
    <a-alert v-if="!healthData.indexExists" message="索引尚未创建，请先创建索引" type="warning" size="small" style="margin-top: 12px" show-icon />
    <a-alert
      v-else-if="!healthData.healthy"
      message="索引状态异常，建议执行修复或重建操作"
      type="error"
      size="small"
      style="margin-top: 12px"
      show-icon
    />
  </a-card>

  <!-- 任务监控 -->
  <!-- 当前任务状态 -->
  <a-card v-if="currentTask" size="small" style="margin-top: 16px">
    <template #title>
      <a-space>
        <Icon icon="ant-design:loading-outlined" spin />
        任务执行中
      </a-space>
    </template>
    <template #extra>
      <a-button size="small" @click="refreshTaskStatus" :loading="taskLoading">
        <Icon icon="ant-design:reload-outlined" />
        刷新
      </a-button>
    </template>

    <a-descriptions :column="2" size="small">
      <a-descriptions-item label="任务类型">
        {{ getTaskTypeName(currentTask.taskType) }}
      </a-descriptions-item>
      <a-descriptions-item label="任务状态">
        <a-tag :color="getTaskStatusColor(currentTask.status)">
          {{ getTaskStatusName(currentTask.status) }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="开始时间">
        {{ formatTime(currentTask.startTime) }}
      </a-descriptions-item>
      <a-descriptions-item label="运行时长">
        {{ getRunningDuration(currentTask.startTime) }}
      </a-descriptions-item>
      <a-descriptions-item label="进度" v-if="currentTask.progress !== undefined">
        <a-progress :percent="currentTask.progress" size="small" :status="currentTask.status === 'FAILED' ? 'exception' : 'active'" />
      </a-descriptions-item>
      <a-descriptions-item label="消息" v-if="currentTask.message">
        {{ currentTask.message }}
      </a-descriptions-item>
    </a-descriptions>
  </a-card>

  <!-- 轮询状态指示器 -->
  <a-alert v-if="!currentTask" style="margin-top: 16px" :type="pollingTimer ? 'info' : 'warning'" size="small" show-icon>
    <template #message>
      <a-space v-if="pollingTimer">
        <span>监控状态:</span>
        <a-tag :color="isPollingActive ? 'processing' : 'default'">
          {{ isPollingActive ? '活跃监控 (3秒)' : '慢速监控 (30秒)' }}
        </a-tag>
      </a-space>
      <span v-else> 任务监控未启动，执行索引操作后将自动开始监控 </span>
    </template>
  </a-alert>

  <!-- 任务历史 -->
  <a-collapse v-if="taskHistory.length > 0" style="margin-top: 16px" size="small">
    <a-collapse-panel key="task-history" header="任务历史">
      <a-list size="small" :data-source="taskHistory" :pagination="false">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #title>
                <a-space>
                  <a-tag :color="getTaskStatusColor(item.status)">
                    {{ getTaskStatusName(item.status) }}
                  </a-tag>
                  {{ getTaskTypeName(item.taskType) }}
                </a-space>
              </template>
              <template #description>
                <div>
                  <div>开始时间: {{ formatTime(item.startTime) }}</div>
                  <div v-if="item.endTime">结束时间: {{ formatTime(item.endTime) }}</div>
                  <div v-if="item.duration">耗时: {{ item.duration }}ms</div>
                  <div v-if="item.message">消息: {{ item.message }}</div>
                </div>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-collapse-panel>
  </a-collapse>
</template>

<script lang="ts" setup>
  import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { defHttp } from '/@/utils/http/axios';
  import { getTaskStatus } from '../SummaryAdvice.api';

  // 响应式数据
  const loading = ref(false);
  const checkLoading = ref(false);
  const healthData = ref<any>({
    healthy: true,
    searchSuccessRate: 1,
    indexSuccessRate: 1,
    lastCheckTime: Date.now(),
    totalSearchCount: 0,
    failedSearchCount: 0,
    totalIndexCount: 0,
    failedIndexCount: 0,
    indexExists: false,
    indexDocumentCount: 0,
    indexSizeInfo: 'Unknown',
  });

  const operationLoading = reactive({
    create: false,
    update: false,
    rebuild: false,
    repair: false,
  });

  // 任务监控相关数据
  const taskLoading = ref(false);
  const currentTask = ref<any>(null);
  const taskHistory = ref<any[]>([]);
  const pollingTimer = ref<NodeJS.Timeout | null>(null);
  const processedTasks = ref<Set<string>>(new Set());
  const isPollingActive = ref(false);
  const pollingInterval = ref(3000);

  // 计算属性
  const searchSuccessRate = computed(() => {
    const rate = healthData.value.searchSuccessRate || 0;
    return (rate * 100).toFixed(1);
  });

  const indexSuccessRate = computed(() => {
    const rate = healthData.value.indexSuccessRate || 0;
    return (rate * 100).toFixed(1);
  });

  const lastCheckTime = computed(() => {
    if (!healthData.value.lastCheckTime) return '未知';
    const date = new Date(healthData.value.lastCheckTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  });

  const lastCheckTimeTooltip = computed(() => {
    if (!healthData.value.lastCheckTime) return '未知';
    const date = new Date(healthData.value.lastCheckTime);
    return `完整时间: ${date.toISOString()}`;
  });

  // 任务类型映射
  const taskTypeMap = {
    CREATE_INDEX: '创建索引',
    UPDATE_INDEX: '更新索引',
    UPDATE_NECESSARY_INDEX: '更新必要索引',
    REBUILD_INDEX: '重建索引',
    REBUILD_ALL_INDEX: '重建所有索引',
    REPAIR_INDEX: '修复索引',
    HEALTH_CHECK: '健康检查',
  };

  // 任务状态映射
  const taskStatusMap = {
    PENDING: '等待中',
    RUNNING: '运行中',
    COMPLETED: '已完成',
    FAILED: '失败',
    CANCELLED: '已取消',
  };

  // API调用方法
  const getHealthReport = () => {
    return defHttp.get({ url: '/summary/lucene/healthReport' }, { isTransformResponse: false });
  };

  const forceHealthCheck = () => {
    return defHttp.post({ url: '/summary/lucene/forceHealthCheck' }, { isTransformResponse: false });
  };

  const createIndexApi = () => {
    return defHttp.post({ url: '/summary/lucene/createIndex' }, { isTransformResponse: false });
  };

  const updateNecessaryIndexApi = () => {
    return defHttp.post({ url: '/summary/lucene/updateNecessaryIndex' }, { isTransformResponse: false });
  };

  const rebuildAllIndexApi = () => {
    return defHttp.post({ url: '/summary/lucene/rebuildAllIndex' }, { isTransformResponse: false });
  };

  const repairIndexApi = () => {
    return defHttp.post({ url: '/summary/lucene/repairIndex' }, { isTransformResponse: false });
  };

  // 方法实现
  const refreshHealth = async () => {
    loading.value = true;
    try {
      const result = await getHealthReport();
      if (result.success) {
        healthData.value = result.result || healthData.value;
        //message.success('健康报告已刷新');
      } else {
        message.error('获取健康报告失败: ' + result.message);
      }
    } catch (error) {
      message.error('获取健康报告异常');
      console.error('Health report error:', error);
    } finally {
      loading.value = false;
    }
  };

  const performHealthCheck = async () => {
    checkLoading.value = true;
    try {
      const result = await forceHealthCheck();
      if (result.success) {
        message.success('健康检查已执行');
        await refreshHealth();
      } else {
        message.error('健康检查失败: ' + result.message);
      }
    } catch (error) {
      message.error('健康检查异常');
      console.error('Health check error:', error);
    } finally {
      checkLoading.value = false;
    }
  };

  const handleOperation = async (operation: string, apiFunc: Function, successMsg: string, confirmMsg?: string) => {
    if (confirmMsg) {
      const confirmed = await new Promise((resolve) => {
        Modal.confirm({
          title: '确认操作',
          content: confirmMsg,
          onOk: () => resolve(true),
          onCancel: () => resolve(false),
        });
      });
      if (!confirmed) return;
    }

    operationLoading[operation] = true;
    try {
      const result = await apiFunc();
      if (result.success) {
        message.success(successMsg);
        await refreshHealth();
      } else {
        message.error(`操作失败: ${result.message}`);
      }
    } catch (error) {
      message.error('操作异常');
      console.error(`${operation} error:`, error);
    } finally {
      operationLoading[operation] = false;
    }
  };

  const handleCreateIndex = () => {
    handleOperation('create', createIndexApi, '索引创建任务已启动');
    startTaskMonitoring();
  };

  const handleUpdateIndex = () => {
    handleOperation('update', updateNecessaryIndexApi, '索引更新任务已启动');
    startTaskMonitoring();
  };

  const handleRebuildIndex = () => {
    handleOperation('rebuild', rebuildAllIndexApi, '索引重建任务已启动', '是否重建所有索引？此操作将清空并重建所有索引。');
    startTaskMonitoring();
  };

  const handleRepairIndex = () => {
    handleOperation('repair', repairIndexApi, '索引修复任务已启动');
    startTaskMonitoring();
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 0.95) return '#52c41a'; // 绿色 - 优秀
    if (rate >= 0.9) return '#1890ff'; // 蓝色 - 良好
    if (rate >= 0.8) return '#faad14'; // 橙色 - 一般
    if (rate >= 0.6) return '#fa8c16'; // 深橙 - 较差
    return '#f5222d'; // 红色 - 差
  };

  // 获取整体状态
  const getOverallStatusText = () => {
    if (!healthData.value?.indexExists) {
      return '未初始化';
    }
    if (!healthData.value?.healthy) {
      return '异常';
    }
    // 根据成功率判断状态
    const searchRate = healthData.value.searchSuccessRate || 0;
    const indexRate = healthData.value.indexSuccessRate || 0;
    const avgRate = (searchRate + indexRate) / 2;

    if (avgRate >= 0.95) return '优秀';
    if (avgRate >= 0.9) return '良好';
    if (avgRate >= 0.8) return '正常';
    return '需关注';
  };

  const getOverallStatusColor = () => {
    if (!healthData.value?.indexExists) {
      return '#8c8c8c'; // 灰色 - 未初始化
    }
    if (!healthData.value?.healthy) {
      return '#f5222d'; // 红色 - 异常
    }
    const searchRate = healthData.value.searchSuccessRate || 0;
    const indexRate = healthData.value.indexSuccessRate || 0;
    const avgRate = (searchRate + indexRate) / 2;
    return getSuccessRateColor(avgRate);
  };

  const getOverallStatusIcon = () => {
    if (!healthData.value?.indexExists) {
      return 'ant-design:question-circle-outlined';
    }
    if (!healthData.value?.healthy) {
      return 'ant-design:exclamation-circle-outlined';
    }
    const searchRate = healthData.value.searchSuccessRate || 0;
    const indexRate = healthData.value.indexSuccessRate || 0;
    const avgRate = (searchRate + indexRate) / 2;

    if (avgRate >= 0.95) return 'ant-design:check-circle-outlined';
    if (avgRate >= 0.8) return 'ant-design:info-circle-outlined';
    return 'ant-design:warning-outlined';
  };

  // 获取索引状态文本
  const getIndexStatusText = () => {
    if (!healthData.value?.indexExists) {
      return '未创建';
    }
    return healthData.value?.healthy ? '正常' : '异常';
  };

  // 获取索引状态颜色
  const getIndexStatusColor = () => {
    if (!healthData.value?.indexExists) {
      return '#f5222d'; // 红色 - 索引不存在
    }
    return healthData.value?.healthy ? '#52c41a' : '#f5222d'; // 绿色健康，红色异常
  };

  // 格式化数字显示
  const formatNumber = (num: number) => {
    if (num === undefined || num === null) return '0';
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // 格式化索引大小
  const formatIndexSize = (sizeInfo: string) => {
    if (!sizeInfo || sizeInfo === 'Unknown') {
      return '未知';
    }
    return sizeInfo;
  };

  // 获取成功次数
  const getSuccessCount = (type: 'search' | 'index') => {
    if (type === 'search') {
      const total = healthData.value.totalSearchCount || 0;
      const failed = healthData.value.failedSearchCount || 0;
      return Math.max(0, total - failed);
    } else {
      const total = healthData.value.totalIndexCount || 0;
      const failed = healthData.value.failedIndexCount || 0;
      return Math.max(0, total - failed);
    }
  };

  // 获取距离上次检查的时间
  const getTimeSinceLastCheck = () => {
    if (!healthData.value.lastCheckTime) return '未知';
    const now = Date.now();
    const lastCheck = healthData.value.lastCheckTime;
    const diff = now - lastCheck;

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天前`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  };

  // 任务监控工具方法
  const getTaskTypeName = (taskType: string) => {
    return taskTypeMap[taskType] || taskType;
  };

  const getTaskStatusName = (status: string) => {
    return taskStatusMap[status] || status;
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'blue';
      case 'RUNNING':
        return 'processing';
      case 'COMPLETED':
        return 'success';
      case 'FAILED':
        return 'error';
      case 'CANCELLED':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatTime = (timestamp: number) => {
    if (!timestamp) return '未知';
    return new Date(timestamp).toLocaleString();
  };

  const getRunningDuration = (startTime: number) => {
    if (!startTime) return '未知';
    const duration = Date.now() - startTime;
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  };

  // 任务状态刷新方法
  const refreshTaskStatus = async () => {
    taskLoading.value = true;
    try {
      const result = await getTaskStatus();
      if (result.success) {
        const taskStatus = result.result;

        if (taskStatus) {
          // 检查任务状态变化
          if (currentTask.value) {
            const prevStatus = currentTask.value.status;
            const newStatus = taskStatus.status;
            const prevTaskId = currentTask.value.taskId;
            const newTaskId = taskStatus.taskId;

            // 如果是同一个任务且状态发生变化
            if (prevTaskId === newTaskId && prevStatus !== newStatus) {
              const taskKey = `${taskStatus.taskId}_${newStatus}`;

              // 任务完成
              if (newStatus === 'COMPLETED') {
                onTaskCompleted(taskStatus);
                processedTasks.value.add(taskKey);
                // 添加到历史记录
                taskHistory.value.unshift({
                  ...taskStatus,
                  endTime: taskStatus.endTime || Date.now(),
                  duration: taskStatus.duration || Date.now() - taskStatus.startTime,
                });
              }

              // 任务失败
              if (newStatus === 'FAILED') {
                onTaskFailed(taskStatus);
                processedTasks.value.add(taskKey);
                // 添加到历史记录
                taskHistory.value.unshift({
                  ...taskStatus,
                  endTime: taskStatus.endTime || Date.now(),
                  duration: taskStatus.duration || Date.now() - taskStatus.startTime,
                });
              }

              // 限制历史记录数量
              if (taskHistory.value.length > 10) {
                taskHistory.value = taskHistory.value.slice(0, 10);
              }

              // 清理过期的已处理任务记录
              cleanupProcessedTasks();
            }
          } else {
            // 如果之前没有任务，但现在有已完成的任务，检查是否已处理过
            const taskKey = `${taskStatus.taskId}_${taskStatus.status}`;

            if (!processedTasks.value.has(taskKey)) {
              if (taskStatus.status === 'COMPLETED') {
                onTaskCompleted(taskStatus);
                processedTasks.value.add(taskKey);
                // 添加到历史记录
                taskHistory.value.unshift({
                  ...taskStatus,
                  endTime: taskStatus.endTime || Date.now(),
                  duration: taskStatus.duration || Date.now() - taskStatus.startTime,
                });
              } else if (taskStatus.status === 'FAILED') {
                onTaskFailed(taskStatus);
                processedTasks.value.add(taskKey);
                // 添加到历史记录
                taskHistory.value.unshift({
                  ...taskStatus,
                  endTime: taskStatus.endTime || Date.now(),
                  duration: taskStatus.duration || Date.now() - taskStatus.startTime,
                });
              }
            }
          }

          // 更新当前任务状态
          if (taskStatus.status === 'PENDING' || taskStatus.status === 'RUNNING') {
            currentTask.value = taskStatus;
            // 有正在运行的任务，确保轮询是活跃的
            if (!isPollingActive.value) {
              startActivePolling();
            }
          } else {
            // 已完成或失败的任务不显示在当前任务中
            currentTask.value = null;
            // 没有正在运行的任务，切换到慢速轮询
            if (isPollingActive.value) {
              startSlowPolling();
            }
          }
        } else {
          currentTask.value = null;
          // 没有任务，切换到慢速轮询
          if (isPollingActive.value) {
            startSlowPolling();
          }
        }
      } else {
        currentTask.value = null;
      }
    } catch (error) {
      console.error('获取任务状态失败:', error);
    } finally {
      taskLoading.value = false;
    }
  };

  // 轮询控制方法
  const startActivePolling = () => {
    stopPolling();
    isPollingActive.value = true;
    pollingInterval.value = 3000; // 3秒间隔

    pollingTimer.value = setInterval(() => {
      refreshTaskStatus();
    }, pollingInterval.value);
  };

  const startSlowPolling = () => {
    stopPolling();
    isPollingActive.value = false;
    pollingInterval.value = 30000; // 30秒间隔

    pollingTimer.value = setInterval(() => {
      refreshTaskStatus();
    }, pollingInterval.value);
  };

  const startPolling = () => {
    // 立即执行一次
    refreshTaskStatus();

    // 默认开始慢速轮询，如果有任务会自动切换到活跃轮询
    startSlowPolling();
  };

  const stopPolling = () => {
    if (pollingTimer.value) {
      clearInterval(pollingTimer.value);
      pollingTimer.value = null;
    }
    isPollingActive.value = false;
  };

  const cleanupProcessedTasks = () => {
    if (processedTasks.value.size > 50) {
      const tasksArray = Array.from(processedTasks.value);
      const recentTasks = tasksArray.slice(-30); // 保留最近30个
      processedTasks.value = new Set(recentTasks);
    }
  };

  // 启动任务监控
  const startTaskMonitoring = () => {
    startPolling();
  };

  // 组件挂载时获取健康报告
  onMounted(() => {
    refreshHealth();
  });

  // 组件卸载时停止轮询
  onUnmounted(() => {
    stopPolling();
  });

  // 任务完成回调
  const onTaskCompleted = (taskStatus) => {
    message.success(`任务执行完成: ${taskStatus.message}`);
    refreshHealth(); // 刷新健康状态
  };

  // 任务失败回调
  const onTaskFailed = (taskStatus) => {
    message.error(`任务执行失败: ${taskStatus.message}`);
    refreshHealth(); // 刷新健康状态
  };

  // 暴露方法给父组件
  defineExpose({
    refreshHealth,
  });
</script>

<style scoped>
  /* 头部操作区域 */
  .health-card-header {
    margin-bottom: 16px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  /* 指标卡片样式 */
  .metrics-card {
    background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
    border: 1px solid #e8f4fd;
    border-radius: 8px;
  }

  .metrics-card :deep(.ant-card-body) {
    padding: 16px;
  }

  /* 指标项样式 */
  .metric-item {
    text-align: center;
    padding: 8px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
  }

  .metric-item:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .metric-subtitle {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 4px;
  }

  /* 信息卡片样式 */
  .info-card {
    height: 100%;
    border-radius: 6px;
    transition: all 0.3s ease;
  }

  .info-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .info-card :deep(.ant-card-head) {
    padding: 8px 12px;
    min-height: auto;
  }

  .info-card :deep(.ant-card-head-title) {
    font-size: 13px;
    font-weight: 600;
  }

  .info-card :deep(.ant-card-body) {
    padding: 12px;
  }

  /* 操作卡片样式 */
  .operation-card {
    border-radius: 6px;
  }

  .operation-card :deep(.ant-card-head) {
    padding: 8px 12px;
    min-height: auto;
  }

  .operation-card :deep(.ant-card-body) {
    padding: 12px;
  }

  /* 统计数字样式 */
  .number-highlight {
    font-weight: 600;
    color: #1890ff;
  }

  .size-info {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    color: #722ed1;
  }

  .stat-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .success-count {
    color: #52c41a;
    font-weight: 600;
  }

  .error-count {
    color: #f5222d;
    font-weight: 600;
  }

  /* 统计组件样式优化 */
  :deep(.ant-statistic-title) {
    font-size: 12px;
    color: #8c8c8c;
    margin-bottom: 4px;
  }

  :deep(.ant-statistic-content) {
    font-size: 14px;
  }

  :deep(.ant-statistic-content-value) {
    font-weight: 600;
  }

  /* 描述列表样式 */
  :deep(.ant-descriptions-item-label) {
    font-weight: 500;
    color: #595959;
    font-size: 12px;
  }

  :deep(.ant-descriptions-item-content) {
    font-size: 12px;
  }

  /* 任务监控样式 */
  :deep(.ant-list-item-meta-title) {
    margin-bottom: 4px;
  }

  :deep(.ant-list-item-meta-description) {
    font-size: 12px;
    color: #666;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .health-card-header {
      text-align: center;
    }

    .metric-item {
      margin-bottom: 8px;
    }

    :deep(.ant-col) {
      margin-bottom: 8px;
    }
  }

  /* 加载状态样式 */
  :deep(.ant-btn-loading-icon) {
    margin-right: 4px;
  }

  /* 标签样式优化 */
  :deep(.ant-tag) {
    border-radius: 4px;
    font-size: 11px;
  }

  /* 进度条样式 */
  :deep(.ant-progress-inner) {
    border-radius: 4px;
  }
</style>
