<template>
  <a-spin :spinning="confirmLoading">
    <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-row>
        <a-col :span="24">
          <a-form-item label="排序号" v-bind="validateInfos.sort">
            <a-input v-model:value="formData.sort" :rows="4" placeholder="请输入排序号" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="关键字" v-bind="validateInfos.keywords">
            <a-textarea v-model:value="formData.keywords" :rows="4" placeholder="请输入关键字" :disabled="disabled" @change="setHelpChar" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="总检建议" v-bind="validateInfos.adviceContent">
            <a-textarea v-model:value="formData.adviceContent" :rows="4" placeholder="请输入总检建议" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="助记码" v-bind="validateInfos.helpChar">
            <a-input v-model:value="formData.helpChar" placeholder="请输入助记码" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <!--        <a-col :span="24">
          <a-form-item
            label="自动匹配脚本"
            v-bind="validateInfos.groovyExpression"
            extra="注意：表达式优先级最高！表达式求值后为true，将匹配该条参考结果。可用变量：departConclusion（科室小结），示例：def regex = /^(无明显异常|未发现异常|正常)$/;
return (departConclusion ==~ regex);"
          >
            <j-code-editor v-model:value="formData.groovyExpression" :disabled="disabled" />
            <a-button size="small" @click="testExpression" style="margin-top: 5px">测试表达式</a-button>
          </a-form-item>
        </a-col>-->
        <a-col :span="24">
          <a-form-item label="适用性别" v-bind="validateInfos.sexLimit">
            <j-dict-select-tag v-model:value="formData.sexLimit" dictCode="sexLimit" placeholder="请选择适用性别" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="适用婚别" v-bind="validateInfos.marriageLimit">
            <j-dict-select-tag v-model:value="formData.marriageLimit" dictCode="material_type" placeholder="请选择适用婚别" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="最小年龄" v-bind="validateInfos.minAge">
            <a-input-number v-model:value="formData.minAge" placeholder="请输入最小年龄" style="width: 100%" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="最大年龄" v-bind="validateInfos.maxAge">
            <a-input-number v-model:value="formData.maxAge" placeholder="请输入最大年龄" style="width: 100%" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="岗位" v-bind="validateInfos.jobCategory">
            <a-input v-model:value="formData.jobCategory" placeholder="请输入岗位" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="危害因素" v-bind="validateInfos.riskFactor">
            <j-async-search-select
              placeholder="请选择危害因素"
              v-model:value="formData.riskFactor"
              dict="zy_risk_factor,name,code"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <!--         <a-col :span="24">
          <a-form-item label="疾病程度" v-bind="validateInfos.diseaseSeverity">
            <j-dict-select-tag v-model:value="formData.diseaseSeverity" dictCode="disease_grade" placeholder="请选择疾病程度" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="疾病类型" v-bind="validateInfos.diseaseType">
            <j-dict-select-tag v-model:value="formData.diseaseType" dictCode="disease_type" placeholder="请选择疾病类型" :disabled="disabled" />
          </a-form-item>
        </a-col>

       <a-col :span="24">
          <a-form-item label="团报隐藏" v-bind="validateInfos.teamHiddenFlag">
            <j-switch v-model:value="formData.teamHiddenFlag" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="疾病" v-bind="validateInfos.idcCode">
            <j-async-search-select v-model:value="formData.idcCode" dict="icd,code~assist_code,name" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="随访周期" v-bind="validateInfos.followUpPeriod">
            <a-input-number v-model:value="formData.followUpPeriod" placeholder="请输入随访周期" style="width: 100%" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="复查周期" v-bind="validateInfos.reviewPeriod">
            <a-input-number v-model:value="formData.reviewPeriod" placeholder="请输入复查周期" style="width: 100%" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="专科建议" v-bind="validateInfos.departmentAdvice">
            <a-textarea v-model:value="formData.departmentAdvice" :rows="4" placeholder="请输入专科建议" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="团体建议" v-bind="validateInfos.teamAdvice">
            <a-textarea v-model:value="formData.teamAdvice" :rows="4" placeholder="请输入团体建议" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="就医指导" v-bind="validateInfos.medicalAdvice">
            <a-textarea v-model:value="formData.medicalAdvice" :rows="4" placeholder="请输入就医指导" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="饮食指导" v-bind="validateInfos.dietAdvice">
            <a-textarea v-model:value="formData.dietAdvice" :rows="4" placeholder="请输入饮食指导" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="健康指导" v-bind="validateInfos.healthAdvice">
            <a-textarea v-model:value="formData.healthAdvice" :rows="4" placeholder="请输入健康指导" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="阳性状态" v-bind="validateInfos.positiveFlag">
            <j-switch v-model:value="formData.positiveFlag" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="生活相关" v-bind="validateInfos.lifeRelatedFlag">
            <j-switch v-model:value="formData.lifeRelatedFlag" :options="[1, 0]" :disabled="disabled" />
          </a-form-item>
        </a-col>-->
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import { computed, defineExpose, defineProps, inject, nextTick, reactive, ref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { getValueType } from '/@/utils';
  import { getNextSortNum, saveOrUpdate, testGroovyExpression } from '../SummaryAdvice.api';
  import { Form } from 'ant-design-vue';
  import { duplicateValidate } from '/@/utils/helper/validator';
  import { JAsyncSearchSelect } from '@/components/Form';

  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => {} },
    formBpm: { type: Boolean, default: true },
  });
  const departmentId4SummaryAdvice = inject('departmentId4SummaryAdvice');
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    sort: '',
    riskFactor: '',
    sexLimit: '不限',
    keywords: '',
    helpChar: '',
    groovyExpression: '',
    marriageLimit: '不限',
    minAge: 0,
    maxAge: 120,
    jobCategory: '',
    diseaseSeverity: '',
    diseaseType: '',
    adviceContent: '',
    teamHiddenFlag: 0,
    idcCode: '',
    followUpPeriod: undefined,
    departmentAdvice: '',
    teamAdvice: '',
    medicalAdvice: '',
    dietAdvice: '',
    healthAdvice: '',
    reviewPeriod: undefined,
    positiveFlag: undefined,
    lifeRelatedFlag: undefined,
    departmentId: '',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    keywords: [{ required: true, message: '请输入关键字!' }],
    marriageLimit: [{ required: true, message: '请输入适用婚别!' }],
    minAge: [{ required: true, message: '请输入最小年龄!' }],
    maxAge: [{ required: true, message: '请输入最大年龄!' }],
    adviceContent: [{ required: true, message: '请输入总检建议!' }],
    /*    groovyExpression: [
      {
        validator: async (_, value) => {
          if (value) {
            // 发送请求到服务器，检查Groovy表达式是否有效
            const response = await testGroovyExpression({ expression: value });
            if (response.success) {
              return Promise.resolve();
            } else {
              return Promise.reject('Groovy表达式无效!');
            }
          }
        },
        trigger: 'change',
      },
    ],*/
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  function setHelpChar() {
    if (formData.keywords) {
      //formData.helpChar = PinyinUtil.getInitials(formData.keywords);
    }
  }

  function testExpression() {
    testGroovyExpression({ expression: formData.groovyExpression }).then((res) => {
      if (res.success) {
        createMessage.success(res.message);
      } else {
        createMessage.error(res.message);
      }
    });
  }

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      //赋值
      Object.assign(formData, record);
      if (!record.id) {
        getNextSortNum().then((res) => {
          formData.sort = res;
        });
      }
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  async function keywordsDuplicatevalidate(_r, value) {
    return duplicateValidate('summary_advice', 'keywords', value, formData.id || '');
  }
  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    height: 500px !important;
    overflow-y: auto;
    padding: 14px;
  }
</style>
