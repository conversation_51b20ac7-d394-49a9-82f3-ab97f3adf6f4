<template>
  <div class="diagnosis-review-editor">
    <div class="editor-header">
      <div class="header-line-number">序号</div>
      <div class="header-content">诊断内容</div>
      <div class="header-actions">
        <span class="header-action-item">标注</span>
      </div>
    </div>

    <div class="editor-body">
      <div v-if="diagnosisList.length === 0" class="empty-state">
        <a-empty description="暂无诊断内容" />
      </div>

      <div v-else>
        <div v-for="(diagnosis, index) in diagnosisList" :key="index" class="diagnosis-item" :class="{ 'has-annotation': hasAnnotation(index) }">
          <div class="line-number">
            <span class="number-badge">{{ index + 1 }}</span>
            <a-badge v-if="hasAnnotation(index)" :count="getAnnotationCount(index)" :number-style="{ backgroundColor: getAnnotationColor(index) }" />
          </div>

          <div class="diagnosis-content" :data-index="index">
            <div
              class="content-text"
              @mouseup="handleTextSelection($event, index)"
              @contextmenu="handleRightClick($event, index)"
              v-html="renderContentWithAnnotations(diagnosis, index)"
            ></div>

            <!-- 显示该行的标注 -->
            <div v-if="getItemAnnotations(index).length > 0" class="annotations-list">
              <div
                v-for="annotation in getItemAnnotations(index)"
                :key="annotation.id"
                class="annotation-item"
                :class="`annotation-${annotation.severity}`"
              >
                <a-tag
                  :color="getAnnotationTagColor(annotation.type)"
                  size="small"
                  closable
                  @close="removeAnnotation(annotation.id)"
                  v-if="!readOnly"
                >
                  {{ getAnnotationTypeText(annotation.type) }}
                </a-tag>
                <a-tag :color="getAnnotationTagColor(annotation.type)" size="small" v-else>
                  {{ getAnnotationTypeText(annotation.type) }}
                </a-tag>
                <span class="annotation-content">{{ annotation.content }}</span>
                <span class="annotation-meta">
                  {{ annotation.createTime ? new Date(annotation.createTime).toLocaleString() : '' }}
                </span>
              </div>
            </div>
          </div>

          <div class="diagnosis-actions">
            <a-dropdown :trigger="['click']" v-if="!readOnly">
              <span class="action-icon more-actions">
                <EllipsisOutlined />
              </span>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="addAnnotation(index)"> <TagOutlined /> 添加标注 </a-menu-item>
                  <a-menu-item @click="addAnnotationFromSelection(index)" :disabled="!hasSelection"> <HighlightOutlined /> 标注选中文本 </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>

            <span v-else class="readonly-indicator">
              <EyeOutlined />
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <a-dropdown :open="contextMenuVisible" :trigger="[]" @openChange="handleContextMenuChange">
      <div
        :style="{
          position: 'fixed',
          left: contextMenuPosition.x + 'px',
          top: contextMenuPosition.y + 'px',
          zIndex: 9999,
        }"
      ></div>
      <template #overlay>
        <a-menu @click="handleContextMenuClick">
          <a-menu-item key="annotate" :disabled="readOnly"> <TagOutlined /> 添加标注 </a-menu-item>
          <a-menu-item key="annotateSelection" :disabled="readOnly || !hasSelection"> <HighlightOutlined /> 标注选中文本 </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { EllipsisOutlined, TagOutlined, HighlightOutlined, EyeOutlined } from '@ant-design/icons-vue';

  interface Props {
    diagnosisList?: string[];
    annotations?: any[];
    readOnly?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    diagnosisList: () => [],
    annotations: () => [],
    readOnly: false,
  });

  const emit = defineEmits(['add-annotation', 'remove-annotation']);

  // 状态管理
  const selectedText = ref('');
  const selectedRange = ref<{ start: number; end: number; itemIndex: number } | null>(null);
  const contextMenuVisible = ref(false);
  const contextMenuPosition = ref({ x: 0, y: 0 });
  const currentItemIndex = ref(-1);

  // 计算属性
  const hasSelection = computed(() => selectedText.value.length > 0);

  // 方法
  const hasAnnotation = (itemIndex: number) => {
    return props.annotations.some((annotation) => annotation.itemIndex === itemIndex);
  };

  const getAnnotationCount = (itemIndex: number) => {
    return props.annotations.filter((annotation) => annotation.itemIndex === itemIndex).length;
  };

  const getAnnotationColor = (itemIndex: number) => {
    const annotations = props.annotations.filter((annotation) => annotation.itemIndex === itemIndex);
    if (annotations.some((a) => a.severity === 'high')) return '#ff4d4f';
    if (annotations.some((a) => a.severity === 'medium')) return '#faad14';
    return '#52c41a';
  };

  const getItemAnnotations = (itemIndex: number) => {
    return props.annotations.filter((annotation) => annotation.itemIndex === itemIndex);
  };

  const getAnnotationTypeText = (type: string) => {
    const typeMap = {
      error: '错误',
      warning: '警告',
      suggestion: '建议',
    };
    return typeMap[type] || type;
  };

  const getAnnotationTagColor = (type: string) => {
    const colorMap = {
      error: 'red',
      warning: 'orange',
      suggestion: 'blue',
    };
    return colorMap[type] || 'default';
  };

  // 渲染带标注的内容
  const renderContentWithAnnotations = (content: string, itemIndex: number) => {
    if (!content) return '';

    const itemAnnotations = getItemAnnotations(itemIndex);
    if (itemAnnotations.length === 0) return content;

    let result = content;

    // 按位置排序，从后往前处理避免位置偏移
    const sortedAnnotations = itemAnnotations
      .filter((a) => a.startIndex !== undefined && a.endIndex !== undefined)
      .sort((a, b) => b.startIndex - a.startIndex);

    sortedAnnotations.forEach((annotation) => {
      const before = result.substring(0, annotation.startIndex);
      const annotated = result.substring(annotation.startIndex, annotation.endIndex);
      const after = result.substring(annotation.endIndex);

      const className = `annotation-highlight annotation-${annotation.severity}`;
      result = before + `<span class="${className}" title="${annotation.content}">${annotated}</span>` + after;
    });

    return result;
  };

  // 文本选择处理
  const handleTextSelection = (event: MouseEvent, itemIndex: number) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      selectedText.value = '';
      selectedRange.value = null;
      return;
    }

    const range = selection.getRangeAt(0);
    const text = range.toString().trim();

    if (text.length > 0) {
      selectedText.value = text;
      selectedRange.value = {
        start: range.startOffset,
        end: range.endOffset,
        itemIndex,
      };
    } else {
      selectedText.value = '';
      selectedRange.value = null;
    }
  };

  // 右键菜单处理
  const handleRightClick = (event: MouseEvent, itemIndex: number) => {
    if (props.readOnly) return;

    event.preventDefault();
    currentItemIndex.value = itemIndex;
    contextMenuPosition.value = { x: event.clientX, y: event.clientY };
    contextMenuVisible.value = true;
  };

  const handleContextMenuChange = (visible: boolean) => {
    contextMenuVisible.value = visible;
  };

  const handleContextMenuClick = ({ key }: { key: string }) => {
    contextMenuVisible.value = false;

    if (key === 'annotate') {
      addAnnotation(currentItemIndex.value);
    } else if (key === 'annotateSelection') {
      addAnnotationFromSelection(currentItemIndex.value);
    }
  };

  // 标注操作
  const addAnnotation = (itemIndex: number) => {
    const annotationData = {
      itemIndex,
      position: `诊断第${itemIndex + 1}行`,
      selectedText: '',
      startIndex: 0,
      endIndex: 0,
    };

    emit('add-annotation', annotationData);
  };

  const addAnnotationFromSelection = (itemIndex: number) => {
    if (!selectedRange.value || selectedText.value.length === 0) return;

    const annotationData = {
      itemIndex,
      position: `诊断第${itemIndex + 1}行`,
      selectedText: selectedText.value,
      startIndex: selectedRange.value.start,
      endIndex: selectedRange.value.end,
    };

    emit('add-annotation', annotationData);

    // 清除选择
    selectedText.value = '';
    selectedRange.value = null;
    window.getSelection()?.removeAllRanges();
  };

  const removeAnnotation = (annotationId: string) => {
    emit('remove-annotation', annotationId);
  };
</script>

<style scoped>
  .diagnosis-review-editor {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .editor-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-bottom: none;
    font-weight: 600;
    font-size: 12px;
  }

  .header-line-number {
    width: 60px;
    text-align: center;
  }

  .header-content {
    flex: 1;
    padding-left: 12px;
  }

  .header-actions {
    width: 80px;
    text-align: center;
  }

  .editor-body {
    flex: 1;
    border: 1px solid #d9d9d9;
    overflow-y: auto;
  }

  .diagnosis-item {
    display: flex;
    align-items: flex-start;
    min-height: 50px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
  }

  .diagnosis-item:hover {
    background-color: #fafafa;
  }

  .diagnosis-item.has-annotation {
    background-color: #fff7e6;
    border-left: 3px solid #faad14;
  }

  .line-number {
    width: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    position: relative;
  }

  .number-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: #f0f0f0;
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
    color: #666;
  }

  .diagnosis-content {
    flex: 1;
    padding: 12px;
  }

  .content-text {
    line-height: 1.6;
    user-select: text;
    cursor: text;
    font-size: 14px;
  }

  .annotations-list {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
  }

  .annotation-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    padding: 4px 8px;
    background-color: #f9f9f9;
    border-radius: 4px;
    font-size: 12px;
  }

  .annotation-content {
    flex: 1;
    color: #666;
  }

  .annotation-meta {
    color: #999;
    font-size: 11px;
  }

  .diagnosis-actions {
    width: 80px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 12px 8px;
  }

  .action-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .action-icon:hover {
    background-color: #f0f0f0;
  }

  .readonly-indicator {
    color: #999;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  /* 标注高亮样式 */
  :deep(.annotation-highlight) {
    padding: 2px 4px;
    border-radius: 2px;
    cursor: help;
  }

  :deep(.annotation-highlight.annotation-high) {
    background-color: #ffebee;
    border: 1px solid #ff4d4f;
  }

  :deep(.annotation-highlight.annotation-medium) {
    background-color: #fff7e6;
    border: 1px solid #faad14;
  }

  :deep(.annotation-highlight.annotation-low) {
    background-color: #f6ffed;
    border: 1px solid #52c41a;
  }
</style>
