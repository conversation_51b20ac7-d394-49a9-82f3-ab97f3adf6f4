<template>
  <div class="diagnosis-editor-enhanced">
    <div class="editor-header">
      <div class="header-title">
        <MedicineBoxOutlined style="margin-right: 8px" />
        诊断编辑器
      </div>
      <div class="header-actions">
        <a-button size="small" type="primary" @click="addDiagnosis" :disabled="readOnly">
          <PlusOutlined />
          添加诊断
        </a-button>
      </div>
    </div>

    <div class="diagnosis-list">
      <div v-if="diagnosisList.length === 0" class="empty-state">
        <a-empty description="暂无诊断内容" />
      </div>

      <div v-else>
        <div
          v-for="(diagnosis, index) in diagnosisList"
          :key="index"
          class="diagnosis-item"
          :class="{
            'has-annotation': hasAnnotation(index),
            'has-review': hasReviewInfo(index),
          }"
        >
          <div class="item-header">
            <div class="item-number">
              <span class="number-badge">{{ index + 1 }}</span>
              <!-- 标注指示器 -->
              <a-badge
                v-if="hasAnnotation(index)"
                :count="getAnnotationCount(index)"
                :number-style="{ backgroundColor: getAnnotationColor(index) }"
              />
              <!-- 审阅状态指示器 -->
              <div v-if="hasReviewInfo(index)" class="review-status-indicator">
                <a-tooltip :title="getReviewStatusText(diagnosis.reviewInfo?.reviewStatus)">
                  <span class="review-status-icon" :class="`status-${diagnosis.reviewInfo?.reviewStatus}`">
                    <CheckCircleOutlined v-if="diagnosis.reviewInfo?.reviewStatus === 'corrected'" />
                    <ExclamationCircleOutlined v-else-if="diagnosis.reviewInfo?.reviewStatus === 'reviewed'" />
                    <ClockCircleOutlined v-else />
                  </span>
                </a-tooltip>
              </div>
            </div>

            <div class="item-actions" v-if="!readOnly">
              <a-button size="small" type="text" @click="removeDiagnosis(index)">
                <DeleteOutlined />
              </a-button>
            </div>
          </div>

          <div class="item-content">
            <!-- 诊断内容输入 -->
            <div class="diagnosis-input">
              <a-textarea
                v-model:value="diagnosis.content"
                placeholder="请输入诊断内容..."
                :rows="2"
                :disabled="readOnly"
                @change="handleDiagnosisChange(index)"
              />
            </div>

            <!-- 显示护士标注 -->
            <div v-if="showAnnotations && getItemAnnotations(index).length > 0" class="nurse-annotations">
              <div class="annotations-header">
                <ExclamationCircleOutlined style="color: #faad14; margin-right: 4px" />
                <span class="annotations-title">护士标注</span>
              </div>
              <div
                v-for="annotation in getItemAnnotations(index)"
                :key="annotation.id"
                class="annotation-item"
                :class="`annotation-${annotation.severity}`"
              >
                <div class="annotation-header">
                  <a-tag :color="getAnnotationTagColor(annotation.type)" size="small">
                    {{ getAnnotationTypeText(annotation.type) }}
                  </a-tag>
                  <a-tag :color="getSeverityColor(annotation.severity)" size="small">
                    {{ getSeverityText(annotation.severity) }}
                  </a-tag>
                  <span class="annotation-time">{{ formatTime(annotation.createTime) }}</span>
                </div>
                <div class="annotation-content">{{ annotation.content }}</div>
                <div v-if="annotation.selectedText" class="annotation-selected-text">
                  <span class="selected-label">选中文本：</span>
                  <span class="selected-text">{{ annotation.selectedText }}</span>
                </div>
                <!-- 处理状态和操作 -->
                <div class="annotation-actions" v-if="!readOnly && annotation.status === 'pending'">
                  <a-button size="small" type="link" @click="handleAnnotation(annotation, 'handled')"> 标记已处理 </a-button>
                  <a-button size="small" type="link" @click="showHandleModal(annotation)"> 添加处理意见 </a-button>
                </div>
                <div v-if="annotation.status === 'handled'" class="annotation-handled">
                  <a-tag color="green" size="small">已处理</a-tag>
                  <span v-if="annotation.handleComment" class="handle-comment">{{ annotation.handleComment }}</span>
                  <span class="handle-time">{{ formatTime(annotation.handleTime) }}</span>
                </div>
              </div>
            </div>

            <!-- 显示审阅信息 -->
            <div v-if="hasReviewInfo(index)" class="review-info">
              <div class="review-header">
                <AuditOutlined style="color: #1890ff; margin-right: 4px" />
                <span class="review-title">审阅信息</span>
                <a-tag :color="getReviewStatusColor(diagnosis.reviewInfo?.reviewStatus)" size="small">
                  {{ getReviewStatusText(diagnosis.reviewInfo?.reviewStatus) }}
                </a-tag>
              </div>

              <div class="review-details">
                <div class="review-meta">
                  <span class="reviewer">审阅人：{{ diagnosis.reviewInfo?.reviewer }}</span>
                  <span class="review-time">时间：{{ formatTime(diagnosis.reviewInfo?.reviewTime) }}</span>
                </div>

                <div v-if="diagnosis.reviewInfo?.reviewComment" class="review-comment">
                  <div class="comment-label">审阅意见：</div>
                  <div class="comment-content">{{ diagnosis.reviewInfo.reviewComment }}</div>
                </div>

                <div v-if="diagnosis.reviewInfo?.correctedContent" class="corrected-content">
                  <div class="corrected-label">更正内容：</div>
                  <div class="corrected-text">{{ diagnosis.reviewInfo.correctedContent }}</div>
                  <a-button size="small" type="link" @click="applyCorrectedContent(index)"> 应用更正 </a-button>
                </div>

                <div v-if="diagnosis.reviewInfo?.annotations?.length > 0" class="review-annotations">
                  <div class="annotations-count"> 标注数量：{{ diagnosis.reviewInfo.annotations.length }} </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import {
    MedicineBoxOutlined,
    PlusOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    AuditOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  interface DiagnosisItem {
    content: string;
    reviewInfo?: {
      hasReview: boolean;
      reviewStatus: 'pending' | 'reviewed' | 'corrected';
      annotations: any[];
      correctedContent?: string;
      reviewTime?: string;
      reviewer?: string;
      reviewComment?: string;
    };
  }

  interface Props {
    modelValue?: string[];
    nurseAnnotations?: any[];
    showAnnotations?: boolean;
    readOnly?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    nurseAnnotations: () => [],
    showAnnotations: false,
    readOnly: false,
  });

  const emit = defineEmits(['update:modelValue', 'change']);

  // 转换为内部数据结构
  const diagnosisList = ref<DiagnosisItem[]>([]);

  // 监听外部数据变化
  watch(
    () => props.modelValue,
    (newVal) => {
      diagnosisList.value = newVal.map((content) => ({
        content,
        reviewInfo: {
          hasReview: false,
          reviewStatus: 'pending',
          annotations: [],
        },
      }));
    },
    { immediate: true }
  );

  // 标注相关方法
  const hasAnnotation = (itemIndex: number) => {
    return props.nurseAnnotations.some((annotation) => annotation.itemIndex === itemIndex);
  };

  const getAnnotationCount = (itemIndex: number) => {
    return props.nurseAnnotations.filter((annotation) => annotation.itemIndex === itemIndex).length;
  };

  const getAnnotationColor = (itemIndex: number) => {
    const annotations = props.nurseAnnotations.filter((annotation) => annotation.itemIndex === itemIndex);
    if (annotations.some((a) => a.severity === 'high')) return '#ff4d4f';
    if (annotations.some((a) => a.severity === 'medium')) return '#faad14';
    return '#52c41a';
  };

  const getItemAnnotations = (itemIndex: number) => {
    return props.nurseAnnotations.filter((annotation) => annotation.itemIndex === itemIndex);
  };

  const getAnnotationTypeText = (type: string) => {
    const typeMap = {
      error: '错误',
      warning: '警告',
      suggestion: '建议',
    };
    return typeMap[type] || type;
  };

  const getAnnotationTagColor = (type: string) => {
    const colorMap = {
      error: 'red',
      warning: 'orange',
      suggestion: 'blue',
    };
    return colorMap[type] || 'default';
  };

  const getSeverityColor = (severity: string) => {
    const colorMap = {
      high: 'red',
      medium: 'orange',
      low: 'green',
    };
    return colorMap[severity] || 'default';
  };

  const getSeverityText = (severity: string) => {
    const textMap = {
      high: '严重',
      medium: '中等',
      low: '轻微',
    };
    return textMap[severity] || severity;
  };

  const formatTime = (time: string) => {
    return time ? new Date(time).toLocaleString() : '';
  };

  // 审阅相关方法
  const hasReviewInfo = (itemIndex: number) => {
    return diagnosisList.value[itemIndex]?.reviewInfo?.hasReview || false;
  };

  const getReviewStatusText = (status?: string) => {
    const statusMap = {
      pending: '待审阅',
      reviewed: '已审阅',
      corrected: '已更正',
    };
    return statusMap[status] || '未知状态';
  };

  const getReviewStatusColor = (status?: string) => {
    const colorMap = {
      pending: 'orange',
      reviewed: 'blue',
      corrected: 'green',
    };
    return colorMap[status] || 'default';
  };

  // 诊断操作方法
  const addDiagnosis = () => {
    diagnosisList.value.push({
      content: '',
      reviewInfo: {
        hasReview: false,
        reviewStatus: 'pending',
        annotations: [],
      },
    });
    updateModelValue();
  };

  const removeDiagnosis = (index: number) => {
    diagnosisList.value.splice(index, 1);
    updateModelValue();
  };

  const handleDiagnosisChange = (index: number) => {
    updateModelValue();
  };

  const updateModelValue = () => {
    const values = diagnosisList.value.map((item) => item.content);
    emit('update:modelValue', values);
    emit('change', values);
  };

  // 处理标注
  const handleAnnotation = async (annotation: any, action: string) => {
    try {
      annotation.status = action;
      annotation.handleTime = new Date().toISOString();
      annotation.handleBy = '当前用户';
      message.success('标注处理成功');
    } catch (error) {
      message.error('标注处理失败');
    }
  };

  const showHandleModal = (annotation: any) => {
    const comment = prompt('请输入处理意见：');
    if (comment) {
      annotation.handleComment = comment;
      handleAnnotation(annotation, 'handled');
    }
  };

  const applyCorrectedContent = (index: number) => {
    const item = diagnosisList.value[index];
    if (item?.reviewInfo?.correctedContent) {
      item.content = item.reviewInfo.correctedContent;
      item.reviewInfo.reviewStatus = 'corrected';
      updateModelValue();
      message.success('已应用更正内容');
    }
  };
</script>

<style scoped>
  .diagnosis-editor-enhanced {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #fff;
  }

  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #d9d9d9;
    border-radius: 6px 6px 0 0;
  }

  .header-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
  }

  .diagnosis-list {
    padding: 16px;
  }

  .diagnosis-item {
    margin-bottom: 16px;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .diagnosis-item:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .diagnosis-item.has-annotation {
    background-color: #fff7e6;
    border-left: 3px solid #faad14;
  }

  .diagnosis-item.has-review {
    background-color: #f0f9ff;
    border-left: 3px solid #1890ff;
  }

  .diagnosis-item.has-annotation.has-review {
    background: linear-gradient(to right, #fff7e6 50%, #f0f9ff 50%);
    border-left: 3px solid #722ed1;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .item-number {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .number-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: #f0f0f0;
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
    color: #666;
  }

  .review-status-indicator {
    display: flex;
    align-items: center;
  }

  .review-status-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    font-size: 12px;
  }

  .review-status-icon.status-pending {
    color: #faad14;
    background-color: #fff7e6;
  }

  .review-status-icon.status-reviewed {
    color: #1890ff;
    background-color: #e6f7ff;
  }

  .review-status-icon.status-corrected {
    color: #52c41a;
    background-color: #f6ffed;
  }

  .diagnosis-input {
    margin-bottom: 8px;
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #999;
  }

  /* 护士标注样式 */
  .nurse-annotations {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .annotations-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
    color: #666;
  }

  .annotations-title {
    color: #faad14;
  }

  .annotation-item {
    margin-bottom: 8px;
    padding: 8px;
    background-color: #fff;
    border-radius: 4px;
    border-left: 3px solid #e8e8e8;
  }

  .annotation-item.annotation-high {
    border-left-color: #ff4d4f;
  }

  .annotation-item.annotation-medium {
    border-left-color: #faad14;
  }

  .annotation-item.annotation-low {
    border-left-color: #52c41a;
  }

  .annotation-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  }

  .annotation-time {
    font-size: 11px;
    color: #999;
    margin-left: auto;
  }

  .annotation-content {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
  }

  .annotation-selected-text {
    margin-top: 4px;
    font-size: 12px;
  }

  .selected-label {
    color: #999;
  }

  .selected-text {
    color: #666;
    background-color: #f0f0f0;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: monospace;
  }

  .annotation-actions {
    margin-top: 4px;
    display: flex;
    gap: 8px;
  }

  .annotation-handled {
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: #666;
  }

  .handle-comment {
    color: #333;
  }

  .handle-time {
    color: #999;
  }

  /* 审阅信息样式 */
  .review-info {
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #f0f9ff;
    border: 1px solid #d6e4ff;
    border-radius: 4px;
  }

  .review-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
  }

  .review-title {
    color: #1890ff;
    margin-right: 8px;
  }

  .review-details {
    font-size: 12px;
  }

  .review-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    color: #666;
  }

  .review-comment {
    margin-bottom: 8px;
  }

  .comment-label {
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .comment-content {
    background-color: #fff;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid #1890ff;
    color: #333;
  }

  .corrected-content {
    margin-bottom: 8px;
  }

  .corrected-label {
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .corrected-text {
    background-color: #f6ffed;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid #52c41a;
    color: #333;
    margin-bottom: 4px;
  }

  .review-annotations {
    color: #666;
  }

  .annotations-count {
    font-size: 11px;
  }
</style>
