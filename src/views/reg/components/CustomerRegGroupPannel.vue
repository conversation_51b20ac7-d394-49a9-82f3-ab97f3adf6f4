<template>
  <a-modal v-model:open="visiable" :title="title" @cancel="handleCancel" :footer="null" width="100%" wrap-class-name="full-modal">
    <div style="height: 95vh; overflow-y: scroll; padding: 5px">
      <a-alert v-if="!customerRegId" message="请选择体检人" type="error" :show-icon="true" :closable="false" style="margin-bottom: 10px" />
      <a-alert
        message="当前总检状态是审核通过状态，项目列表已禁止修改"
        type="warning"
        v-if="!allowModifyItems && customerRegId"
        style="margin-bottom: 10px"
      />
      <a-row :gutter="8" v-if="customerRegId">
        <a-col :span="9">
          <a-tabs type="card">
            <a-tab-pane key="1" tab="待选组合" style="padding: 5px; height: 80vh">
              <a-row :gutter="8">
                <a-col :span="7">
                  <div style="max-height: 80vh; overflow-y: scroll; padding: 0">
                    <depart-tree @select="handleDepartTreeSelect" :disabled="!allowModifyItems" />
                  </div>
                </a-col>
                <a-col :span="17">
                  <a-form layout="inline" :model="batchFormState" style="margin-bottom: 10px">
                    <a-form-item labelAlign="left">
                      <a-input-search
                        v-model:value="queryParam.keyword"
                        @input="handleSearchInput"
                        allow-clear
                        @search="handleSearchKeyword"
                        size="middle"
                        placeholder="名称"
                        :disabled="!allowModifyItems"
                      />
                    </a-form-item>
                    <a-form-item v-if="selectedRows.length"> 已选中 {{ selectedRows.length }} 项 </a-form-item>
                  </a-form>

                  <BasicTable @register="registerTable" :rowSelection="allowModifyItems ? rowSelection : null">
                    <!--操作栏-->
                    <template #action="{ record }">
                      <TableAction :actions="allowModifyItems ? getTableAction(record) : []" />
                    </template>
                    <!--字段回显插槽-->
                    <template #bodyCell="{ column, record, index, text }"> </template>
                  </BasicTable>
                </a-col>
              </a-row>
            </a-tab-pane>
            <a-tab-pane key="2" tab="待选套餐">
              <!--              <a-alert type="warning" show-icon message="单位预约人员不可使用套餐" v-if="selectedCustomerReg.value?.teamId" />-->
              <a-alert type="warning" show-icon message="当前总检状态是审核通过状态，无法使用套餐" v-if="!allowModifyItems" />
              <item-suit-lite-list @use="handleSetGroupBySuit" ref="itemSuitLiteList" :disabled="!allowModifyItems" />
            </a-tab-pane>
          </a-tabs>
        </a-col>
        <a-col :span="1" style="display: flex; flex-direction: column; justify-content: center; align-items: center">
          <a-space direction="vertical" size="middle">
            <a-button size="small" type="primary" :disabled="selectedRows.length == 0 || !allowModifyItems || regGroupLoading" :loading="regGroupLoading" @click="addSelectedBatch">添加</a-button>
            <a-button size="small" type="primary" @click="removeRegGroupBatch" :disabled="!removeBtnEnable || !allowModifyItems">删除</a-button>
            <a-button size="small" type="primary" @click="minusRegGroupBatch" :disabled="!minusBtnEnable || !allowModifyItems">减项</a-button>
            <a-button size="small" type="primary" @click="undoMinusRegGroupBatch" :disabled="!undoMinusBtnEnable || !allowModifyItems">反减</a-button>
            <a-button size="small" type="primary" @click="refundFeeBatch" :disabled="!refundEnable || !allowModifyItems">退费</a-button>
            <!--            <a-button size="small" type="primary" @click="setPayerType('单位支付')">单位支付</a-button>
            <a-button size="small" type="primary" @click="setPayerType('个人支付')">个人支付</a-button>-->
          </a-space>
        </a-col>
        <a-col :span="14">
          <a-card size="small" title="已选组合" style="padding: 5px; height: 80vh">
            <template #extra>
              <a-button type="dashed" size="small" @click="showColorModal" style="margin-bottom: 16px"> 设置表格行颜色 </a-button>
            </template>
            <div style="display: flex; justify-content: space-between; align-items: center">
              <div style="display: flex; align-items: center">
                <span style="font-size: 14px">总价:</span><span style="color: red; font-weight: bold; font-size: 16px">￥{{ totalPrice }}</span>
                <a-divider type="vertical" />
                <span style="font-size: 14px">折后:</span
                ><span style="color: red; font-weight: bold; font-size: 16px">￥{{ totalPriceAfterDis }}</span>
                <!-- <a-divider type="vertical" />
                             <span style="font-size: 14px">单位付:</span
                  ><span style="color: red; font-weight: bold; font-size: 16px">￥{{ totalCompanyPayAmount }}</span>
                  <a-divider type="vertical" />
                  <span style="font-size: 14px">个人付:</span
                  ><span style="color: red; font-weight: bold; font-size: 16px">￥{{ totalPersonPayAmount }}</span>-->
                <template v-if="companyTeam.limitAmount">
                  <a-divider type="vertical" />
                  <span type="danger" style="font-size: 14px">剩余额度:</span
                  ><span style="color: red; font-weight: bold; font-size: 16px">￥{{ remainingBalancePreview }}</span>
                  <span style="font-weight: bold; font-size: 16px">(￥{{ companyTeam.limitAmount }})</span>
                  <a @click="openRelatedFeePayRecordListModal" style="margin-left: 5px">查看记录</a>
                </template>
              </div>
              <a-space>
                <a-button size="small" type="primary" @click="handleAddItemDone" :disabled="customerReg.status != '已登记' || !allowModifyItems"
                >完成加项</a-button
                >
                <a-dropdown>
                  <a class="ant-dropdown-link" @click.prevent>
                    <MenuOutlined />
                  </a>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item>
                        <a @click="showCheckStatus">检查状态</a>
                      </a-menu-item>
                      <a-menu-item :disabled="!allowModifyItems">
                        <a @click="allowModifyItems && openHistoryRegListModal()">使用历史体检项目</a>
                      </a-menu-item>
                      <a-menu-item>
                        <a @click="showColorModal">表格背景</a>
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </div>
            <a-alert
              :message="mustCheckItemTip"
              :type="mustCheckItemGroupFiltered.length > 0 ? 'warning' : 'success'"
              v-if="customerReg?.riskFactor"
              style="margin-bottom: 5px"
            >
              <template #action>
                <a-space
                ><a-button size="small" type="text" @click="addMustCheckItem" :disabled="!allowModifyItems">添加</a-button>
                  <a-button size="small" type="text" @click="openMustCheckItemModal">查看</a-button></a-space
                >
              </template>
            </a-alert>
            <div style="display: flex; gap: 8px; align-items: center; margin-bottom: 10px;">
              <a-tooltip :title="getDisplayModeTooltip()">
                <a-button
                  size="small"
                  @click="toggleRelationDisplayMode"
                  :icon="getDisplayModeIcon()"
                  style="flex-shrink: 0;"
                >
                  {{ getDisplayModeText() }}
                </a-button>
              </a-tooltip>
              <a-input-search
                v-model:value="regGroupSearch.keyword"
                allow-clear
                size="small"
                placeholder="项目名称"
                @input="handleRegGroupSearch"
                :disabled="!allowModifyItems"
                style="flex: 1;"
              />
            </div>
            <a-form layout="inline" :model="batchFormState" style="margin-bottom: 10px">

              <template v-if="hasPermission('reg:discount')">
                <a-form-item labelAlign="left" tooltip="折扣率与折后总价只能设置一个，同时设置时以折扣率为准">
                  <a-input-number
                    v-model:value="batchFormState.disRate"
                    size="small"
                    placeholder="折扣率"
                    :disabled="!hasPermission('reg:discount') || !allowModifyItems"
                  />
                </a-form-item>
                <a-form-item labelAlign="left">
                  <a-input-number
                    v-model:value="batchFormState.priceAfterDis"
                    size="small"
                    placeholder="折后总价"
                    :disabled="!hasPermission('reg:discount') || !allowModifyItems"
                  />
                </a-form-item>
              </template>
              <a-form-item labelAlign="left">
                <a-select
                  size="small"
                  v-model:value="batchFormState.type"
                  placeholder="项目类型"
                  style="width: 95px"
                  :disabled="!allowModifyItems"
                  :options="[
                    { label: '健康项目', value: '健康项目' },
                    { label: '职业项目', value: '职业项目' },
                  ]"
                />
              </a-form-item>
              <a-form-item>
                <a-popconfirm :title="batchTip" ok-text="确定" cancel-text="取消" @confirm="updateByBatch">
                  <a-button type="primary" size="small" html-type="submit" :disabled="!allowModifyItems">整体设置</a-button>
                </a-popconfirm>
              </a-form-item>
            </a-form>

            <!-- 依赖项目提示区域 -->
            <div v-if="missingDependencies.length > 0" class="missing-dependencies-alert" style="margin-bottom: 16px">
              <a-alert type="warning" show-icon :closable="false">
                <template #message>
                  <div class="missing-dependencies-content">
                    <span class="alert-title">检测到缺失的依赖项目</span>
                    <div class="missing-projects-list">
                      <a-tag
                        v-for="dependency in missingDependencies"
                        :key="dependency.dependentId"
                        color="orange"
                        style="margin: 2px 4px 2px 0"
                        :title="`依赖此项目的检查项目: ${dependency.relatedItemsText}`"
                      >
                        {{ dependency.dependentName }}
                        <template v-if="dependency.dependentItemDetails"> ({{ dependency.dependentItemDetails }}) </template>
                      </a-tag>
                    </div>
                  </div>
                </template>
                <template #action>
                  <a-button type="primary" size="small" @click="handleQuickAddAllDependencies" :loading="addingDependencies"> 一键添加 </a-button>
                </template>
              </a-alert>
            </div>

            <a-table
              :style="tableStyle"
              :row-class-name="rowClassName"
              :class="['ant-table-striped']"
              :scroll="{ y: 'calc(100vh - 200px)' }"
              :loading="regGroupLoading"
              :bordered="false"
              :pagination="false"
              row-key="id"
              :columns="regGroupColumn"
              :data-source="filteredRegGroupDataSource"
              :row-selection="{
                selectedRowKeys: regGroupTableState.selectedRowKeys,
                onChange: onRegGroupTableSelectChange,
                getCheckboxProps: (record) => ({
                  disabled: record.payStatus == '已退款',
                }),
              }"
              size="small"
            >
              <template #bodyCell="{ column, text, record, index }">
                <template v-if="'itemGroupName' == column.dataIndex">
                  <div style="display: flex; justify-content: space-between; align-items: center">
                    <template v-if="record.addMinusFlag == 1">
                      <a-tooltip :title="getItemTooltipText(text, record.checkPartName)" trigger="click">
                        <div style="display: flex; align-items: center; gap: 2px; overflow: hidden;">
                          <!-- 项目关系前缀显示 -->
                          <span
                            v-if="getItemRelationPrefix(record.itemGroupId)"
                            :style="{
                              color: getItemRelationPrefix(record.itemGroupId).color,
                              fontSize: '12px',
                              fontWeight: 500,
                              flexShrink: 0,
                              userSelect: 'none'
                            }"
                            :title="getItemRelationPrefix(record.itemGroupId).title"
                          >
                            {{ getItemRelationPrefix(record.itemGroupId).text }}
                          </span>

                          <!-- 加项标识和项目名称 -->
                          <span class="added-item" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: getItemSourceType(record.itemGroupId) !== 'main' ? '#666' : 'inherit'">
                            加 {{ formatItemDisplayName(text, record.checkPartName) }}
                          </span>

                          <!-- 可选的badge显示 -->
                          <a-tag
                            v-if="getItemSourceBadge(record.itemGroupId) && showDetailedBadges"
                            :title="getItemSourceBadge(record.itemGroupId).title"
                            size="small"
                            :style="{
                              margin: 0,
                              fontSize: '10px',
                              lineHeight: '16px',
                              padding: '0 4px',
                              fontWeight: 500,
                              backgroundColor: getItemSourceBadge(record.itemGroupId).bg,
                              color: getItemSourceBadge(record.itemGroupId).color,
                              border: `1px solid ${getItemSourceBadge(record.itemGroupId).bg}`,
                              cursor: 'pointer'
                            }"
                            @click.stop.prevent="showItemRelationDetail(record.itemGroupId)"
                          >
                            {{ getItemSourceBadge(record.itemGroupId).text }}
                          </a-tag>
                        </div>
                      </a-tooltip>
                    </template>
                    <template v-else-if="record.addMinusFlag == -1">
                      <div style="display: flex; align-items: center; gap: 4px; overflow: hidden;">
                        <span class="removed-item" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">减 {{ text }}</span>
                        <a-tag
                          v-if="getItemSourceBadge(record.itemGroupId)"
                          :title="getItemSourceBadge(record.itemGroupId).title"
                          size="small"
                          :style="{
                            margin: 0,
                            fontSize: '10px',
                            lineHeight: '16px',
                            padding: '0 4px',
                            fontWeight: 500,
                            backgroundColor: getItemSourceBadge(record.itemGroupId).bg,
                            color: getItemSourceBadge(record.itemGroupId).color,
                            border: `1px solid ${getItemSourceBadge(record.itemGroupId).bg}`,
                            cursor: 'pointer'
                          }"
                          @click="showItemRelationDetail(record.itemGroupId)"
                        >
                          {{ getItemSourceBadge(record.itemGroupId).text }}
                        </a-tag>
                      </div>
                    </template>
                    <template v-else>
                      <a-tooltip :title="getItemTooltipText(text, record.checkPartName)" trigger="click">
                        <div style="display: flex; align-items: center; gap: 2px; overflow: hidden;">
                          <!-- 项目关系前缀显示 -->
                          <span
                            v-if="getItemRelationPrefix(record.itemGroupId)"
                            :style="{
                              color: getItemRelationPrefix(record.itemGroupId).color,
                              fontSize: '12px',
                              fontWeight: 500,
                              flexShrink: 0,
                              userSelect: 'none'
                            }"
                            :title="getItemRelationPrefix(record.itemGroupId).title"
                          >
                            {{ getItemRelationPrefix(record.itemGroupId).text }}
                          </span>

                          <!-- 项目名称 -->
                          <span
                            :style="{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              color: getItemSourceType(record.itemGroupId) !== 'main' ? '#666' : 'inherit'
                            }"
                          >
                            {{ formatItemDisplayName(text, record.checkPartName) }}
                          </span>

                          <!-- 保留原有的badge（可选显示） -->
                          <a-tag
                            v-if="getItemSourceBadge(record.itemGroupId) && showDetailedBadges"
                            :title="getItemSourceBadge(record.itemGroupId).title"
                            size="small"
                            :style="{
                              margin: 0,
                              fontSize: '10px',
                              lineHeight: '16px',
                              padding: '0 4px',
                              fontWeight: 500,
                              backgroundColor: getItemSourceBadge(record.itemGroupId).bg,
                              color: getItemSourceBadge(record.itemGroupId).color,
                              border: `1px solid ${getItemSourceBadge(record.itemGroupId).bg}`,
                              cursor: 'pointer'
                            }"
                            @click="showItemRelationDetail(record.itemGroupId)"
                          >
                            {{ getItemSourceBadge(record.itemGroupId).text }}
                          </a-tag>
                        </div>
                      </a-tooltip>
                    </template>
                  </div>
                </template>
                <template v-else-if="'seq' == column.dataIndex">
                  {{ index + 1 }}
                </template>
                <template v-else-if="'disRate' == column.dataIndex">
                  <input
                    v-if="hasPermission('reg:discount')"
                    type="number"
                    min="0"
                    step="0.1"
                    @change="handleDisRateChange(record, $event)"
                    :value="record.disRate"
                    :readonly="record.payStatus != '待支付'"
                    style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 60px"
                  />
                  <span v-else>
                    {{ record.disRate }}
                  </span>
                </template>
                <template v-else-if="'priceAfterDis' == column.dataIndex">
                  <input
                    v-if="hasPermission('reg:discount')"
                    type="number"
                    min="0"
                    @change="handlePriceChange(record, $event)"
                    :value="record.priceAfterDis"
                    :readonly="record.payStatus != '待支付'"
                    style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 60px"
                  />
                  <span v-else>
                    {{ record.priceAfterDis }}
                  </span>
                </template>
                <template v-else-if="'minDiscountRate' == column.dataIndex">
                  <input
                    v-if="hasPermission('reg:minDiscountRate')"
                    type="number"
                    min="0"
                    step="0.1"
                    @change="updateMinDiscountRate(record, $event)"
                    :value="record.minDiscountRate"
                    :readonly="record.payStatus != '待支付'"
                    style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 60px"
                  />
                  <span v-else>
                    {{ record.minDiscountRate }}
                  </span>
                </template>
                <template v-else-if="column.dataIndex == 'type'">
                  <a-select
                    @change="updateType(record)"
                    size="small"
                    :disabled="record.payStatus != '待支付'"
                    v-model:value="record.type"
                    style="width: 90px"
                    :options="[
                      { label: '健康项目', value: '健康项目' },
                      { label: '职业项目', value: '职业项目' },
                    ]"
                  />
                </template>
                <template v-else-if="column.dataIndex == 'payerType'">
                  <a-select
                    @change="updatePayerType(record)"
                    size="small"
                    :disabled="record.payStatus != '待支付' || !customerReg.teamId"
                    v-model:value="record.payerType"
                    style="width: 90px"
                    :options="[
                      { label: '个人支付', value: '个人支付' },
                      { label: '单位支付', value: '单位支付' },
                    ]"
                  />
                </template>
                <template v-else-if="column.dataIndex == 'payStatus'">
                  <template v-if="record.payStatus == '退款中' || record.payStatus == '退款成功' || record.payStatus == '退款失败'">
                    <a-typography-text type="danger">{{ record.payStatus }}</a-typography-text>
                  </template>
                  <template v-else-if="record.payStatus == '支付成功' || record.payStatus == '已支付'">
                    {{ record.payStatus }}
                  </template>
                  <template v-else>
                    {{ record.payStatus }}
                  </template>
                </template>
                <template v-else-if="column.dataIndex == 'operation'">
                  <template v-if="record.actionType === '退费'">
                    <a @click="doActionType(record)">{{ record.actionType }}</a>
                  </template>
                  <template v-else>
                    <a-popconfirm :title="`确定${record.actionType}吗?`" ok-text="确定" cancel-text="取消" @confirm="doActionType(record)">
                      <a>{{ record.actionType }}</a>
                    </a-popconfirm>
                  </template>
                </template>
                <template v-else>
                  {{ text }}
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
      <group-item-modal ref="groupItemModal" />
      <!--      <fee-refund-modal ref="feeRefundModal" @success="handleRefundSuccess" />-->
      <bill-refund-modal ref="feeRefundModal" @success="handleRefundSuccess" />
      <must-check-item-group-modal ref="mustCheckItemGroupModal" />
      <ColorSettingsModal :initialColors="initialTableStyle" @apply-colors="applyStripedStyles" ref="colorModalRef" />
      <RelatedFeePayRecordListModal ref="relatedFeePayRecordListModal" />
      <CustomerRegItemGroupStatusModal ref="statusModal" />
      <HistoryRegListModal ref="historyRegListModal" @success="handleHistoryItem" />

      <!-- 部位选择模态框 -->
      <a-modal
        title="选择检查部位"
        v-model:open="checkPartState.visible"
        width="600px"
        @ok="confirmAddItemWithParts"
        @cancel="closeCheckPartSelector"
        :confirmLoading="checkPartState.loading"
        :okButtonProps="{ disabled: checkPartState.selectedParts.length === 0 }"
        okText="确认添加"
        cancelText="取消"
        @keydown="handleModalKeydown"
      >
        <div style="padding: 10px; height: 50vh; overflow-y: auto;">
          <div style="margin-bottom: 8px;">
            <strong>项目：</strong>{{ checkPartState.currentItemGroup?.name }}
          </div>

          <a-form-item required>
            <a-select
              ref="checkPartSelectRef"
              v-model:value="checkPartState.selectedParts"
              mode="multiple"
              placeholder="请选择检查部位，支持多选，可输入关键字搜索（支持拼音缩写）"
              :filter-option="filterPartOption"
              :loading="checkPartState.loading"
              :options="checkPartState.options"
              @keydown="handleSelectKeydown"
              style="width: 100%"
              show-search
              allow-clear
              :not-found-content="checkPartState.loading ? '搜索中...' : '暂无数据'"
              :max-tag-count="3"
              :max-tag-text-length="8"
            >
              <template v-if="checkPartState.loading" #suffixIcon>
                <a-spin size="small" />
              </template>
            </a-select>
          </a-form-item>

          <div style="margin-top: 16px; padding: 12px; background: #f5f5f5; border-radius: 4px;">
            <div style="margin-bottom: 8px;">
              <strong>操作提示：</strong>
            </div>
            <div style="color: #666; font-size: 12px; line-height: 1.5;">
              • 支持多选，为同一项目选择多个部位会创建多条记录<br/>
              • 支持输入关键字搜索部位，支持拼音搜索<br/>
              • 部位按使用频次排序，常用部位排在前面<br/>
              • 按 Ctrl+Enter 可快速确认添加
            </div>
          </div>
        </div>
      </a-modal>

      <!-- 套餐部位补充弹窗 -->
      <SuitPartSelectionModal
        ref="suitPartModalRef"
        @confirm="handleSuitPartConfirm"
        @cancel="handleSuitPartCancel"
      />

      <!-- 依赖项目快捷添加模态框 -->
      <DependencyQuickAddModal
        ref="dependencyQuickAddModalRef"
        @quick-add="handleDependencyQuickAdd"
        @confirm="handleDependencyConfirm"
        @cancel="handleDependencyCancel"
      />

      <!-- 项目关系详情模态框 -->
      <a-modal
        v-model:open="relationDetailModal.visible"
        title="项目关系详情"
        width="800px"
        :footer="null"
        @cancel="closeRelationDetailModal"
      >
        <div style="padding:10px">
          <div v-if="relationDetailModal.loading" style="text-align: center; padding: 40px;">
            <a-spin size="large" />
            <div style="margin-top: 16px;">正在加载关系详情...</div>
          </div>

          <div v-else-if="relationDetailModal.itemInfo && relationDetailModal.relationData" style="padding: 16px 0;">
            <!-- 当前项目信息 -->
            <div style="margin-bottom: 24px;">
              <h4 style="margin-bottom: 12px; color: #1890ff;">
                <span style="margin-right: 8px;">📋</span>当前项目信息
              </h4>
              <div style="background: #f5f5f5; padding: 12px; border-radius: 6px;">
                <div><strong>项目名称：</strong>{{ relationDetailModal.itemInfo.itemGroupName }}</div>
                <div v-if="relationDetailModal.itemInfo.checkPartName" style="margin-top: 4px;">
                  <strong>检查部位：</strong>{{ relationDetailModal.itemInfo.checkPartName }}
                </div>
                <div style="margin-top: 4px;">
                  <strong>项目类型：</strong>
                  <a-tag :color="getItemSourceBadge(relationDetailModal.itemInfo.itemGroupId)?.bg" style="margin-left: 8px;">
                    {{ getItemSourceBadge(relationDetailModal.itemInfo.itemGroupId)?.text }}
                  </a-tag>
                </div>
              </div>
            </div>

            <!-- 关系来源信息 -->
            <div v-if="relationDetailModal.relationData.sourceInfo" style="margin-bottom: 24px;">
              <h4 style="margin-bottom: 12px; color: #52c41a;">
                <span style="margin-right: 8px;">🔗</span>关系来源
              </h4>
              <div style="background: #f6ffed; border: 1px solid #b7eb8f; padding: 12px; border-radius: 6px;">
                <div><strong>来源项目：</strong>{{ relationDetailModal.relationData.sourceInfo.mainItem.itemGroupName }}</div>
                <div v-if="relationDetailModal.relationData.sourceInfo.mainItem.checkPartName" style="margin-top: 4px;">
                  <strong>来源部位：</strong>{{ relationDetailModal.relationData.sourceInfo.mainItem.checkPartName }}
                </div>
                <div style="margin-top: 8px;">
                  <strong>关系类型：</strong>
                  <span v-if="relationDetailModal.relationData.sourceInfo.type === 'dependent'" style="color: #fa8c16;">
                  依赖关系 - 当前项目是上述项目的依赖项目
                </span>
                  <span v-else-if="relationDetailModal.relationData.sourceInfo.type === 'gift'" style="color: #52c41a;">
                  赠送关系 - 当前项目是上述项目的赠送项目
                </span>
                  <span v-else-if="relationDetailModal.relationData.sourceInfo.type === 'attach'" style="color: #722ed1;">
                  附属关系 - 当前项目是上述项目的附属项目
                </span>
                </div>
              </div>
            </div>

            <!-- 说明信息 -->
            <div style="background: #e6f7ff; border: 1px solid #91d5ff; padding: 12px; border-radius: 6px;">
              <h4 style="margin-bottom: 8px; color: #1890ff;">
                <span style="margin-right: 8px;">💡</span>说明
              </h4>
              <div style="color: #666; line-height: 1.6;">
                <div v-if="relationDetailModal.relationData.sourceInfo?.type === 'dependent'">
                  • 此项目是依赖项目，当添加来源项目时会自动添加<br/>
                  • 依赖项目通常是检查必需的基础项目或前置项目<br/>
                  • 删除来源项目时，此项目也会被自动删除
                </div>
                <div v-else-if="relationDetailModal.relationData.sourceInfo?.type === 'gift'">
                  • 此项目是赠送项目，当添加来源项目时会免费获得<br/>
                  • 赠送项目通常是促销活动或套餐优惠的一部分<br/>
                  • 删除来源项目时，此项目也会被自动删除
                </div>
                <div v-else-if="relationDetailModal.relationData.sourceInfo?.type === 'attach'">
                  • 此项目是附属项目，与来源项目形成组合<br/>
                  • 附属项目通常是主项目的补充检查或相关项目<br/>
                  • 删除来源项目时，此项目也会被自动删除
                </div>
              </div>
            </div>
          </div>

          <div v-else style="text-align: center; padding: 40px; color: #999;">
            暂无关系详情信息
          </div>
        </div>
      </a-modal>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { computed, h, inject, nextTick, onMounted, reactive, ref, unref, watch } from 'vue';
import { CompanyTeam, CompanyTeamItemGroup, CustomerRegItemGroup, ICustomerReg, ItemGroup, Key, CheckPartDict } from '#/types';
import { getItemGroupByTeam, getTeamById } from '@/views/reg/CompanyReg.api';
import { getGroupOfSuit, getItemGroupBySuit } from '@/views/basicinfo/ItemSuit.api';
import {
  addCustomerReg,
  addCustomerRegForSuit,
  getItemGroupByCustomerRegId,
  getItemGroupWithDependencyAnalysis,
  getSpendAmount,
  handleAddItem,
  minusItemGroup,
  removeItemGroup,
  undoMinusItemGroup,
  updateItemGroup,
  addItemGroupWithCheckParts,
} from '@/views/reg/CustomerReg.api';
import { list as listItemGroup, listByRiskFactor } from '@/views/basicinfo/ItemGroup.api';
import { message, theme } from 'ant-design-vue';
import DepartTree from '@/views/basicinfo/components/DepartTree.vue';
import GroupItemModal from '@/views/basicinfo/components/GroupItemModal.vue';
import ItemSuitLiteList from '@/views/basicinfo/ItemSuitLiteList.vue';
import { selectedCustomerRegKey } from '@/providekey/provideKeys';
import { useMessage } from '@/hooks/web/useMessage';
import { usePermission } from '/@/hooks/web/usePermission';
import { BasicColumn, BasicTable, TableAction } from '@/components/Table';
import { useListPage } from '@/hooks/system/useListPage';
import { debounce, throttle } from 'lodash-es';
import { isItemGroupAvailable, isSuitAvailable } from '@/utils/itemGroupValidation';
import { listByItemGroup } from '@/views/basicinfo/CheckPartDict.api';
import SuitPartSelectionModal from './SuitPartSelectionModal.vue';
import { v4 as uuidv4 } from 'uuid';
import MustCheckItemGroupModal from '@/views/reg/components/MustCheckItemGroupModal.vue';
import ColorSettingsModal from '@/components/colorSetting/ColorSettingsModal.vue';
import BillRefundModal from '@/views/fee/components/BillRefundModal.vue';
import { MenuOutlined, BranchesOutlined, TagOutlined, AppstoreOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue';
import RelatedFeePayRecordListModal from '@/views/fee/RelatedFeePayRecordListModal.vue';
import {
  checkItemMutex,
  formatConflictMessage,
  checkItemDependencies,
  checkAllItemsDependencies,
  formatDependencyMessage,
  getMissingDependencyDetails,
  preloadRelationData,
  mergeDependenciesByGroup,
  analyzeItemSources,
  getItemRelations,
} from '@/utils/itemGroupRelationManager';
import { DependencyChecker } from '@/utils/DependencyChecker';

import HistoryRegListModal from '@/views/reg/components/HistoryRegListModal.vue';
import CustomerRegItemGroupStatusModal from '@/views/summary/components/CustomerRegItemGroupStatusModal.vue';
import DependencyQuickAddModal from '@/components/DependencyQuickAddModal.vue';

const { token } = theme.useToken();
const { hasPermission } = usePermission();
const { createConfirm, createErrorModal } = useMessage();
const emits = defineEmits(['cancel', 'addItemDone']);
const feeRefundModal = ref();
const visiable = ref<boolean>(false);
const title = computed(() => (selectedCustomerReg.value.name ? selectedCustomerReg.value.name + '的项目组合' : '项目组合'));
const customerRegId = computed<string>(() => selectedCustomerReg.value?.id);
const customerReg = computed<ICustomerReg>(() => selectedCustomerReg.value);
const companyTeam = ref<CompanyTeam>({});
const spendAmount = ref<number>(0);
let originList: CustomerRegItemGroup[] = [];
let groupListFromAppoint: CustomerRegItemGroup[] = [];
const statusModal = ref<any>({});

// 部位选择相关状态
const checkPartState = reactive({
  visible: false,
  loading: false,
  options: [] as Array<{label: string, value: string, frequency: number}>,
  selectedParts: [] as string[],
  currentItemGroup: null as ItemGroup | null,
});

const checkPartSelectRef = ref();
const suitPartModalRef = ref();

// 依赖项目快捷添加模态框引用
const dependencyQuickAddModalRef = ref(null);

// 缺失的依赖项目列表
const missingDependencies = ref([]);
// 添加依赖项目的loading状态
const addingDependencies = ref(false);
// 依赖检查缓存时间戳
const lastDependencyCheckTime = ref(0);
// 依赖检查缓存时长（5分钟）
const DEPENDENCY_CHECK_CACHE_DURATION = 5 * 60 * 1000;

// 依赖检查器
const dependencyChecker = ref(null);

// 项目来源分析结果
const itemSourceMap = ref(new Map());
// 项目来源分析的loading状态
const analyzingItemSources = ref(false);

// 依赖关系详情模态框状态
const relationDetailModal = ref({
  visible: false,
  loading: false,
  itemInfo: null,
  relationData: null,
});

// 初始化依赖检查器
onMounted(() => {
  dependencyChecker.value = new DependencyChecker(regGroupDataSource);

  // 预加载常用项目的关系数据
  preloadCommonItemRelations();
});

// 预加载常用项目的关系数据
async function preloadCommonItemRelations() {
  try {
    // 获取当前客户已有的项目ID列表
    const existingItemIds = regGroupDataSource.value
      .filter(item => item.addMinusFlag !== -1)
      .map(item => item.itemGroupId);

    // 预加载这些项目的关系数据
    if (existingItemIds.length > 0) {
      await preloadRelationData([...new Set(existingItemIds)]);
      console.log('预加载项目关系数据完成');
    }
  } catch (error) {
    console.error('预加载项目关系数据失败:', error);
  }
}

// 添加项目后的依赖检查（已迁移到后端统一分析）
async function checkDependenciesAfterAdd(addedItems) {
  console.log('ℹ️ checkDependenciesAfterAdd 被调用，但依赖关系已由后端统一分析');
  console.log('   - 新增项目数量:', addedItems.length);
  console.log('   - 当前缺失依赖数量:', missingDependencies.value?.length || 0);

  // 依赖关系数据已经在主要的数据获取函数中由后端返回
  // 无需在添加项目后重复检查，直接返回

  // 如果确实需要重新检查（比如手动添加项目），可以调用主要的数据刷新函数
  // 这样可以获取最新的完整依赖分析结果
  return;
}

// 检查所有项目的依赖关系
async function checkAllDependencies(forceCheck = false) {
  console.log('checkAllDependencies 被调用，forceCheck:', forceCheck, '项目数量:', regGroupDataSource.value?.length || 0);

  // 确保数据源存在且有效
  if (!regGroupDataSource.value || regGroupDataSource.value.length === 0) {
    console.log('项目列表为空，清空依赖提示');
    missingDependencies.value = [];
    return;
  }

  // 检查缓存，避免频繁检查
  const now = Date.now();
  if (!forceCheck && now - lastDependencyCheckTime.value < DEPENDENCY_CHECK_CACHE_DURATION) {
    console.log('依赖检查缓存有效，跳过检查');
    return;
  }

  // 如果有项目但当前没有依赖提示，也需要检查（可能是首次加载）
  if (!forceCheck && regGroupDataSource.value.length > 0 && missingDependencies.value.length === 0) {
    console.log('有项目但无依赖提示，执行检查');
    // 继续执行检查
  }

  try {
    console.log('开始检查所有项目的依赖关系');
    console.log('regGroupDataSource数据示例:', regGroupDataSource.value[0]);
    console.log('regGroupDataSource第一个项目的所有字段:', Object.keys(regGroupDataSource.value[0] || {}));

    // 将所有现有项目作为"新添加"项目来检查依赖
    const allItems = regGroupDataSource.value
      .filter(item => item.addMinusFlag !== -1 && item.itemGroupId) // 排除减项的项目和无效项目
      .map(item => ({
        itemGroupId: item.itemGroupId,
        itemGroupName: item.itemGroupName,
        checkPartId: item.checkPartId,
        checkPartName: item.checkPartName
      }));

    console.log('转换后的allItems示例:', allItems[0]);
    console.log('过滤前项目数量:', regGroupDataSource.value.length, '过滤后项目数量:', allItems.length);

    if (allItems.length === 0) {
      console.log('没有有效的项目需要检查依赖');
      missingDependencies.value = [];
      return;
    }

    // 使用专门的函数检查所有项目的依赖关系
    const dependencyCheck = await checkAllItemsDependencies(allItems);
    console.log('依赖检查结果:', dependencyCheck);

    if (!dependencyCheck.isValid && dependencyCheck.missing.length > 0) {
      // 合并相同大项的依赖项目
      const mergedDependencies = mergeDependenciesByGroup(dependencyCheck.missing);
      console.log('合并后的依赖项目:', mergedDependencies);

      // 更新缺失依赖项目列表
      missingDependencies.value = mergedDependencies;

      console.log(
        '发现缺失的依赖大项:',
        mergedDependencies.map((dep) => dep.dependentName)
      );
    } else {
      // 如果没有依赖问题或项目为空，清空缺失依赖列表
      console.log('没有依赖问题，清空依赖提示');
      missingDependencies.value = [];
    }

    // 更新检查时间戳
    lastDependencyCheckTime.value = now;
    console.log('依赖检查完成，最终依赖项目数量:', missingDependencies.value.length);
  } catch (error) {
    console.error('检查所有依赖关系失败:', error);
    // 确保异常情况下不影响用户体验
    missingDependencies.value = [];
  }
}

// 处理依赖项目快捷添加
async function handleDependencyQuickAdd({ dependencies, originalItems }) {
  try {
    dependencyQuickAddModalRef.value?.setLoading(true);

    console.log('开始快捷添加依赖项目:', dependencies);

    // 直接通过API搜索并添加依赖项目
    const projectsToAdd = [];

    for (const dependency of dependencies) {
      try {
        // 通过项目ID直接查询项目信息
        console.log(`搜索依赖项目: ${dependency.name} (${dependency.id})`);

        // 设置搜索条件为项目名称
        const searchParams = {
          name: dependency.name,
          pageNo: 1,
          pageSize: 10,
        };

        // 调用项目搜索API
        const searchResult = await listItemGroup(searchParams);
        console.log(`搜索结果:`, searchResult);

        // 在搜索结果中查找匹配的项目
        const foundProject = searchResult.records?.find((item) => item.id === dependency.id);

        if (foundProject) {
          console.log(`找到依赖项目: ${foundProject.name}`);
          projectsToAdd.push(foundProject);
        } else {
          console.warn(`未找到依赖项目: ${dependency.name} (${dependency.id})`);
        }
      } catch (searchError) {
        console.error(`搜索依赖项目失败: ${dependency.name}`, searchError);
      }
    }

    if (projectsToAdd.length === 0) {
      message.warn('未找到可添加的依赖项目，请检查项目是否存在或手动搜索添加');
      dependencyQuickAddModalRef.value?.setLoading(false);
      return;
    }

    console.log(
      '准备添加的项目:',
      projectsToAdd.map((p) => p.name)
    );

    // 批量添加依赖项目
    await handleAddBatch(projectsToAdd);

    // 关闭模态框
    dependencyQuickAddModalRef.value?.close();

    message.success(`成功添加 ${projectsToAdd.length} 个依赖项目`);
  } catch (error) {
    console.error('快捷添加依赖项目失败:', error);
    message.error('快捷添加失败: ' + (error.message || '未知错误'));
    dependencyQuickAddModalRef.value?.setLoading(false);
  }
}

// 处理依赖检查确认（忽略并继续）
function handleDependencyConfirm({ dependencies, originalItems, action }) {
  console.log('用户选择忽略依赖检查并继续');
  // 这里可以记录用户的选择，或者执行其他逻辑
}

// 处理依赖检查取消
function handleDependencyCancel({ dependencies, originalItems }) {
  console.log('用户取消了依赖检查');
  // 这里可以执行取消后的清理逻辑
}

// 处理一键添加所有依赖项目（从项目列表上方的提示区域）
async function handleQuickAddAllDependencies() {
  if (missingDependencies.value.length === 0) {
    return;
  }

  try {
    addingDependencies.value = true;

    console.log('开始快捷添加依赖项目:', missingDependencies.value);

    // 直接通过API搜索并添加依赖项目
    const projectsToAdd = [];

    for (const dependency of missingDependencies.value) {
      try {
        // 通过项目ID直接查询项目信息
        console.log(`搜索依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);

        // 设置搜索条件为项目名称
        const searchParams = {
          name: dependency.dependentName,
          pageNo: 1,
          pageSize: 10,
        };

        // 调用项目搜索API
        const searchResult = await listItemGroup(searchParams);
        console.log(`搜索结果:`, searchResult);

        // 在搜索结果中查找匹配的项目
        const foundProject = searchResult.records?.find((item) => item.id === dependency.dependentId);

        if (foundProject) {
          console.log(`找到依赖项目: ${foundProject.name}`);
          projectsToAdd.push(foundProject);
        } else {
          console.warn(`未找到依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);
        }
      } catch (searchError) {
        console.error(`搜索依赖项目失败: ${dependency.dependentName}`, searchError);
      }
    }

    if (projectsToAdd.length === 0) {
      message.warn('未找到可添加的依赖项目，请检查项目是否存在');
      return;
    }

    console.log(
      '准备添加的项目:',
      projectsToAdd.map((p) => p.name)
    );

    // 批量添加依赖项目
    await handleAddBatch(projectsToAdd);

    // 清空缺失依赖列表
    missingDependencies.value = [];

    message.success(`成功添加 ${projectsToAdd.length} 个依赖项目`);
  } catch (error) {
    console.error('快捷添加依赖项目失败:', error);
    message.error('快捷添加失败: ' + (error.message || '未知错误'));
  } finally {
    addingDependencies.value = false;
  }
}

// 分析项目来源类型
async function analyzeProjectSources() {
  if (regGroupDataSource.value.length === 0) {
    itemSourceMap.value = new Map();
    return;
  }

  analyzingItemSources.value = true;
  try {
    console.log('开始分析项目来源类型...');
    const sourceMap = await analyzeItemSources(regGroupDataSource.value);
    itemSourceMap.value = sourceMap;
    console.log('项目来源分析完成:', sourceMap);
  } catch (error) {
    console.error('分析项目来源失败:', error);
    itemSourceMap.value = new Map();
  } finally {
    analyzingItemSources.value = false;
  }
}

// 获取项目的来源类型
function getItemSourceType(itemGroupId) {
  return itemSourceMap.value.get(itemGroupId) || 'main';
}

// 获取项目来源的badge配置
function getItemSourceBadge(itemGroupId) {
  const sourceType = getItemSourceType(itemGroupId);

  switch (sourceType) {
    case 'dependent':
      return {
        text: '依赖',
        bg: '#fa8c16', // 橙色 - 依赖项目，表示需要注意的关联关系
        color: '#fff',
        title: '此项目是依赖项目，由其他项目的依赖关系自动添加，点击查看详情'
      };
    case 'gift':
      return {
        text: '赠送',
        bg: '#52c41a', // 绿色 - 赠送项目，表示免费获得
        color: '#fff',
        title: '此项目是赠送项目，由其他项目的赠送关系自动添加，点击查看详情'
      };
    case 'attach':
      return {
        text: '附属',
        bg: '#722ed1', // 紫色 - 附属项目，表示从属关系
        color: '#fff',
        title: '此项目是附属项目，由其他项目的附属关系自动添加，点击查看详情'
      };
    default:
      return null;
  }
}

// 显示项目关系详情
async function showItemRelationDetail(itemGroupId) {
  const currentItem = regGroupDataSource.value.find(item => item.itemGroupId === itemGroupId);
  if (!currentItem) {
    message.warning('未找到项目信息');
    return;
  }

  relationDetailModal.value.visible = true;
  relationDetailModal.value.loading = true;
  relationDetailModal.value.itemInfo = currentItem;
  relationDetailModal.value.relationData = null;

  try {
    // 获取项目关系数据
    const relationData = await getItemRelations(itemGroupId);

    // 分析当前项目的来源
    const sourceType = getItemSourceType(itemGroupId);

    // 查找是哪个主项目导致了这个项目的添加
    const mainItems = regGroupDataSource.value.filter(item =>
      getItemSourceType(item.itemGroupId) === 'main' &&
      item.addMinusFlag !== -1
    );

    let sourceInfo = null;
    for (const mainItem of mainItems) {
      const mainRelations = await getItemRelations(mainItem.itemGroupId);

      // 检查是否在依赖项目中
      if (sourceType === 'dependent' && mainRelations.dependentGroups) {
        const found = mainRelations.dependentGroups.find(dep =>
          (typeof dep === 'string' ? dep : dep.relationGroupId) === itemGroupId
        );
        if (found) {
          sourceInfo = {
            type: 'dependent',
            mainItem: mainItem,
            relationDetail: found
          };
          break;
        }
      }

      // 检查是否在赠送项目中
      if (sourceType === 'gift' && mainRelations.giftGroups) {
        const found = mainRelations.giftGroups.find(gift =>
          (typeof gift === 'string' ? gift : gift.relationGroupId) === itemGroupId
        );
        if (found) {
          sourceInfo = {
            type: 'gift',
            mainItem: mainItem,
            relationDetail: found
          };
          break;
        }
      }

      // 检查是否在附属项目中
      if (sourceType === 'attach' && mainRelations.attachGroups) {
        const found = mainRelations.attachGroups.find(attach =>
          (typeof attach === 'string' ? attach : attach.relationGroupId) === itemGroupId
        );
        if (found) {
          sourceInfo = {
            type: 'attach',
            mainItem: mainItem,
            relationDetail: found
          };
          break;
        }
      }
    }

    relationDetailModal.value.relationData = {
      sourceInfo,
      allRelations: relationData
    };

  } catch (error) {
    console.error('获取项目关系详情失败:', error);
    message.error('获取项目关系详情失败');
  } finally {
    relationDetailModal.value.loading = false;
  }
}

// 关闭关系详情模态框
function closeRelationDetailModal() {
  relationDetailModal.value.visible = false;
  relationDetailModal.value.itemInfo = null;
  relationDetailModal.value.relationData = null;
}

// 判断是否允许修改项目列表
const allowModifyItems = computed<boolean>(() => {
  // 如果总检状态为未总检或者为空，则允许修改
  // return !customerReg.value?.summaryStatus || customerReg.value?.summaryStatus === '未总检';
  return customerReg.value?.summaryStatus !== '审核通过';
});

const props = defineProps({
  showPrice: {
    type: Boolean,
    default: true,
  },
});

function handleAddItemDone() {
  handleAddItem({ id: customerRegId.value }).then((res) => {
    if (res.success) {
      message.success(res.message);
      emits('addItemDone');
    } else {
      message.error(res.message);
    }
  });
}

/**历年体检弹窗*/
const historyRegListModal = ref();
const openHistoryRegListModal = () => {
  if (!customerReg.value) {
    message.error('请选择体检人!');
    return;
  }
  if (!allowModifyItems.value) {
    message.error('当前总检状态是审核通过状态，无法使用历史体检项目!');
    return;
  }
  historyRegListModal.value.open(customerReg.value);
};

const handleHistoryItem = (data: ItemGroup[]) => {
  if (!allowModifyItems.value) {
    message.error('当前总检状态是审核通过状态，无法添加历史体检项目!');
    return;
  }
  //将data批量加入
  handleAddBatch(data);
};

// 处理套餐选择事件
const handleSetGroupBySuit = (suit) => {
  if (allowModifyItems.value) {
    setGroupBySuit(suit);
  }
};

/**检查状态*/
const showCheckStatus = () => {
  statusModal.value.open(customerReg.value);
};

/**团检限额相关*/
const relatedFeePayRecordListModal = ref();
const openRelatedFeePayRecordListModal = () => {
  relatedFeePayRecordListModal.value.open(customerReg.value);
};

const remainingBalance = computed(() => {
  if (companyTeam.value.limitAmount) {
    return (companyTeam.value.limitAmount - spendAmount.value).toFixed(2);
  }
  return 0; // or any default value you prefer
});

//根据剩余额度和已选项目中的待支付项目，计算剩余额度预览值
const remainingBalancePreview = computed(() => {
  if (companyTeam.value.limitAmount) {
    const selectedItems = filteredRegGroupDataSource.value.filter((item) => item.payStatus == '待支付');
    const selectedAmount = selectedItems.reduce((total, item) => total + item.priceAfterDis, 0);
    return (remainingBalance.value - selectedAmount).toFixed(2);
  }
  return 0;
});

//组合项目列表
const regGroupColumn = computed(() => {
  const baseColumns = [
    {
      title: '',
      dataIndex: 'seq',
      width: '15px',
      align: 'center',
    },
    {
      title: '组合名称',
      dataIndex: 'itemGroupName',
      key: 'itemGroupName',
      ellipsis: true,
      width: '120px',
      customRender: ({ text }) => {
        return h(
          'div',
          {
            style: {
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            },
          },
          [
            h(
              'a-tooltip',
              {
                title: text,
              },
              {
                default: () => [text],
              }
            ),
          ]
        );
      },
    },
  ];

  const priceColumns = props.showPrice
    ? [
      {
        title: '原价',
        dataIndex: 'price',
        width: '50px',
      },
      {
        title: '折后价',
        dataIndex: 'priceAfterDis',
        width: '40px',
      },
      {
        title: '折扣率',
        dataIndex: 'disRate',
        width: '40px',
      },
      {
        title: '最低折',
        dataIndex: 'minDiscountRate',
        width: '40px',
        ellipsis: true,
      },
    ]
    : [];

  const statusColumns = [
    {
      title: '状态',
      dataIndex: 'payStatus',
      width: '40px',
      ellipsis: true,
    },
    {
      title: '操作人',
      dataIndex: 'updateName',
      width: '30px',
      ellipsis: true,
    },
    // {
    //   title: '操作',
    //   dataIndex: 'operation',
    //   width: '30px',
    //   fixed: 'right',
    // },
  ];

  return [...baseColumns, ...priceColumns, ...statusColumns];
});

const groupColumns: BasicColumn[] = [
  {
    title: '组合名称',
    dataIndex: 'name',
    ellipsis: true,
    width: 150,
  },
  {
    title: '价格',
    dataIndex: 'price',
    ellipsis: false,
    width: 80,
  },
  {
    title: '科室',
    dataIndex: 'departmentName',
    ellipsis: true,
    width: 80,
  },
];
const queryParam = reactive({ name: '', departmentId: '', helpChar: '', enableFlag: 1 });
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    api: listItemGroup,
    rowKey: 'id',
    columns: groupColumns,
    canResize: false,
    clickToRowSelect: false,
    size: 'small',
    showTableSetting: false,
    striped: true,
    actionColumn: {
      width: 60,
      fixed: 'right',
    },
    pagination: {
      pageSize: 30,
    },
    useSearchForm: false,
    beforeFetch: (params) => {
      return Object.assign(params, queryParam);
    },
    afterFetch: (data) => {
      data.uuid = uuidv4();
      return data;
    },
    customRow: (record) => {
      return {
        class: 'no-select',
        onDblclick: () => {
          handleAddOne(record);
        },
      };
    },
  },
});

const [registerTable, { reload }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;

const selectedCustomerReg = inject(selectedCustomerRegKey, {
  value: {},
  setValue: (val) => (selectedCustomerReg.value = val),
});

/**设置项目列表条纹背景*/
  // Retrieve stored tableStyle from localStorage
const storedTableStyle = JSON.parse(localStorage.getItem('tableStyle') || '{}');
const initialTableStyle = {
  odd: storedTableStyle['--striped-odd-color'] || '#f9f9f9',
  even: storedTableStyle['--striped-even-color'] || '#ffffff',
  fontOdd: storedTableStyle['--font-color-odd'] || '#000000',
  fontEven: storedTableStyle['--font-color-even'] || '#000000',
};

// Define a reactive object to store dynamic styles
const tableStyle = reactive({
  '--striped-odd-color': storedTableStyle['--striped-odd-color'] || '#f9f9f9',
  '--striped-even-color': storedTableStyle['--striped-even-color'] || '#ffffff',
  '--font-color-odd': storedTableStyle['--font-color-odd'] || '#000000',
  '--font-color-even': storedTableStyle['--font-color-even'] || '#000000',
});
const colorModalRef = ref();
const showColorModal = () => {
  colorModalRef.value?.openModal();
};

// 定义方法应用条纹颜色和字体颜色
function applyStripedStyles(data) {
  console.log('applyStripedStyles:', data);
  tableStyle['--striped-odd-color'] = data.odd;
  tableStyle['--striped-even-color'] = data.even;
  tableStyle['--font-color-odd'] = data.fontOdd;
  tableStyle['--font-color-even'] = data.fontEven;
  localStorage.setItem('tableStyle', JSON.stringify(tableStyle));
}

// 修改 rowClassName 方法以区分奇数行和偶数行
function rowClassName(record, index) {
  return index % 2 === 0 ? 'table-striped odd-row' : 'table-striped even-row';
}

function getTableAction(record) {
  return [
    /* {
      label: '项目',
      onClick: () => showItem(record),
    },*/
    {
      label: '添加',
      onClick: () => handleAddOne(record),
    },
  ];
}

/**职业病体检必检项目*/
const mustCheckItemGroupModal = ref(null);
const mustCheckItemGroup = ref<ItemGroup[]>([]);
//过滤出dataSource中没有的必检项目
const mustCheckItemGroupFiltered = computed(() => {
  return mustCheckItemGroup.value.filter((item) => !regGroupDataSource.value.some((row) => row.itemGroupId === item.id));
});
const mustCheckItemTip = computed(() => {
  return `必检项目:${mustCheckItemGroup.value.length}个，${mustCheckItemGroupFiltered.value.length}个待添加！`;
});
function fetchMustCheckItemGorup() {
  listByRiskFactor({ riskFactorIds: customerReg.value.riskFactor, post: customerReg.value.jobStatus }).then((res) => {
    if (res.success) {
      mustCheckItemGroup.value = res.result;
      //根据dataSource中是否包含必检项目，修改mustCheckAdded状态
      mustCheckItemGroup.value.forEach((item) => {
        item.mustCheckAdded = regGroupDataSource.value.some((row) => row.itemGroupId === item.id);
      });
    }
  });
}
function openMustCheckItemModal() {
  mustCheckItemGroupModal.value?.open(mustCheckItemGroup.value);
}

function addMustCheckItem() {
  mustCheckItemGroupFiltered.value.forEach((item) => {
    item.type = '职业项目';
    handleAddOne(item);
  });
}

/**右侧登记中关联的项目组合相关操作*/
const regGroupSearch = reactive({ keyword: '' });
const regGroupLoading = ref<boolean>(false);
const regGroupDataSource = ref<CustomerRegItemGroup[]>([]);
const filteredRegGroupDataSource = computed(() => {
  // 先按关键字过滤
  const keyword = regGroupSearch.keyword.trim().toLowerCase();
  let filteredData = regGroupDataSource.value;

  if (keyword) {
    filteredData = filteredData.filter((item) => {
      const name = item.itemGroupName ? item.itemGroupName.toLowerCase() : '';
      const pinyin = item.helpChar ? item.helpChar.toLowerCase() : '';
      if (name.includes(keyword)|| pinyin.includes(keyword)) {
        return true;
      }
      return false;
    });
  }

  // 构建主项目和子项目的层级结构
  const parentChildMap = buildParentChildMap(filteredData);
  return buildHierarchicalStructure(filteredData, parentChildMap);
});

// 为项目建立父子关系映射
function buildParentChildMap(data) {
  const parentChildMap = new Map();

  console.log('🔍 开始为项目建立父子关系映射...');

  data.forEach(item => {
    const sourceType = getItemSourceType(item.itemGroupId);

    if (sourceType !== 'main') {
      // 这是一个子项目，需要找到它的父项目
      const parentItems = data.filter(parentItem => {
        const parentSourceType = getItemSourceType(parentItem.itemGroupId);
        if (parentSourceType !== 'main') return false;

        // 检查是否存在关系
        return isChildOfParent(item, parentItem);
      });

      parentItems.forEach(parentItem => {
        if (!parentChildMap.has(parentItem.id)) {
          parentChildMap.set(parentItem.id, []);
        }
        parentChildMap.get(parentItem.id).push(item);
        console.log(`建立关系: ${parentItem.itemGroupName} -> ${item.itemGroupName} (${sourceType})`);
      });
    }
  });

  console.log(`父子关系映射完成，共 ${parentChildMap.size} 个父项目`);
  return parentChildMap;
}

// 检查子项目是否属于父项目
function isChildOfParent(childItem, parentItem) {
  // 检查依赖关系
  if (parentItem.dependentGroups && parentItem.dependentGroups.some(dep => dep.relationItemId === childItem.itemGroupId)) {
    return true;
  }
  // 检查附属关系
  if (parentItem.attachGroups && parentItem.attachGroups.some(att => att.relationItemId === childItem.itemGroupId)) {
    return true;
  }
  // 检查赠送关系
  if (parentItem.giftGroups && parentItem.giftGroups.some(gift => gift.relationItemId === childItem.itemGroupId)) {
    return true;
  }
  return false;
}

// 构建层级结构：主项目 + 其子项目（层级关系优先于分类排序）
function buildHierarchicalStructure(data, parentChildMap) {
  console.log('=== buildHierarchicalStructure 开始 ===');
  console.log('输入数据数量:', data.length);

  const result = [];
  const processedItems = new Set();

  // 按照加项和减项进行大分类
  const categorizedData = {
    added: data.filter(item => item.addMinusFlag === 1),
    normal: data.filter(item => item.addMinusFlag !== 1 && item.addMinusFlag !== -1),
    reduced: data.filter(item => item.addMinusFlag === -1)
  };

  console.log('分类结果:', {
    added: categorizedData.added.length,
    normal: categorizedData.normal.length,
    reduced: categorizedData.reduced.length
  });

  // 新的处理逻辑：层级关系优先于分类排序
  // 1. 首先找出所有主项目（不分类别）
  const allMainItems = data.filter(item => {
    const sourceType = getItemSourceType(item.itemGroupId);
    return sourceType === 'main';
  });

  console.log('找到的所有主项目:', allMainItems.map(item => `${item.itemGroupName} (${item.id}) - 加减项标志: ${item.addMinusFlag}`));

  // 2. 按照加减项标志对主项目进行排序
  const sortedMainItems = allMainItems.sort((a, b) => {
    // 加项(1) -> 正常(0) -> 减项(-1)
    const getOrder = (flag) => {
      if (flag === 1) return 0;  // 加项最前
      if (flag === -1) return 2; // 减项最后
      return 1; // 正常项目在中间
    };
    return getOrder(a.addMinusFlag) - getOrder(b.addMinusFlag);
  });

  console.log('排序后的主项目:', sortedMainItems.map(item => `${item.itemGroupName} (${item.id}) - 加减项标志: ${item.addMinusFlag}`));

  // 3. 为每个主项目添加其子项目
  sortedMainItems.forEach(mainItem => {
    if (processedItems.has(mainItem.id)) {
      console.log(`跳过已处理的主项目: ${mainItem.itemGroupName} (${mainItem.id})`);
      return;
    }

    console.log(`--- 处理主项目: ${mainItem.itemGroupName} (${mainItem.id}) ---`);

    // 添加主项目
    result.push(mainItem);
    processedItems.add(mainItem.id);
    console.log(`添加主项目: ${mainItem.itemGroupName} (${mainItem.id})`);

    // 查找并添加该主项目的所有子项目
    const childItems = parentChildMap.get(mainItem.id) || [];
    console.log(`找到 ${childItems.length} 个子项目:`, childItems.map(child => `${child.itemGroupName} (${child.id})`));

    childItems.forEach(childItem => {
      if (!processedItems.has(childItem.id)) {
        console.log(`添加子项目: ${childItem.itemGroupName} (${childItem.id})`);
        result.push(childItem);
        processedItems.add(childItem.id);
      } else {
        console.log(`跳过已处理的子项目: ${childItem.itemGroupName} (${childItem.id})`);
      }
    });
  });

  // 4. 处理没有找到主项目的孤立子项目（按分类排序）
  ['added', 'normal', 'reduced'].forEach(category => {
    const categoryData = categorizedData[category];

    categoryData.forEach(item => {
      if (!processedItems.has(item.id)) {
        const sourceType = getItemSourceType(item.itemGroupId);
        if (sourceType !== 'main') {
          console.log(`添加孤立子项目: ${item.itemGroupName} (${item.id}) - 类型: ${sourceType}`);
          result.push(item);
          processedItems.add(item.id);
        }
      }
    });
  });

  console.log('=== buildHierarchicalStructure 结束 ===');
  console.log('最终结果数量:', result.length);
  console.log('最终结果:', result.map((item, index) => `${index + 1}. ${item.itemGroupName} (${item.id}) - 加减项: ${item.addMinusFlag}`));

  return result;
}

const onRegGroupSearch = debounce(() => {
  // The computed property automatically updates
}, 300);

// 处理右侧项目搜索事件
const handleRegGroupSearch = () => {
  if (allowModifyItems.value) {
    onRegGroupSearch();
  }
};

const regGroupTableState = reactive<{
  selectedRowKeys: Key[];
  selectedRows: CustomerRegItemGroup[];
  loading: boolean;
}>({
  selectedRowKeys: [],
  selectedRows: [],
  loading: false,
});

const onRegGroupTableSelectChange = async (selectedRowKeys: Key[], selectedRows: CustomerRegItemGroup[]) => {
  let newSelectedRowKeys = [...selectedRowKeys];
  let newSelectedRows = [...selectedRows];
  // 判断是否为全选操作
  const isSelectAll = selectedRowKeys.length === filteredRegGroupDataSource.value.length;
  if (!isSelectAll) {
    // 遍历选中的行
    for (const row of selectedRows) {
      if (row.attachGroupIds && row.attachGroupIds.length > 0) {
        const confirmResult = await new Promise<boolean>((resolve) => {
          createConfirm({
            iconType: 'warning',
            title: '确认是否勾选附属项目！',
            content: '选中项目有附属项目，是否勾选附属项目？',
            okText: '确认',
            cancelText: '取消',
            onOk: () => resolve(true),
            onCancel: () => resolve(false),
          });
        });

        if (confirmResult) {
          // 勾选附属项目
          row.attachGroupIds.forEach((attachId) => {
            const attachRow = regGroupDataSource.value.find((item) => item.id === attachId);
            if (attachRow && !newSelectedRowKeys.includes(attachId)) {
              newSelectedRowKeys.push(attachId);
              newSelectedRows.push(attachRow);
            }
          });
        }
      }
    }
  }
  regGroupTableState.selectedRowKeys = newSelectedRowKeys;
  regGroupTableState.selectedRows = newSelectedRows;

};

const totalPrice = computed(() => {
  let amount = regGroupDataSource.value.reduce(
    (total, row) => (row.addMinusFlag != -1 && row.payStatus != '退款成功' ? total + row.price : total),
    0
  );
  return amount.toFixed(2);
});

const totalPriceAfterDis = computed(() => {
  let amount = regGroupDataSource.value.reduce(
    (total, row) => (row.addMinusFlag != -1 && row.payStatus != '退款成功' ? total + row.priceAfterDis : total),
    0
  );
  return amount.toFixed(2);
});
const totalPersonPayAmount = computed(() => {
  return regGroupDataSource.value
    .reduce((total, item) => {
      return item.payerType == '个人支付' ? total + item.priceAfterDis : total;
    }, 0)
    .toFixed(2);
});

const totalCompanyPayAmount = computed(() => {
  return regGroupDataSource.value
    .reduce((total, item) => {
      return item.payerType == '单位支付' && item.addMinusFlag != -1 ? total + item.priceAfterDis : total;
    }, 0)
    .toFixed(2);
});

// 完全使用后端统一依赖关系分析
async function fetchCustomerRegGroupList(id) {
  regGroupLoading.value = true;

  try {
    // 使用后端统一依赖关系分析接口
    console.log('🚀 调用后端统一依赖分析接口，登记ID:', id);
    const response = await getItemGroupWithDependencyAnalysis({ regId: id });
    const analysisResult = response.result || response;

    if (analysisResult && analysisResult.items) {
      // 使用后端返回的完整数据
      regGroupDataSource.value = analysisResult.items;

      // 设置依赖关系摘要
      const summary = analysisResult.summary || {};
      missingDependencies.value = summary.missingDependencies || [];

      // 设置项目来源分析结果
      const sourceMap = new Map();
      regGroupDataSource.value.forEach(item => {
        if (item.sourceType) {
          sourceMap.set(item.itemGroupId, item.sourceType);
        }
      });
      itemSourceMap.value = sourceMap;

      console.log('✅ 后端依赖关系分析完成');
      console.log('   - 项目数量:', regGroupDataSource.value.length);
      console.log('   - 缺失依赖项目数量:', missingDependencies.value.length);
      console.log('   - 项目来源分析完成，类型数量:', sourceMap.size);
      console.log('   - API调用次数: 1次 (优化前需要', regGroupDataSource.value.length + 2, '次)');

      // 调试：打印项目来源类型信息
      console.log('📊 项目来源类型详情:');
      regGroupDataSource.value.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.itemGroupName} (${item.id}) - sourceType: ${item.sourceType || '未设置'}`);
      });
    } else {
      console.error('❌ 后端接口返回数据格式异常:', analysisResult);
      throw new Error('后端接口返回数据格式异常');
    }
  } catch (error) {
    console.error('❌ 获取项目依赖关系分析失败:', error);
    message.error('获取项目列表失败: ' + (error.message || '未知错误'));

    // 清空数据，避免显示错误信息
    regGroupDataSource.value = [];
    missingDependencies.value = [];
    itemSourceMap.value = new Map();
  }

  // 处理团体相关数据（保持原有逻辑）
  await handleTeamRelatedData();

  // 初始化组合的操作类型
  regGroupDataSource.value.forEach((item) => {
    item.uuid = uuidv4();
    item.actionType = getActionType(item);
  });

  regGroupLoading.value = false;
}

// 注意：已移除旧的降级逻辑，完全使用后端统一依赖分析

// 处理团体相关数据的辅助函数
async function handleTeamRelatedData() {
  let teamId = selectedCustomerReg.value?.teamId;
  if (teamId) {
    try {
      const [teamGroups, teamInfo, spendInfo] = await Promise.all([
        getItemGroupByTeam({ teamId: teamId }),
        getTeamById({ id: teamId }),
        getSpendAmount({ idCard: selectedCustomerReg.value.idCard, teamId: teamId })
      ]);

      groupListFromAppoint = teamGroups.map((item: CompanyTeamItemGroup) => {
        return {
          itemGroupId: item.itemGroupId,
        };
      });
      companyTeam.value = teamInfo;
      spendAmount.value = spendInfo;
    } catch (error) {
      console.error('获取团体相关数据失败:', error);
      companyTeam.value = {};
      groupListFromAppoint = [];
    }
  } else {
    companyTeam.value = {};
    groupListFromAppoint = [];
  }

  // 初始化originRegGroupList
  if (teamId && groupListFromAppoint.length > 0) {
    originList = groupListFromAppoint;
  } else {
    let regStatus = selectedCustomerReg.value?.status;
    if (regStatus == '已登记') {
      originList = unref(regGroupDataSource.value);
    } else {
      originList = [];
    }
  }

  // 处理风险因素相关逻辑
  if (customerReg.value.riskFactor) {
    fetchMustCheckItemGorup();
  }
}

function getRemoveType(itemGroup: CustomerRegItemGroup): string {
  let regStatus = selectedCustomerReg.value?.status;
  if (regStatus == '已登记') {
    if (itemGroup.addMinusFlag == 1 || itemGroup.addMinusFlag == 0) {
      return '减项';
    } else if (itemGroup.addMinusFlag == -1) {
      return '反减';
    }
  } else {
    if (selectedCustomerReg.value?.teamId && groupListFromAppoint.length > 0) {
      if (itemGroup.addMinusFlag == 1 || itemGroup.addMinusFlag == 0) {
        return '减项';
      } else if (itemGroup.addMinusFlag == -1) {
        return '反减';
      }
    } else {
      if (itemGroup.addMinusFlag == 1) {
        return '减项';
      } else if (itemGroup.addMinusFlag == -1) {
        return '反减';
      } else if (itemGroup.addMinusFlag == 0) {
        return '删除';
      }
    }
  }
}

function getActionType(customerRegItemGroup: CustomerRegItemGroup): string {
  if (customerRegItemGroup.lockByRefund == '1') {
    return '';
  }
  if (customerRegItemGroup.payStatus == '待支付') {
    return getRemoveType(customerRegItemGroup);
  } else if (customerRegItemGroup.payStatus == '已支付') {
    return '退费';
  }
  return '';
}

function getPayerType4Add(addMinusFlag: number): string {
  if (companyTeam.value?.payerType) {
    addMinusFlag = addMinusFlag != null ? addMinusFlag : getAddMinusFlag4Add();
    if (addMinusFlag == 1) {
      return companyTeam.value.addItemPayerType;
    } else {
      return companyTeam.value.payerType;
    }
  } else {
    return '个人支付';
  }
}

function getAddMinusFlag4Add(): number {
  if (selectedCustomerReg.value?.teamId) {
    if (groupListFromAppoint.length == 0) {
      if (selectedCustomerReg.value?.status == '已登记') {
        return 1;
      } else {
        return 0;
      }
    } else {
      return 1;
    }
  } else {
    if (selectedCustomerReg.value?.status == '已登记') {
      return 1;
    } else {
      return 0;
    }
  }
}

function updateRegGroupBatch(needDisGroup) {
  let regGroupList = needDisGroup.map((item) => {
    return {
      id: item.id,
      customerRegId: unref(customerRegId),
      itemGroupName: item.itemGroupName,
      itemGroupId: item.itemGroupId,
      type: item.type,
      price: item.price,
      disRate: item.disRate,
      priceAfterDis: item.priceAfterDis,
      payerType: item.payerType,
      addMinusFlag: item.addMinusFlag,
      minDiscountRate: item.minDiscountRate,
      priceDisDiffAmount: item.priceDisDiffAmount,
    };
  });
  regGroupLoading.value = true;
  updateItemGroup({ data: regGroupList })
    .then((res) => {
      if (res.success) {
        message.success(res.message);
      }
    })
    .finally(() => {
      regGroupLoading.value = false;
    });
}

function updateRegGroupByOne(record) {
  let data = [
    {
      id: record.id,
      customerRegId: unref(customerRegId),
      itemGroupName: record.itemGroupName,
      itemGroupId: record.itemGroupId,
      type: record.type,
      price: record.price,
      disRate: record.disRate,
      priceAfterDis: record.priceAfterDis,
      payerType: record.payerType,
      addMinusFlag: record.addMinusFlag,
      minDiscountRate: record.minDiscountRate,
      priceDisDiffAmount: record.priceDisDiffAmount,
    },
  ];
  regGroupLoading.value = true;
  updateItemGroup({ data: data })
    .then((res) => {
      if (res.success) {
        message.success(res.message);
      }
    })
    .finally(() => {
      regGroupLoading.value = false;
    });
}

function doActionType(record: CustomerRegItemGroup) {
  if (record.actionType == '删除') {
    removeRegGroupByOne(record);
  } else if (record.actionType == '减项') {
    minusRegGroupByOne(record);
  } else if (record.actionType == '反减') {
    undoMinusRegGroupByOne(record);
  } else if (record.actionType == '退费') {
    //refundFee([record]);
    handleRefund([record]);
  }
}

const removeRegGroupByOne = (record: CustomerRegItemGroup) => {
  let info = {
    regId: unref(customerRegId),
    ids: [record.id],
  };
  removeItemGroup(info)
    .then((res) => {
      //console.log('removeItemGroup', res);
      if (res.success) {
        lastDependencyCheckTime.value = 0;
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success('删除成功');
      } else {
        message.error(res.message);
      }
    })
    .catch((err) => {
      console.log('removeItemGroup', err);
    });
};

const removeRegGroupBatch = () => {
  if (regGroupTableState.selectedRowKeys.length == 0) {
    message.warn('请选择要删除的项目');
    return;
  }
  //筛选出removeType为移除的项目，和removeType为减项的项目
  let removeList = regGroupTableState.selectedRows.filter((item) => item.actionType == '删除');
  //console.log('removeList', removeList);
  //let minusList = regGroupDataSource.value.filter((item) => item.actionType == '减项');
  if (removeList.length == 0) {
    message.warn('没有可删除的项目');
    return;
  }
  //弹出确认框，在弹窗内展示删除的项目
  let removeNames = removeList.map((item, index) => index + 1 + '、' + item.itemGroupName);
  let tipContent = `<div style="max-height: 200px; overflow-y: scroll">${removeNames.join('<br/>')}</div>`;
  createConfirm({
    iconType: 'warning',
    title: '批量删除确认',
    content: tipContent,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      let removeIds = removeList.map((item) => item.id);
      //let minusIds = minusList.map((item) => item.id);
      let info = {
        regId: unref(customerRegId),
        ids: removeIds,
      };
      removeItemGroup(info)
        .then((res) => {
          //console.log('removeItemGroup', res);
          if (res.success) {
            lastDependencyCheckTime.value = 0;
            fetchCustomerRegGroupList(selectedCustomerReg.value.id);
            regGroupTableState.selectedRows = [];
            regGroupTableState.selectedRowKeys = [];
            message.success(`成功删除${removeIds.length}个`);
          } else {
            message.error(res.message);
          }
        })
        .catch((err) => {
          console.log('removeItemGroup', err);
        });
    },
  });
};

const minusRegGroupByOne = (record: CustomerRegItemGroup) => {
  let info = {
    regId: unref(customerRegId),
    ids: [record.id],
  };
  minusItemGroup(info)
    .then((res) => {
      //console.log('removeItemGroup', res);
      if (res.success) {
        lastDependencyCheckTime.value = 0;
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success('操作成功');
      } else {
        message.error(res.message);
      }
    })
    .catch((err) => {
      console.log('minusRegGroupByOne', err);
    });
};

const minusRegGroupBatch = () => {
  if (regGroupTableState.selectedRowKeys.length == 0) {
    message.warn('请选择要减项的项目');
    return;
  }
  //筛选出removeType为移除的项目，和removeType为减项的项目
  let minusList = regGroupTableState.selectedRows.filter((item) => item.actionType == '减项');
  //let minusList = regGroupDataSource.value.filter((item) => item.actionType == '减项');

  //console.log('minusList', minusList);
  if (minusList.length == 0) {
    message.warn('没有可减项的项目');
    return;
  }

  let minusNames = minusList.map((item, index) => index + 1 + '、' + item.itemGroupName);

  createConfirm({
    iconType: 'warning',
    title: '批量减项确认',
    content: minusNames.join('<br/>'),
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      let minusIds = minusList.map((item) => item.id);
      let info = {
        regId: unref(customerRegId),
        ids: minusIds,
      };
      minusItemGroup(info)
        .then((res) => {
          //console.log('removeItemGroup', res);
          if (res.success) {
            lastDependencyCheckTime.value = 0;
            fetchCustomerRegGroupList(selectedCustomerReg.value.id);
            regGroupTableState.selectedRows = [];
            regGroupTableState.selectedRowKeys = [];
            message.success(`成功减项${minusIds.length}个`);
          } else {
            message.error(res.message);
          }
        })
        .catch((err) => {
          console.log('minusRegGroupBatch', err);
        });
    },
  });
};

const undoMinusRegGroupByOne = (record: CustomerRegItemGroup) => {
  let info = {
    regId: unref(customerRegId),
    ids: [record.id],
  };
  undoMinusItemGroup(info)
    .then((res) => {
      //console.log('removeItemGroup', res);
      if (res.success) {
        lastDependencyCheckTime.value = 0;
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success('操作成功');
      } else {
        message.error(res.message);
      }
    })
    .catch((err) => {
      console.log('minusRegGroupByOne', err);
    });
};

function refundFeeBatch() {
  if (regGroupTableState.selectedRowKeys.length == 0) {
    message.warn('请选择要退费的项目');
    return;
  }
  let refundList = regGroupTableState.selectedRows.filter((item) => item.actionType == '退费');
  if (refundList.length == 0) {
    message.warn('没有可退费的项目！');
    return;
  }

  let minusNames = refundList.map((item, index) => index + 1 + '、' + item.itemGroupName);
  createConfirm({
    iconType: 'warning',
    title: '批量退费确认',
    content: minusNames.join('<br/>'),
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      handleRefund(refundList);
    },
  });
}

function setPayerType(payerType: string) {
  if (regGroupTableState.selectedRows.length === 0) {
    message.warn('请选择要设置的项目');
    return;
  }

  let regGroupList = regGroupTableState.selectedRows
    .filter((item) => item.payerType != payerType && item.payStatus == '待支付')
    .map((item) => {
      return {
        id: item.id,
        payerType: payerType,
      };
    });
  if (regGroupList.length == 0) {
    message.warn('没有可设置的项目!');
    return;
  }

  regGroupLoading.value = true;
  updateItemGroup({ data: regGroupList })
    .then((res) => {
      if (res.success) {
        //更新regGroupDataSource
        regGroupDataSource.value.forEach((item) => {
          if (regGroupTableState.selectedRows.some((row) => row.id == item.id)) {
            item.payerType = payerType;
          }
        });
        regGroupTableState.selectedRows = [];
        regGroupTableState.selectedRowKeys = [];
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    })
    .finally(() => {
      regGroupLoading.value = false;
    });
}

function handleRefund(records: CustomerRegItemGroup[]) {
  feeRefundModal.value.fetchData(records, customerReg.value);
}

function handleRefundSuccess() {
  lastDependencyCheckTime.value = 0;
  fetchCustomerRegGroupList(selectedCustomerReg.value.id);
  regGroupTableState.selectedRows = [];
  regGroupTableState.selectedRowKeys = [];
}

const undoMinusRegGroupBatch = () => {
  if (regGroupTableState.selectedRowKeys.length == 0) {
    message.warn('请选择要反减的项目');
    return;
  }
  //筛选出removeType为移除的项目，和removeType为减项的项目
  let minusList = regGroupTableState.selectedRows.filter((item) => item.actionType == '反减');
  //let minusList = regGroupDataSource.value.filter((item) => item.actionType == '减项');

  //console.log('minusList', minusList);
  if (minusList.length == 0) {
    message.warn('没有可反减的项目');
    return;
  }

  let minusNames = minusList.map((item, index) => index + 1 + '、' + item.itemGroupName);

  createConfirm({
    iconType: 'warning',
    title: '批量反减确认',
    content: minusNames.join('<br/>'),
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      let minusIds = minusList.map((item) => item.id);
      let info = {
        regId: unref(customerRegId),
        ids: minusIds,
      };
      undoMinusItemGroup(info)
        .then((res) => {
          //console.log('removeItemGroup', res);
          if (res.success) {
            lastDependencyCheckTime.value = 0;
            fetchCustomerRegGroupList(selectedCustomerReg.value.id);
            regGroupTableState.selectedRows = [];
            regGroupTableState.selectedRowKeys = [];
            message.success(`成功反减${minusIds.length}个`);
          } else {
            message.error(res.message);
          }
        })
        .catch((err) => {
          console.log('minusRegGroupBatch', err);
        });
    },
  });
};

function handlePriceChange(record, event) {
  if (event.target.value == record.priceAfterDis) {
    return;
  }

  let priceAfterDis = Number(event.target.value);
  //判断折后价是否正确
  let rate = record.price != 0 ? priceAfterDis / record.price : 0;
  rate = parseFloat(rate.toFixed(2));
  let minDiscountRate = parseFloat(record.minDiscountRate) || 0;
  if (rate < minDiscountRate) {
    let minPrice = parseFloat((record.price * minDiscountRate).toFixed(2));
    createErrorModal({ title: '操作失败', content: '折后价过低，该项目可设置的最低折后价为' + minPrice + '元！' });
    fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    return;
  }

  record.priceAfterDis = priceAfterDis;
  record.disRate = rate;
  updateRegGroupByOne(record);
}

function handleDisRateChange(record, event) {
  if (event.target.value == record.disRate) {
    return;
  }
  let rate = Number(event.target.value);
  let minDiscountRate = record.minDiscountRate || 0;
  if (rate < minDiscountRate) {
    createErrorModal({ title: '操作失败', content: '折扣率不能小于最低折扣率' + minDiscountRate });
    fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    return;
  }
  record.disRate = parseFloat(rate.toFixed(2));
  record.priceAfterDis = parseFloat((record.price * rate).toFixed(2));
  updateRegGroupByOne(record);
}

function updateType(record) {
  if (record.type == null) {
    message.error('请选择类型');
    return;
  }
  updateRegGroupByOne(record);
}

function updateMinDiscountRate(record, event) {
  if (event.target.value == record.minDiscountRate) {
    return;
  }
  if (record.minDiscountRate < 0) {
    createErrorModal({ title: '操作失败', content: '最低折扣率不能小于0' });
    fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    return;
  }
  let rate = Number(event.target.value);
  let disRate = record.disRate || 0;
  if (disRate < rate) {
    createErrorModal({ title: '操作失败', content: '最低折扣率不能大于折扣率' + disRate });
    fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    return;
  }
  record.minDiscountRate = parseFloat(rate.toFixed(2));
  updateRegGroupByOne(record);
}

function updatePayerType(record) {
  if (record.payerType == null) {
    message.error('请选择支付方');
    return;
  }
  updateRegGroupByOne(record);
}

/**中间按钮相关*/
const removeBtnEnable = computed<Boolean>(() => regGroupTableState.selectedRows.some((item) => item.actionType == '删除'));
const minusBtnEnable = computed<Boolean>(() => regGroupTableState.selectedRows.some((item) => item.actionType == '减项'));
const undoMinusBtnEnable = computed<Boolean>(() => regGroupTableState.selectedRows.some((item) => item.actionType == '反减'));
const refundEnable = computed<Boolean>(() => regGroupTableState.selectedRows.some((item) => item.actionType == '退费'));

/**左侧体检项目组合相关操作*/
const groupItemModal = ref();

function showItem(record) {
  groupItemModal.value.showDetail(record.id);
}

const selectedDepartId = ref<string | null>(null);
const groupLoading = ref<boolean>(false);

const searchGroupByDepartment = (depart) => {
  if (depart.isLeaf) {
    selectedDepartId.value = depart.id;
    queryParam.departmentId = depart.id;
  } else {
    selectedDepartId.value = '';
    queryParam.departmentId = '';
  }
  reload();
};

// 处理部门树选择事件
const handleDepartTreeSelect = (depart) => {
  if (allowModifyItems.value) {
    searchGroupByDepartment(depart);
  }
};

const searchGroupByKeyword = (val) => {
  reload();
};

// 处理搜索输入事件
const handleSearchInput = () => {
  if (allowModifyItems.value) {
    throttleSearchGroupByKeyword();
  }
};

// 处理搜索按钮点击事件
const handleSearchKeyword = (val) => {
  if (allowModifyItems.value) {
    searchGroupByKeyword(val);
  }
};

const throttleSearchGroupByKeyword = throttle(searchGroupByKeyword, 300);

const addSuitableItems = (items) => {
  const addList = items.map((item) => generateCustomerRegItemGroup(item)).filter((item) => item !== null);

  if (addList.length === 0) {
    message.warn('所选项目已均已存在！');
    return;
  }

  addCustomerReg(addList)
    .then((res) => {
      if (res.success) {
        // 清除依赖检查缓存，确保重新检查
        lastDependencyCheckTime.value = 0;
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success(`成功添加${addList.length}条！`);
      } else {
        message.error(res.message);
      }
    })
    .finally(() => {
      regGroupLoading.value = false;
    });
};

// 通过套餐设置组合（优化版：使用后端统一依赖分析）
const setGroupBySuit = (suit) => {
  /*if (selectedCustomerReg.value?.teamId) {
    message.warn('单位预约人员不能使用套餐');
    return;
  }*/
  if (!isSuitAvailable(suit, customerReg.value)) {
    message.warn('套餐不适用当前体检人！');
    return;
  }

  createConfirm({
    iconType: 'warning',
    title: '使用套餐确认',
    content: `套餐内项目将与现有项目合并，确定使用 ${suit.name} 吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      groupLoading.value = true;

      try {
        console.log('🚀 使用套餐，套餐ID:', suit.id);

        // 使用现有的套餐项目列表接口（已包含关系数据）
        const response = await getItemGroupBySuit({ suitId: suit.id });
        const analysisResult = response.result || response;

        if (analysisResult && analysisResult.suitGroupList) {
          console.log('✅ 使用后端统一依赖分析处理套餐项目');
          await handleSuitItemsWithDependencyAnalysis(analysisResult.suitGroupList, suit);
        } else {
          console.warn('⚠️ 后端接口返回数据格式异常，降级到旧逻辑');
          await handleSuitItemsLegacy(suit);
        }
      } catch (error) {
        console.error('❌ 套餐依赖分析失败，降级到旧逻辑:', error);
        await handleSuitItemsLegacy(suit);
      }
    },
  });
};

// 新的套餐项目处理逻辑：使用后端依赖分析结果
async function handleSuitItemsWithDependencyAnalysis(suitItems, suit) {
  try {
    // 建立父子关系映射
    const parentChildMap = buildParentChildMapForSuitItems(suitItems);

    // 构建层级结构，确保父子项目正确排序
    const hierarchicalItems = buildHierarchicalStructureForSuitItems(suitItems, parentChildMap);

    console.log('📊 套餐项目层级结构构建完成');
    console.log('   - 项目总数:', hierarchicalItems.length);
    console.log('   - 父子关系映射:', parentChildMap.size, '个父项目');

    // 按层级顺序处理项目
    await processSuitItemsInOrder(hierarchicalItems, suit);

  } catch (error) {
    console.error('❌ 套餐项目依赖分析处理失败:', error);
    message.error('套餐使用失败: ' + (error.message || '未知错误'));
    groupLoading.value = false;
  }
}

// 降级函数：保持原有逻辑作为备用
async function handleSuitItemsLegacy(suit) {
  console.log('使用旧版套餐项目处理逻辑');

  const res = await getGroupOfSuit({ suitId: suit.id })
  try {
    //console.log('套餐中的项目：', res);
    let addList = [];
    let unsuitableItems = [];
    let groupList = unref(regGroupDataSource);

    let needPartSelectionItems = [];

    res.forEach((group) => {
      // 检查项目是否需要部位选择
      if (group.hasCheckPart === '1') {
        // 如果套餐中已经预设了部位信息，则可以直接添加
        if (group.checkPartId && group.checkPartName) {
          console.log(`套餐中的项目 ${group.name} 使用预设部位：${group.checkPartName}`);
          // 继续处理，不跳过
        } else {
          console.log(`套餐中的项目 ${group.name} 需要部位选择，跳过自动添加`);
          needPartSelectionItems.push(group);
          return;
        }
      }

      // 检查项目是否已存在 - 考虑部位信息
      const isDuplicate = checkItemGroupDuplicate(groupList, group);
      if (isDuplicate) {
        console.log(`项目 ${group.name} 已存在，跳过添加`);
        return;
      }

      const { isValid, errorMessage } = isItemGroupAvailable(group, customerReg.value);
      if (!isValid) {
        unsuitableItems.push({ group, errorMessage });
      } else {
        group.priceAfterDis = group.priceAfterDisOfSuit;
        group.minDiscountRate = group.minDiscountRateOfSuit;
        group.disRate = group.disRateOfSuit;
        group.priceDisDiffAmount = group.priceDisDiffAmountOfSuit;
        let data = generateCustomerRegItemGroup(group);
        if (data != null) {
          data.itemSuitName = suit.name;
          data.itemSuitId = suit.id;
          addList.push(data);
        }
      }
    });

    // 处理需要部位选择的项目
    if (needPartSelectionItems.length > 0) {
      console.log('Found items needing part selection:', needPartSelectionItems);
      // 先处理可以直接添加的项目
      if (addList.length > 0) {
        proceedToAddItems(addList);
      }
      // 然后显示部位选择弹窗
      suitPartModalRef.value?.open(needPartSelectionItems);
      return;
    }

    // Handle unsuitable items
    if (unsuitableItems.length > 0) {
      const unsuitableNames = unsuitableItems.map((item) => item.group.name).join(', ');
      const errorMessages = unsuitableItems.map((item) => item.errorMessage).join('<br/>');
      createConfirm({
        iconType: 'warning',
        title: '不适用项目确认',
        content: h('div', { style: 'max-height: 50vh; overflow-y: auto' }, [
          h('p', `以下项目不适用：${unsuitableNames}。`),
          h('p', { innerHTML: errorMessages }),
          h('p', '是否继续添加适用的项目？'),
        ]),
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          proceedToAddItems(addList);
        },
        onCancel: () => {
          groupLoading.value = false;
        },
      });
    } else {
      if (addList.length == 0) {
        message.warn('套餐内项目已均已存在，无法使用该套餐！');
        groupLoading.value = false;
        return;
      }
      proceedToAddItems(addList);
    }
  } catch (error) {
    console.error('旧版套餐项目处理失败:', error);
    message.error('套餐使用失败: ' + (error.message || '未知错误'));
    groupLoading.value = false;
  }
}

// 为套餐项目建立父子关系映射
function buildParentChildMapForSuitItems(suitItems) {
  const parentChildMap = new Map();

  console.log('🔍 开始为套餐项目建立父子关系映射...');

  suitItems.forEach(item => {
    const sourceType = item.sourceType || 'main';

    if (sourceType !== 'main') {
      // 这是一个子项目，需要找到它的父项目
      const parentItem = findParentItemForSuitItems(item, suitItems);
      if (parentItem) {
        const parentId = parentItem.id;
        if (!parentChildMap.has(parentId)) {
          parentChildMap.set(parentId, []);
        }
        parentChildMap.get(parentId).push(item);
        console.log(`   ✅ 套餐父子关系: ${parentItem.name} -> ${item.name} (${sourceType})`);
      } else {
        console.log(`   ⚠️ 套餐项目未找到父项目: ${item.name} (${sourceType})`);
      }
    }
  });

  console.log(`🎯 套餐父子关系映射完成，共找到 ${parentChildMap.size} 个父项目`);
  return parentChildMap;
}

// 为套餐项目找到父项目
function findParentItemForSuitItems(childItem, allItems) {
  const childSourceType = childItem.sourceType || 'main';

  // 遍历所有项目，找到包含此子项目的父项目
  for (const potentialParent of allItems) {
    if (potentialParent.id === childItem.id) continue;

    const parentSourceType = potentialParent.sourceType || 'main';
    if (parentSourceType !== 'main') continue;

    // 检查父项目的依赖关系数据
    if (isChildOfParentForSuitItems(childItem, potentialParent, childSourceType)) {
      return potentialParent;
    }
  }

  return null;
}

// 检查套餐子项目是否属于父项目
function isChildOfParentForSuitItems(childItem, parentItem, childSourceType) {
  // 基于后端返回的依赖关系数据进行判断
  switch (childSourceType) {
    case 'dependent':
      return parentItem.dependentGroups &&
             parentItem.dependentGroups.some(dep => dep.relationGroupId === childItem.id);

    case 'attach':
      return parentItem.attachGroups &&
             parentItem.attachGroups.some(attach => attach.relationGroupId === childItem.id);

    case 'gift':
      return parentItem.giftGroups &&
             parentItem.giftGroups.some(gift => gift.relationGroupId === childItem.id);

    default:
      // 降级到原有的字段判断逻辑
      return childItem.attachBaseId === parentItem.id ||
             childItem.parentGroupId === parentItem.id ||
             (childItem.itemSuitId && parentItem.itemSuitId && childItem.itemSuitId === parentItem.itemSuitId);
  }
}

// 构建套餐项目的层级结构
function buildHierarchicalStructureForSuitItems(suitItems, parentChildMap) {
  console.log('=== buildHierarchicalStructureForSuitItems 开始 ===');

  const result = [];
  const processedItems = new Set();

  // 找出所有主项目
  const mainItems = suitItems.filter(item => (item.sourceType || 'main') === 'main');
  console.log('套餐主项目数量:', mainItems.length);

  // 处理每个主项目及其子项目
  mainItems.forEach(mainItem => {
    if (processedItems.has(mainItem.id)) return;

    console.log(`--- 处理套餐主项目: ${mainItem.name} (${mainItem.id}) ---`);

    // 添加主项目
    result.push(mainItem);
    processedItems.add(mainItem.id);

    // 查找并添加该主项目的所有子项目
    const childItems = parentChildMap.get(mainItem.id) || [];
    console.log(`找到 ${childItems.length} 个子项目:`, childItems.map(child => child.name));

    // 对子项目进行排序
    const sortedChildren = sortChildItemsForSuitItems(childItems);

    // 添加排序后的子项目
    sortedChildren.forEach(childItem => {
      if (!processedItems.has(childItem.id)) {
        result.push(childItem);
        processedItems.add(childItem.id);
      }
    });
  });

  // 处理孤立的子项目（没有找到父项目的）
  const orphanItems = suitItems.filter(item =>
    (item.sourceType || 'main') !== 'main' && !processedItems.has(item.id)
  );

  if (orphanItems.length > 0) {
    console.log('发现孤立的子项目:', orphanItems.map(item => item.name));
    orphanItems.forEach(item => {
      result.push(item);
      processedItems.add(item.id);
    });
  }

  console.log('=== buildHierarchicalStructureForSuitItems 完成 ===');
  console.log('最终项目顺序:', result.map(item => `${item.name} (${item.sourceType || 'main'})`));

  return result;
}

// 对套餐子项目进行排序
function sortChildItemsForSuitItems(childItems) {
  return childItems.sort((a, b) => {
    const aType = a.sourceType || 'main';
    const bType = b.sourceType || 'main';

    const typeOrder = { dependent: 1, gift: 2, attach: 3, suit: 4 };
    const aOrder = typeOrder[aType] || 999;
    const bOrder = typeOrder[bType] || 999;

    if (aOrder !== bOrder) {
      return aOrder - bOrder;
    }

    // 同类型按创建时间排序
    const aTime = a.createTime ? new Date(a.createTime).getTime() : 0;
    const bTime = b.createTime ? new Date(b.createTime).getTime() : 0;
    return aTime - bTime;
  });
}

// 按层级顺序处理套餐项目
async function processSuitItemsInOrder(hierarchicalItems, suit) {
  console.log('📋 开始按层级顺序处理套餐项目...');

  let addList = [];
  let unsuitableItems = [];
  let needPartSelectionItems = [];
  let groupList = unref(regGroupDataSource);

  hierarchicalItems.forEach((group, index) => {
    console.log(`处理第 ${index + 1} 个项目: ${group.name} (${group.sourceType || 'main'})`);

    // 检查项目是否需要部位选择
    if (group.hasCheckPart === '1') {
      // 如果套餐中已经预设了部位信息，则可以直接添加
      if (group.checkPartId && group.checkPartName) {
        console.log(`套餐中的项目 ${group.name} 使用预设部位：${group.checkPartName}`);
        // 继续处理，不跳过
      } else {
        console.log(`套餐中的项目 ${group.name} 需要部位选择，跳过自动添加`);
        needPartSelectionItems.push(group);
        return;
      }
    }

    // 检查项目是否已存在 - 考虑部位信息
    const isDuplicate = checkItemGroupDuplicate(groupList, group);
    if (isDuplicate) {
      console.log(`项目 ${group.name} 已存在，跳过添加`);
      return;
    }

    const { isValid, errorMessage } = isItemGroupAvailable(group, customerReg.value);
    if (!isValid) {
      unsuitableItems.push({ group, errorMessage });
    } else {
      group.priceAfterDis = group.priceAfterDisOfSuit;
      group.minDiscountRate = group.minDiscountRateOfSuit;
      group.disRate = group.disRateOfSuit;
      group.priceDisDiffAmount = group.priceDisDiffAmountOfSuit;
      let data = generateCustomerRegItemGroup(group);
      if (data != null) {
        data.itemSuitName = suit.name;
        data.itemSuitId = suit.id;
        addList.push(data);
      }
    }
  });

  console.log('📊 套餐项目处理完成统计:');
  console.log('   - 可添加项目:', addList.length);
  console.log('   - 不适用项目:', unsuitableItems.length);
  console.log('   - 需要部位选择:', needPartSelectionItems.length);

  // 处理需要部位选择的项目
  if (needPartSelectionItems.length > 0) {
    console.log('Found items needing part selection:', needPartSelectionItems);
    // 先处理可以直接添加的项目
    if (addList.length > 0) {
      proceedToAddItems(addList);
    }
    // 然后显示部位选择弹窗
    suitPartModalRef.value?.open(needPartSelectionItems);
    return;
  }

  // Handle unsuitable items
  if (unsuitableItems.length > 0) {
    const unsuitableNames = unsuitableItems.map((item) => item.group.name).join(', ');
    const errorMessages = unsuitableItems.map((item) => item.errorMessage).join('<br/>');
    createConfirm({
      iconType: 'warning',
      title: '不适用项目确认',
      content: h('div', { style: 'max-height: 50vh; overflow-y: auto' }, [
        h('p', `以下项目不适用：${unsuitableNames}。`),
        h('p', { innerHTML: errorMessages }),
        h('p', '是否继续添加适用的项目？'),
      ]),
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        proceedToAddItems(addList);
      },
      onCancel: () => {
        groupLoading.value = false;
      },
    });
  } else {
    if (addList.length == 0) {
      message.warn('套餐内项目已均已存在，无法使用该套餐！');
      groupLoading.value = false;
      return;
    }
    proceedToAddItems(addList);
  }
}

// Helper function to proceed with adding items
function proceedToAddItems(addList, isFromSuit = false) {
  if (addList.length == 0) {
    groupLoading.value = false;
    return;
  }

  // 如果是套餐添加，直接保存项目，不处理赠送和附属项目
  if (isFromSuit) {
    // 套餐添加：调用专用API，跳过赠送和附属项目逻辑
    addCustomerRegForSuit(addList)
      .then(async (res) => {
        if (res.success) {
          fetchCustomerRegGroupList(selectedCustomerReg.value.id);
          selectedRows.value = [];
          message.success(`成功添加套餐项目 ${addList.length} 条！（套餐中已包含赠送和附属项目）`);

          // 套餐添加后也进行依赖检查
          const addedItems = addList.map(item => ({
            id: item.itemGroupId,
            name: item.itemGroupName,
            checkPartId: item.checkPartId,
            checkPartName: item.checkPartName
          }));
          await checkDependenciesAfterAdd(addedItems);
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {
        regGroupLoading.value = false;
        groupLoading.value = false;
      });
  } else {
    // 普通添加：使用原有逻辑，会自动处理赠送和附属项目
    addCustomerReg(addList)
      .then(async (res) => {
        if (res.success) {
          fetchCustomerRegGroupList(selectedCustomerReg.value.id);
          selectedRows.value = [];
          message.success(`成功添加 ${addList.length} 条！`);

          // 普通添加后进行依赖检查
          const addedItems = addList.map(item => ({
            id: item.itemGroupId,
            name: item.itemGroupName,
            checkPartId: item.checkPartId,
            checkPartName: item.checkPartName
          }));
          await checkDependenciesAfterAdd(addedItems);
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {
        regGroupLoading.value = false;
        groupLoading.value = false;
      });
  }
}

/** 对登记中的体检项目进行整体设置*/
interface BatchState {
  disRate: number | null;
  priceAfterDis: number | null;
  type: string;
}

const batchFormState = reactive<BatchState>({
  disRate: null,
  priceAfterDis: null,
  type: '健康项目',
});

const batchTip = computed(() => {
  if (batchFormState.disRate !== null) {
    return `确定要将所有项目的折扣率设置为${batchFormState.disRate}吗？`;
  } else if (batchFormState.priceAfterDis !== null) {
    return `确定要将所有项目的折后价设置为${batchFormState.priceAfterDis}元吗？`;
  }
  return '确定将所有项目的类型设置为' + batchFormState.type + '吗？';
});

function updateByBatch() {
  console.log('所有项目：', regGroupDataSource);
  //实际参与打折的项目
  const needDisGroup = regGroupDataSource.value.filter((row) => row.addMinusFlag !== -1 && row.payStatus === '待支付');
  if (!needDisGroup || needDisGroup.length == 0) {
    message.warn('没有可以设置的项目！');
    return;
  }
  if (
    (batchFormState.disRate === null || batchFormState.disRate === undefined) &&
    (batchFormState.priceAfterDis === null || batchFormState.priceAfterDis === undefined)
  ) {
    message.warn('请设置批量调整条件！');
    return;
  }

  /*  if (customerReg.value.paymentState !== '待支付') {
    message.warn('当前的支付状态下，无法进行整体设置！');
    return;
  }*/

  if (batchFormState.disRate !== null) {
    // 更新每一行的折扣率和折后价格
    const inputRate = Number(batchFormState.disRate);
    needDisGroup.forEach((row) => {
      // 确保折扣率不低于最小折扣率
      const rate = inputRate > row.minDiscountRate ? inputRate : row.minDiscountRate;
      row.disRate = rate;
      row.priceAfterDis = parseFloat((row.price * rate).toFixed(2));
    });
  } else if (batchFormState.priceAfterDis !== null) {
    //目标价格
    const targetTotalPrice = Number(batchFormState.priceAfterDis);
    //总原价
    // const originalTotalPrice = regGroupDataSource.value.reduce((total, row) => total + row.price, 0);
    const originalTotalPrice = needDisGroup.reduce((total, row) => total + row.price, 0);
    //总差价
    const totalDiffAmount = targetTotalPrice - originalTotalPrice;

    if (totalDiffAmount < 0) {
      //可折扣到的最低价格
      // const minAvailableTotalPrice = regGroupDataSource.value.reduce((total, row) => {
      //   const minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
      //   return total + minPrice;
      // }, 0);
      const minAvailableTotalPrice = needDisGroup.reduce((total, row) => {
        const minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
        return total + minPrice;
      }, 0);
      //可以折扣的最多差价
      const totalAvailableDecrease = originalTotalPrice - minAvailableTotalPrice;

      if (-totalDiffAmount > totalAvailableDecrease) {
        createErrorModal({
          title: '操作失败',
          content: '设定的折后总价小于可设置的最小总价 ' + minAvailableTotalPrice.toFixed(2) + ' 元，无法执行该操作！',
        });
        return;
      }

      // 分配减少的金额
      // regGroupDataSource.value.forEach((row) => {
      needDisGroup.forEach((row) => {
        const minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
        const availableDecrease = row.price - minPrice;
        const decreaseAmount = (availableDecrease / totalAvailableDecrease) * -totalDiffAmount;
        row.priceAfterDis = parseFloat((row.price - decreaseAmount).toFixed(2));
        row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
      });
    } else if (totalDiffAmount > 0) {
      // 需要增加价格
      // 暂不考虑价格上限
      needDisGroup.forEach((row) => {
        const increaseAmount = (row.price / originalTotalPrice) * totalDiffAmount;
        row.priceAfterDis = parseFloat((row.price + increaseAmount).toFixed(2));
        row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
      });
    } else {
      needDisGroup.forEach((row) => {
        row.priceAfterDis = row.price;
        row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
      });
    }

    // 调整因舍入造成的差异
    let finalTotalAmount = needDisGroup.reduce((total, row) => total + row.priceAfterDis, 0);
    let finalDiff = parseFloat((targetTotalPrice - finalTotalAmount).toFixed(2));

    if (finalDiff !== 0) {
      needDisGroup[0].priceAfterDis = parseFloat((needDisGroup[0].priceAfterDis + finalDiff).toFixed(2));
      needDisGroup[0].disRate = needDisGroup[0].price !== 0 ? parseFloat((needDisGroup[0].priceAfterDis / needDisGroup[0].price).toFixed(2)) : 0;
    }
  }

  // 更新类型
  needDisGroup.forEach((row) => {
    row.type = batchFormState.type;
  });
  updateRegGroupBatch(needDisGroup);
}

function updateByBatchBackup() {
  if (batchFormState.disRate == null && batchFormState.priceAfterDis == null && batchFormState.type == null) {
    message.warn('请填写折扣率或折后总价！');
    return;
  }
  //判断是否全都是待支付项目
  if (!regGroupDataSource.value.every((item) => item.payStatus == '待支付')) {
    message.warn('当前项目列表不支持整体设置！');
    return;
  }

  //let availabeList = regGroupDataSource.value.filter((item) => item.payStatus == '待支付' && item.lockByRefund != '1' && item.addMinusFlag != -1);
  if (batchFormState.disRate !== null) {
    // 更新每一行的折扣率和折后价格
    let rate = Number(batchFormState.disRate);
    regGroupDataSource.value.forEach((row) => {
      //console.log('折扣率：', rate, '最小折扣率：', row.minDiscountRate);
      // 检查折扣率是否大于最大折扣率
      rate = rate > row.minDiscountRate ? rate : row.minDiscountRate;
      row.disRate = rate;
      row.priceAfterDis = parseFloat((row.price * rate).toFixed(2));
    });
  } else if (batchFormState.priceAfterDis !== null) {
    let targetTotalPrice = Number(batchFormState.priceAfterDis);

    // 计算可以折扣到的最低总价
    const minTotalPrice = regGroupDataSource.value.reduce((total, row) => {
      let minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
      return total + minPrice;
    }, 0);
    // 检查用户设定的折后总价是否大于最低总价
    if (targetTotalPrice < minTotalPrice) {
      // 弹出提示框
      createErrorModal({ title: '操作失败', content: '设定的折后总价小于可设置的最小总价' + minTotalPrice.toFixed(2) + '元，无法执行该操作！' });
      return;
    }
    // 计算总原价
    const totalItemPrice = regGroupDataSource.value.reduce((total, row) => total + row.price, 0);
    //计算可供折扣的总价
    const totalAvailableAmount4Dis = totalItemPrice - minTotalPrice;
    // 计算差价
    let diff = totalItemPrice - targetTotalPrice;
    // 按照每个项目可供折扣的金额与总可供折扣的金额的比例来分配差价
    regGroupDataSource.value.forEach((row) => {
      let minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
      let availableAmount4Dis = row.price - minPrice;
      let diffAmount = (availableAmount4Dis / totalAvailableAmount4Dis) * diff;
      row.priceAfterDis = parseFloat((row.price - diffAmount).toFixed(2));
      row.disRate = row.price != 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
    });
    //计算最终的差价，将差价放到还可以减掉该差价的项目中，并将该项目的priceDisDiffAmount设置为最终的差价
    let finalTotalAmount = regGroupDataSource.value.reduce((total, row) => total + row.priceAfterDis, 0);
    let finalDiff = targetTotalPrice - finalTotalAmount;

    //console.log('最终差价：', finalDiff);
    if (finalDiff != 0) {
      //找到可以减掉该差价的项目
      let availableRow2Minus = regGroupDataSource.value.find((row) => {
        //计算可供折扣的差价
        if (row.disRate > row.minDiscountRate) {
          let availableAmount4Dis = row.price * (row.disRate - row.minDiscountRate);
          return availableAmount4Dis > finalDiff;
        }
        return false;
      });
      if (availableRow2Minus) {
        availableRow2Minus.priceAfterDis = parseFloat((availableRow2Minus.priceAfterDis + finalDiff).toFixed(2));
        availableRow2Minus.priceDisDiffAmount = finalDiff;
        //console.log('可以减掉差价的项目：', availableRow2Minus);
      }
    }
  }
  // 更新类型
  regGroupDataSource.value.forEach((row) => {
    row.type = batchFormState.type;
  });
  updateRegGroupBatch();
}

const reset = () => {
  batchFormState.disRate = null;
  batchFormState.priceAfterDis = null;
  batchFormState.type = '健康项目';
};

function generateCustomerRegItemGroup(row) {
  let existItem = regGroupDataSource.value.find((item) => item.itemGroupId === row.id);
  //如果existItem不存在，或者存在但是已退款，则可以添加，获取是仅收费项目
  let valid = row.chargeItemOnlyFlag == '1' || !existItem || existItem.payStatus == '退款成功';
  if (valid) {
    let addMinusFlag = getAddMinusFlag4Add();
    let payerType = getPayerType4Add(addMinusFlag);

    regGroupLoading.value = true;
    let data: CustomerRegItemGroup = {
      uuid: uuidv4(),
      customerRegId: unref(customerRegId),
      examNo: customerReg.value.examNo,
      itemGroupName: row.name,
      itemGroupId: row.id,
      hisCode: row.hisCode,
      hisName: row.hisName,
      platCode: row.platCode,
      platName: row.platName,
      classCode: row.classCode,
      type: row.type ?? '健康项目',
      disRate: row.disRate != undefined && row.disRate != null ? row.disRate : 1,
      price: row.price,
      priceAfterDis: row.priceAfterDis != undefined && row.priceAfterDis != null ? row.priceAfterDis : row.price,
      payerType: payerType,
      departmentId: row.departmentId,
      departmentName: row.departmentName,
      departmentCode: row.departmentCode,
      addMinusFlag: addMinusFlag,
      payStatus: '待支付',
      minDiscountRate: row.minDiscountRate,
      priceDisDiffAmount: row.priceDisDiffAmount || 0,
    };
    data.actionType = getActionType(data);

    return data;
  } else {
    return null;
  }
}

// 添加选中的行添加到登记项目列表中
const handleAddOne = (row) => {
  // 防止重复点击
  if (regGroupLoading.value) {
    console.log('正在添加项目，请勿重复点击');
    return;
  }

  const { isValid, errorMessage } = isItemGroupAvailable(row, customerReg.value);
  if (!isValid) {
    message.warn(errorMessage);
    return;
  }

  // 检查是否需要部位选择
  if (row.hasCheckPart === '1') {
    showCheckPartSelector(row);
    return;
  }

  // 使用统一的判重逻辑
  const isDuplicate = checkItemGroupDuplicate(regGroupDataSource.value, row);
  if (isDuplicate) {
    message.warn(`${row.name}已经存在`);
    return;
  }

  // 设置loading状态，防止重复点击
  regGroupLoading.value = true;

  let data = generateCustomerRegItemGroup(row);
  if (data == null) {
    message.warn(`${row.name}已经存在`);
    regGroupLoading.value = false;
    return;
  }

  console.log('开始添加项目:', row.name, '请求数据:', data);

  addCustomerReg([data])
    .then((res) => {
      if (res.success) {
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success('添加成功');
      } else {
        message.error(res.message);
      }
    })
    .catch((error) => {
      console.error('添加项目失败:', error);
      message.error('添加项目失败，请重试');
    })
    .finally(() => {
      regGroupLoading.value = false;
    });
};

const addSelectedBatch = () => {
  if (selectedRows.value.length === 0) {
    message.warn('请选择要添加的项目');
    return;
  }

  const unsuitableItems = [];
  const suitableItems = [];

  selectedRows.value.forEach((row) => {
    const { isValid, errorMessage } = isItemGroupAvailable(row, customerReg.value);
    if (!isValid) {
      unsuitableItems.push({ row, errorMessage });
    } else {
      suitableItems.push(row);
    }
  });

  if (unsuitableItems.length > 0) {
    const unsuitableNames = unsuitableItems.map((item) => item.row.name).join(', ');
    const errorMessages = unsuitableItems.map((item) => item.errorMessage).join('<br/>');
    createConfirm({
      iconType: 'warning',
      title: '不适用项目确认',
      content: h('div', { style: 'max-height: 50vh; overflow-y: auto' }, [
        h('p', `以下项目不适用：${unsuitableNames}。`),
        h('p', { innerHTML: errorMessages }), // Adjust based on the content of errorMessages
        h('p', '是否继续添加适用的项目？'),
      ]),
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        addSuitableItems(suitableItems);
        selectedRows.value = [];
        regGroupTableState.selectedRowKeys = [];
      },
    });
  } else {
    addSuitableItems(suitableItems);
    selectedRows.value = [];
    regGroupTableState.selectedRowKeys = [];
  }
};

const handleAddBatch = (rows) => {
  if (rows.length == 0) {
    message.warn('请选择要添加的项目');
    return;
  }
  let addList = [];
  let unsuitableItems = [];
  let groupList = unref(regGroupDataSource);
  rows.forEach((row) => {
    // 检查项目是否已存在 - 考虑部位信息
    const isDuplicate = checkItemGroupDuplicate(groupList, row);
    if (isDuplicate) {
      console.log(`项目 ${row.name} 已存在，跳过添加`);
      return;
    }

    const { isValid, errorMessage } = isItemGroupAvailable(row, customerReg.value);
    if (!isValid) {
      unsuitableItems.push({ row, errorMessage });
    } else {
      let data = generateCustomerRegItemGroup(row);
      if (data != null) {
        addList.push(data);
      }
    }
  });

  // 处理不适用的项目
  if (unsuitableItems.length > 0) {
    const unsuitableNames = unsuitableItems.map((item) => item.row.name).join(', ');
    const errorMessages = unsuitableItems.map((item) => item.errorMessage).join('<br/>');
    createConfirm({
      iconType: 'warning',
      title: '不适用项目确认',
      content: h('div', { style: 'max-height: 50vh; overflow-y: auto' }, [
        h('p', `以下项目不适用：${unsuitableNames}。`),
        h('p', { innerHTML: errorMessages }),
        h('p', '是否继续添加适用的项目？'),
      ]),
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        proceedToAddItems(addList);
      },
    });
  } else {
    if (addList.length == 0) {
      message.warn('已存在的项目已均已存在，无法使用该套餐！');
      return;
    }
    proceedToAddItems(addList);
  }
};

function handleCancel() {
  visiable.value = false;
  emits('cancel');
}

function open() {
  visiable.value = true;
  regGroupDataSource.value = [];
  originList = [];
  selectedRows.value = [];
  selectedRowKeys.value = [];

  regGroupTableState.selectedRowKeys = [];
  regGroupTableState.selectedRows = [];
  // 清空依赖项目提示
  missingDependencies.value = [];
  // 清空依赖检查缓存，确保重新检查
  lastDependencyCheckTime.value = 0;

  if (selectedCustomerReg.value && selectedCustomerReg.value.id) {
    fetchCustomerRegGroupList(selectedCustomerReg.value.id);

    // 延迟执行依赖检查，确保数据完全加载
    setTimeout(async () => {
      console.log('open函数中延迟执行依赖检查，项目数量:', regGroupDataSource.value.length);
      if (regGroupDataSource.value.length > 0) {
        await checkAllDependencies(true);
      }
    }, 200); // 稍微增加延迟时间
  }
}

// 项目重复检查逻辑（考虑部位信息和减项处理）
function checkItemGroupDuplicate(groupList: CustomerRegItemGroup[], newItem: ItemGroup): boolean {
  // 仅收费项目允许重复添加
  if (newItem.chargeItemOnlyFlag === '1') {
    console.log(`项目 ${newItem.name} 是仅收费项目，允许重复添加`);
    return false;
  }

  // 分别处理减项和正常项目
  const minusItems = groupList.filter(item =>
    item.itemGroupId === newItem.id &&
    item.addMinusFlag === -1 &&
    item.payStatus !== '退款成功'
  );

  const normalItems = groupList.filter(item =>
    item.itemGroupId === newItem.id &&
    item.addMinusFlag !== -1 &&
    item.payStatus !== '退款成功'
  );

  // 如果项目不需要部位选择
  if (newItem.hasCheckPart !== '1') {
    // 如果存在减项，提示反减
    if (minusItems.length > 0) {
      console.log(`项目 ${newItem.name} 存在减项记录，建议进行反减操作`);
      message.warning(`项目"${newItem.name}"存在减项记录，建议进行反减操作而不是重新添加`);
      return true; // 阻止添加
    }

    // 检查是否存在正常项目
    return normalItems.length > 0;
  }

  // 如果项目需要部位选择，但新项目没有部位信息，则认为不重复（需要通过部位选择添加）
  if (!newItem.checkPartId) {
    // 但如果存在减项，仍需提示
    if (minusItems.length > 0) {
      const minusPartNames = minusItems.map(item => item.checkPartName || '未知部位').join('、');
      console.log(`项目 ${newItem.name} 存在减项记录（部位：${minusPartNames}），建议进行反减操作`);
      message.warning(`项目"${newItem.name}"存在减项记录（部位：${minusPartNames}），建议进行反减操作`);
      return true; // 阻止添加
    }
    return false;
  }

  // 检查特定部位的减项
  const specificMinusItems = minusItems.filter(item => item.checkPartId === newItem.checkPartId);
  if (specificMinusItems.length > 0) {
    const partName = newItem.checkPartName || '未知部位';
    console.log(`项目 ${newItem.name} 的部位 ${partName} 存在减项记录，建议进行反减操作`);
    message.warning(`项目"${newItem.name}"的部位"${partName}"存在减项记录，建议进行反减操作而不是重新添加`);
    return true; // 阻止添加
  }

  // 检查是否存在相同项目和相同部位的正常组合
  return normalItems.some(item => item.checkPartId === newItem.checkPartId);
}

// 部位选择相关方法
async function showCheckPartSelector(itemGroup: ItemGroup) {
  console.log('Showing check part selector for item group:', itemGroup);

  // 重置状态
  checkPartState.currentItemGroup = itemGroup;
  checkPartState.selectedParts = [];
  checkPartState.options = [];
  checkPartState.loading = false;

  // 显示模态框
  checkPartState.visible = true;

  // 加载部位选项
  await loadCheckParts(itemGroup.id);

  // 自动聚焦到选择器
  nextTick(() => {
    checkPartSelectRef.value?.focus();
  });
}

// 部位选项过滤函数，支持helpChar检索
function filterPartOption(input: string, option: any) {
  // 空输入时显示所有选项
  if (!input || !input.trim()) {
    return true;
  }

  const searchText = input.toLowerCase().trim();
  const label = option.label || '';
  const partData = option.partData || {};
  let helpChar = partData.helpChar || '';
  const code = partData.code || '';
  const name = partData.name || partData.partName || '';

  // 多字段模糊匹配：标签、名称、拼音缩写、编码
  const matchLabel = label.toLowerCase().includes(searchText);
  const matchName = name.toLowerCase().includes(searchText);
  const matchHelpChar = helpChar.toLowerCase().includes(searchText);
  const matchCode = code.toLowerCase().includes(searchText);

  const isMatch = matchLabel || matchName || matchHelpChar || matchCode;

  return isMatch;
}

async function loadCheckParts(itemGroupId: string, keyword?: string) {
  checkPartState.loading = true;
  try {
    console.log('Loading check parts for itemGroupId:', itemGroupId, 'keyword:', keyword);
    // 不再使用keyword参数进行服务端过滤，而是加载所有数据供前端过滤
    const params = { itemGroupId };

    const res = await listByItemGroup(params);
    console.log('API response:', res);

    // 处理不同的响应格式
    if (Array.isArray(res)) {
      console.log('Response is array, length:', res.length);

      if (res.length > 0) {
        console.log('First item:', res[0]);

        checkPartState.options = res.map((item: any) => {
          const frequency = item.frequency || 0;
          const name = item.name || '';
          const id = item.id || '';

          const option = {
            label: name,
            value: id,
            frequency: frequency,
            partData: item // 保存完整的部位数据供过滤使用
          };
          return option;
        });

        console.log('✓ Options mapped successfully:', checkPartState.options);
        console.log('✓ Options count:', checkPartState.options.length);
      } else {
        console.log('⚠ Array is empty');
        checkPartState.options = [];
        message.warning('没有找到可用的部位选项');
      }
    }
    // 处理包装对象格式
    else if (res && res.success && Array.isArray(res.result)) {
      console.log('Response is wrapped object, result length:', res.result.length);

      if (res.result.length > 0) {
        console.log('First item:', res.result[0]);

        checkPartState.options = res.result.map((item: any) => {
          const frequency = item.frequency || 0;
          const name = item.name || '';
          const id = item.id || '';

          const option = {
            label: frequency > 0 ? `${name} (${frequency}次)` : name,
            value: id,
            frequency: frequency,
            partData: item // 保存完整的部位数据供过滤使用
          };
          return option;
        });

        console.log('✓ Options mapped successfully:', checkPartState.options);
        console.log('✓ Options count:', checkPartState.options.length);
      } else {
        console.log('⚠ Result array is empty');
        checkPartState.options = [];
        message.warning('没有找到可用的部位选项');
      }
    }
    // 处理其他格式
    else {
      console.log('⚠ Unexpected response format:', res);
      checkPartState.options = [];
      message.warning('响应格式异常，无法加载部位选项');
    }
  } catch (error) {
    console.error('Load check parts error:', error);
    message.error('加载部位选项失败: ' + error.message);
    checkPartState.options = [];
  } finally {
    checkPartState.loading = false;
  }
}

// 移除searchCheckParts函数，现在使用前端过滤而不是服务端搜索

function closeCheckPartSelector() {
  checkPartState.visible = false;
  checkPartState.currentItemGroup = null;
  checkPartState.selectedParts = [];
  checkPartState.options = [];
}

// 处理模态框键盘事件
function handleModalKeydown(event: KeyboardEvent) {
  // 在模态框中按Ctrl+Enter时，如果有选中项目则保存
  if (event.key === 'Enter' && event.ctrlKey && checkPartState.selectedParts.length > 0) {
    event.preventDefault();
    confirmAddItemWithParts();
  }
}

// 处理选择器键盘事件
function handleSelectKeydown(event: KeyboardEvent) {
  // 在选择器中按Enter时，如果有选中项目则保存
  if (event.key === 'Enter' && event.ctrlKey && checkPartState.selectedParts.length > 0) {
    event.preventDefault();
    confirmAddItemWithParts();
  }
}

// 根据ID获取部位名称
function getPartNameById(partId: string): string {
  const option = checkPartState.options.find(opt => opt.value === partId);
  return option ? option.label : partId;
}

async function confirmAddItemWithParts() {
  // 验证必要条件
  if (!checkPartState.currentItemGroup) {
    message.warn('项目信息丢失，请重新选择');
    closeCheckPartSelector();
    return;
  }

  if (checkPartState.selectedParts.length === 0) {
    message.warn('请至少选择一个检查部位');
    // 聚焦到选择器
    nextTick(() => {
      checkPartSelectRef.value?.focus();
    });
    return;
  }

  checkPartState.loading = true;
  try {
    const selectedPartNames = checkPartState.selectedParts.map(id => getPartNameById(id));
    console.log('Adding item with parts:', {
      itemGroup: checkPartState.currentItemGroup.name,
      parts: selectedPartNames
    });

    // 前端负责数据拼装
    const itemGroups = [];
    const parentGroupId = checkPartState.currentItemGroup.id; // 使用项目ID作为父组ID，用于关联同一项目的不同部位

    for (const partId of checkPartState.selectedParts) {
      const partName = getPartNameById(partId);
      const partInfo = checkPartState.options.find(opt => opt.value === partId);

      // 创建临时项目对象用于重复检查
      const tempItem = {
        ...checkPartState.currentItemGroup,
        checkPartId: partId,
        checkPartName: partName
      };

      // 使用现有的重复检查方法（会自动排除耗材）
      const isDuplicate = checkItemGroupDuplicate(regGroupDataSource.value, tempItem);
      if (isDuplicate) {
        message.warn(`${checkPartState.currentItemGroup.name} - ${partName} 已存在`);
        continue;
      }

      // 使用现有的generateCustomerRegItemGroup方法生成基础数据
      const baseData = generateCustomerRegItemGroup(checkPartState.currentItemGroup);
      if (!baseData) {
        continue;
      }

      // 设置部位相关信息
      baseData.checkPartId = partId;
      baseData.checkPartName = partName;
      baseData.checkPartCode = partInfo?.code || '';
      baseData.parentGroupId = parentGroupId;
      baseData.itemGroupName = `${checkPartState.currentItemGroup.name}`;

      itemGroups.push(baseData);
    }

    if (itemGroups.length === 0) {
      message.warn('没有可添加的项目-部位组合');
      return;
    }

    // 前端互斥检查
    const mutexCheck = await checkItemMutex(itemGroups, regGroupDataSource.value);
    if (!mutexCheck.isValid) {
      const conflictMsg = formatConflictMessage(mutexCheck.conflicts);
      message.error('项目冲突：\n' + conflictMsg);
      return;
    }

    if (mutexCheck.warning) {
      message.warning(mutexCheck.warning);
    }

    const params = {
      customerRegId: customerRegId.value,
      itemGroups: itemGroups
    };

    const res = await addItemGroupWithCheckParts(params);
    if (res.success) {
      const partNames = selectedPartNames.join('、');
      message.success(`成功添加 ${checkPartState.currentItemGroup.name} - ${partNames}`);

      // 刷新列表
      await fetchCustomerRegGroupList(selectedCustomerReg.value.id);

      // 添加项目后进行依赖检查
      await checkDependenciesAfterAdd(itemGroups);

      closeCheckPartSelector();
    } else {
      message.error(res.message || '添加失败，请重试');
    }
  } catch (error) {
    console.error('Add item with parts error:', error);

    // 处理数据库约束错误（重复项目）
    if (error.message && (error.message.includes('Duplicate') || error.message.includes('已存在') || error.message.includes('重复'))) {
      message.warning('项目可能已被其他用户添加，正在刷新数据...');
      // 自动刷新数据
      await fetchCustomerRegGroupList(selectedCustomerReg.value.id);
      closeCheckPartSelector();
    } else {
      message.error('添加失败: ' + error.message);
    }
  } finally {
    checkPartState.loading = false;
  }
}

// 处理套餐部位补充确认
async function handleSuitPartConfirm(itemsWithParts: ItemGroup[]) {
  console.log('Suit part selection confirmed:', itemsWithParts);

  try {
    groupLoading.value = true;
    let addList = [];
    let groupList = unref(regGroupDataSource);
    let duplicateItems: string[] = [];
    let invalidItems: string[] = [];

    itemsWithParts.forEach((group) => {
      // 检查项目是否已存在 - 考虑部位信息
      const isDuplicate = checkItemGroupDuplicate(groupList, group);
      if (isDuplicate) {
        duplicateItems.push(`${group.name} - ${group.checkPartName}`);
        return;
      }

      const { isValid, errorMessage } = isItemGroupAvailable(group, customerReg.value);
      if (!isValid) {
        invalidItems.push(`${group.name}: ${errorMessage}`);
        return;
      }

      let data = generateCustomerRegItemGroup(group);
      if (data != null) {
        // 设置部位信息
        data.checkPartId = group.checkPartId;
        data.checkPartName = group.checkPartName;
        data.itemGroupName = `${group.name}-${group.checkPartName}`;
        addList.push(data);
      }
    });

    if (addList.length > 0) {
      const res = await addCustomerReg(addList);
      if (res.success) {
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success(`成功添加 ${addList.length} 个项目`);
      } else {
        message.error(res.message);
      }
    } else {
      // 根据具体失败原因给出准确提示
      if (duplicateItems.length > 0 && invalidItems.length === 0) {
        message.warn(`以下项目-部位组合已存在：${duplicateItems.join('、')}`);
      } else if (invalidItems.length > 0 && duplicateItems.length === 0) {
        message.warn(`以下项目不符合添加条件：${invalidItems.join('、')}`);
      } else if (duplicateItems.length > 0 && invalidItems.length > 0) {
        message.warn(`部分项目已存在，部分项目不符合添加条件。已存在：${duplicateItems.join('、')}；不符合条件：${invalidItems.join('、')}`);
      } else {
        message.warn('没有可添加的项目');
      }
    }
  } catch (error) {
    console.error('Add suit items with parts error:', error);
    message.error('添加失败: ' + error.message);
  } finally {
    groupLoading.value = false;
  }
}

// 处理套餐部位补充取消
function handleSuitPartCancel() {
  console.log('Suit part selection cancelled');
  groupLoading.value = false;
}

// 获取项目显示名称（包含部位信息）
function getDisplayItemName(record: CustomerRegItemGroup): string {
  if (record.checkPartName) {
    // 如果项目名称已经包含部位信息，直接返回
    if (record.itemGroupName.includes(record.checkPartName)) {
      return record.itemGroupName;
    }
    // 否则添加部位信息
    return `${record.itemGroupName}-${record.checkPartName}`;
  }
  return record.itemGroupName;
}

// 获取完整的项目名称（用于tooltip）
function getFullItemName(record: CustomerRegItemGroup): string {
  let fullName = record.itemGroupName;
  if (record.checkPartName && !fullName.includes(record.checkPartName)) {
    fullName += ` (部位: ${record.checkPartName})`;
  }
  return fullName;
}

// 格式化项目名称显示（包含部位信息）
function formatItemDisplayName(itemName: string, checkPartName?: string): string {
  if (!checkPartName) {
    return itemName;
  }
  return `${itemName}[${checkPartName}]`;
}

// 获取项目名称的完整显示文本（用于tooltip）
function getItemTooltipText(itemName: string, checkPartName?: string): string {
  return formatItemDisplayName(itemName, checkPartName);
}

// 项目关系显示模式配置
const relationDisplayMode = ref('prefix'); // 'prefix' | 'badge' | 'both' | 'none'

// 项目关系映射缓存
const itemRelationshipMap = ref(new Map());

// 主项目到子项目的映射缓存
const mainToChildrenMap = ref(new Map());

// 控制是否显示详细的badge
const showDetailedBadges = computed(() => {
  return relationDisplayMode.value === 'badge' || relationDisplayMode.value === 'both';
});

// 获取项目关系前缀显示配置
function getItemRelationPrefix(itemGroupId) {
  console.log(`getItemRelationPrefix called for ${itemGroupId}, relationDisplayMode: ${relationDisplayMode.value}`);

  if (relationDisplayMode.value === 'badge' || relationDisplayMode.value === 'none') {
    console.log(`返回null，因为显示模式是 ${relationDisplayMode.value}`);
    return null;
  }

  const sourceType = getItemSourceType(itemGroupId);
  console.log(`项目 ${itemGroupId} 的sourceType: ${sourceType}`);

  // 提供多种前缀样式
  const prefixStyles = {
    'tree': { dependent: '├─', gift: '├─', attach: '├─', suit: '├─' },
    'arrow': { dependent: '→', gift: '→', attach: '→', suit: '→' },
    'dot': { dependent: '●', gift: '●', attach: '●', suit: '●' },
    'icon': { dependent: '🔗', gift: '🎁', attach: '📎', suit: '📦' }
  };

  const currentStyle = 'tree'; // 可以做成配置项
  const prefixText = prefixStyles[currentStyle];

  switch (sourceType) {
    case 'dependent':
      console.log(`返回依赖项目前缀 for ${itemGroupId}`);
      return {
        text: prefixText.dependent,
        color: '#fa8c16',
        title: '依赖项目：由其他项目的依赖关系自动添加'
      };
    case 'gift':
      console.log(`返回赠送项目前缀 for ${itemGroupId}`);
      return {
        text: prefixText.gift,
        color: '#52c41a',
        title: '赠送项目：由其他项目的赠送关系自动添加'
      };
    case 'attach':
      console.log(`返回附属项目前缀 for ${itemGroupId}`);
      return {
        text: prefixText.attach,
        color: '#722ed1',
        title: '附属项目：由其他项目的附属关系自动添加'
      };
    case 'suit':
      console.log(`返回套餐项目前缀 for ${itemGroupId}`);
      return {
        text: prefixText.suit,
        color: '#1890ff',
        title: '套餐项目：来自套餐配置'
      };
    default:
      console.log(`返回null，因为sourceType是 ${sourceType} for ${itemGroupId}`);
      return null;
  }
}

// 切换显示模式
function toggleRelationDisplayMode() {
  const modes = ['prefix', 'badge', 'both', 'none'];
  const currentIndex = modes.indexOf(relationDisplayMode.value);
  const nextIndex = (currentIndex + 1) % modes.length;
  relationDisplayMode.value = modes[nextIndex];

  // 保存用户偏好
  localStorage.setItem('relationDisplayMode', relationDisplayMode.value);
}

// 获取显示模式的文本描述
function getDisplayModeText() {
  const modeTexts = {
    'prefix': '前缀',
    'badge': '标签',
    'both': '全部',
    'none': '隐藏'
  };
  return modeTexts[relationDisplayMode.value] || '前缀';
}

// 获取显示模式的图标
function getDisplayModeIcon() {
  const modeIcons = {
    'prefix': h(BranchesOutlined),
    'badge': h(TagOutlined),
    'both': h(AppstoreOutlined),
    'none': h(EyeInvisibleOutlined)
  };
  return modeIcons[relationDisplayMode.value] || h(BranchesOutlined);
}

// 获取显示模式的提示信息
function getDisplayModeTooltip() {
  const modeTooltips = {
    'prefix': '前缀模式：使用 ├─ 符号显示项目关系',
    'badge': '标签模式：使用彩色标签显示项目关系',
    'both': '完整模式：同时显示前缀和标签',
    'none': '隐藏模式：不显示项目关系标识'
  };
  return modeTooltips[relationDisplayMode.value] + '（点击切换）';
}

onMounted(() => {
  // Apply stored styles on component mount
  if (storedTableStyle && storedTableStyle.length > 0) {
    applyStripedStyles(storedTableStyle);
  }

  dependencyChecker.value = new DependencyChecker(regGroupDataSource);

  // 预加载常用项目的关系数据
  preloadCommonItemRelations();

  // 初始化显示模式
  const savedMode = localStorage.getItem('relationDisplayMode');
  if (savedMode && ['prefix', 'badge', 'both', 'none'].includes(savedMode)) {
    relationDisplayMode.value = savedMode;
  }
});

defineExpose({
  open,
});
</script>
<style lang="less" scoped>
/* 使用 CSS 变量设置条纹背景颜色 */
.ant-table-striped {
  --striped-odd-color: #f9f9f9; /* 默认奇数行颜色 */
  --striped-even-color: #ffffff; /* 默认偶数行颜色 */
  --font-color-odd: #000000; /* 默认奇数行字体颜色 */
  --font-color-even: #000000; /* 默认偶数行字体颜色 */
}

/* 奇数行样式 */
.ant-table-striped :deep(.odd-row) td {
  background-color: var(--striped-odd-color) !important;
  color: var(--font-color-odd) !important;
}

/* 偶数行样式 */
.ant-table-striped :deep(.even-row) td {
  background-color: var(--striped-even-color) !important;
  color: var(--font-color-even) !important;
}

.ant-table-striped :deep(.odd-row) td input,
.ant-table-striped :deep(.even-row) td input {
  background-color: inherit;
  border: 1px solid #d9d9d9 !important;
}

:deep(.ant-table-cell) {
  padding: 1px !important;
}

.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }

  .ant-modal-body {
    flex: 1;
  }
}
.no-select {
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none; /* Safari and older Chrome */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer and Edge */
}

/* Custom styles to reduce padding */
:deep(.ant-table-thead > tr > th),
:deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px; /* Adjust these values as needed */
}

:deep(.ant-table) {
  font-size: 12px; /* Reduce font size if desired */
}

:deep(.ant-table .ant-select-selector) {
  padding: 0 4px; /* Adjust padding for select inputs inside the table */
}

:deep(.ant-table input[type='number']),
:deep(.ant-table input[type='text']) {
  padding: 0 4px;
  height: 24px; /* Adjust height if necessary */
}

:deep(.ant-table .ant-input-number) {
  padding: 0 4px;
  height: 24px;
}

:deep(.ant-table .ant-checkbox-wrapper .ant-checkbox) {
  zoom: 0.8; /* Reduce checkbox size */
}
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }

  .ant-modal-body {
    flex: 1;
  }
}
.no-select {
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none; /* Safari and older Chrome */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer and Edge */
}
.added-item {
  color: #cf1322;
}
.removed-item {
  color: #389e0d;
  text-decoration: line-through #135200;
}

/* 依赖项目提示区域样式 */
.missing-dependencies-alert {
  .missing-dependencies-content {
    .alert-title {
      font-weight: 600;
      margin-bottom: 8px;
      display: block;
    }

    .missing-projects-list {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }
  }
}
</style>
