import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  commonList = '/reg/customerReg/commonList',
  list = '/reg/customerReg/list',
  list4Occu = '/reg/customerReg/list4Occu',
  list4CompanyReg = '/reg/customerReg/list4CompanyReg',
  save = '/reg/customerReg/add',
  edit = '/reg/customerReg/edit',
  deleteOne = '/reg/customerReg/delete',
  deleteBatch = '/reg/customerReg/deleteBatch',
  importExcel = '/reg/customerReg/importExcel',
  exportXls = '/reg/customerReg/exportXls',
  getItemGroupByCustomerRegId = '/reg/customerReg/getItemGroupByCustomerRegId',
  saveItemGroupOfCustomerReg = '/reg/customerReg/saveItemGroupOfCustomerReg',
  regBatch = '/reg/customerReg/regBatch',
  regOne = '/reg/customerReg/reg',
  handleAddItem = '/reg/customerReg/handleAddItem',
  savePrintInfo = '/reg/customerReg/savePrintInfo',
  removeItemGroup = '/reg/customerReg/removeItemGroup',
  minusItemGroup = '/reg/customerReg/minusItemGroup',
  undoMinusItemGroup = '/reg/customerReg/undoMinusItemGroup',
  addCustomerReg = '/reg/customerReg/addItemGroup',
  addCustomerRegForSuit = '/reg/customerReg/addItemGroupForSuit',
  updateItemGroup = '/reg/customerReg/updateItemGroup',
  setItemGroupBySuit = '/reg/customerReg/setItemGroupBySuit',
  getCustomerRegDetail = '/reg/customerReg/getCustomerRegDetail',
  updateGuidancePrintTimes = '/reg/customerReg/updateGuidancePrintTimes',
  getGuidanceSheet = '/reg/customerReg/getGuidanceSheet',
  retrieve = '/reg/customerReg/retrieve',
  getGroupList4Retrieve = '/reg/customerReg/getGroupList4Retrieve',
  undoRetrieve = '/reg/customerReg/undoRetrieve',
  unReg = '/reg/customerReg/unRegCustomerReg',
  stat = '/reg/customerReg/stat',
  queryById = '/reg/customerReg/queryById',
  getItemGroupsByRegIdAndTemplateId = '/reg/customerReg/getItemGroupsByRegIdAndTemplateId',
  listByCondition = '/reg/customerReg/listByCondition',
  getByIds = '/reg/customerReg/getByIds',
  sendFee2His = '/reg/customerReg/sendFee2His',
  sendFee2HisByIds = '/reg/customerReg/sendFee2HisByIds',
  verifyAndSaveReport = '/reg/customerReg/verifyAndSaveReport',
  updateReportPrintStatusById = '/reg/customerReg/updateReportPrintStatusById',
  markReportAsTaken = '/reg/customerReg/markReportAsTaken',
  getSpendAmount = '/reg/customerReg/getSpendAmount',
  getRemainingAmount = '/reg/customerReg/getRemainingAmount',
  getTeamInfoByIdCard = '/reg/customerReg/getTeamInfoByIdCard',
  updateHealthQuestId = '/reg/customerReg/updateHealthQuestId',
  markAsTakenBatch = '/reg/customerReg/markAsTakenBatch',
  verifyBatch = '/reg/customerReg/verifyBatch',
  listRecipeByRegId = '/fee/customerRegBill/listByRegId',
  assignSummaryDoctorBatch = '/reg/customerReg/assignSummaryDoctorBatch',
  analysisPayment = '/fee/customerRegBill/analysisPayment',
  getLastNYearsReg = '/reg/customerReg/getLastNYearsReg',
  resetState4ReGeneratePdf = '/reg/customerReg/resetState4ReGeneratePdf',
  updateApplyPrintTimes = '/reg/customerReg/updateApplyPrintTimes',
  companyNotify = '/reg/customerReg/companyNotify',
  changeItemGroupByCompanyTeam = '/reg/customerReg/changeItemGroupByCompanyTeam',
  sendPsyNotify = '/reg/customerReg/sendPsyNotify',
  getItemGroupOfTeam = '/reg/companyReg/getItemGroupOfTeam',
  sendReportNotify = '/reg/customerReg/sendReportNotify',
  regenratePdf = '/reg/customerReg/regeneratePdf',
  updateReportId = '/reg/customerReg/updateReportId',
  sendItemGroup2Interface = '/reg/customerReg/sendItemGroup2Interface',
  checkIsSummary = '/reg/customerReg/checkIsSummary',
  getTeamAndLimitInfoByIdCard = '/reg/customerReg/getTeamAndLimitInfoByIdCard',
  getTeamLimitListByTeamId = '/fee/teamCustomerLimitAmount/list',
  addItemGroupWithCheckParts = '/reg/customerReg/addItemGroupWithCheckParts',
  getRiskFactorsByWorkType = '/occu/zyRiskFactor/getRiskFactorsByWorktypeCode',
  filterRiskFactorsByJobStatus = '/occu/zyRiskFactor/filterRiskFactorsByJobStatus',
  getItemGroupWithDependencyAnalysis = '/reg/customerReg/getItemGroupWithDependencyAnalysis',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

export const list4Occu = (params) => defHttp.get({ url: Api.list4Occu, params });

export const list4CompanyReg = (params) => defHttp.get({ url: Api.list4CompanyReg, params });

/**
 * 通用列表接口
 * @param params
 */
export const commonList = (params) => defHttp.get({ url: Api.commonList, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 列表接口
 * @param params
 */
export const getItemGroupByCustomerRegId = (params) => defHttp.get({ url: Api.getItemGroupByCustomerRegId, params });

export const saveItemGroupOfCustomerReg = (params) => {
  return defHttp.post({ url: Api.saveItemGroupOfCustomerReg, params }, { isTransformResponse: false });
};

/**
 * 注册
 * @param params
 */
export const regBatch = (params) => {
  return defHttp.post({ url: Api.regBatch, params }, { isTransformResponse: false });
};

/**
 * 保存打印信息
 * @param params
 */
export const savePrintInfo = (params) => {
  return defHttp.post({ url: Api.savePrintInfo, params }, { isTransformResponse: false });
};

export const removeItemGroup = (params) => {
  return defHttp.post({ url: Api.removeItemGroup, data: params }, { isTransformResponse: false });
};

export const minusItemGroup = (params) => {
  return defHttp.post({ url: Api.minusItemGroup, data: params }, { isTransformResponse: false });
};

export const undoMinusItemGroup = (params) => {
  return defHttp.post({ url: Api.undoMinusItemGroup, data: params }, { isTransformResponse: false });
};

export const addCustomerReg = (params) => {
  return defHttp.post({ url: Api.addCustomerReg, data: params }, { isTransformResponse: false });
};

export const addCustomerRegForSuit = (params) => {
  return defHttp.post({ url: Api.addCustomerRegForSuit, data: params }, { isTransformResponse: false });
};

export const updateItemGroup = (params) => {
  return defHttp.post({ url: Api.updateItemGroup, data: params }, { isTransformResponse: false });
};

export const setItemGroupBySuit = (params) => {
  return defHttp.post({ url: Api.setItemGroupBySuit, data: params }, { isTransformResponse: false });
};

export const getCustomerRegDetail = (params) => {
  return defHttp.get({ url: Api.getCustomerRegDetail, params });
};

export const updateGuidancePrintTimes = (params) => {
  return defHttp.get({ url: Api.updateGuidancePrintTimes, params });
};

export const getGuidanceSheet = (params) => {
  return defHttp.get({ url: Api.getGuidanceSheet, params });
};

export const retrieve = (params) => {
  return defHttp.post({ url: Api.retrieve, params });
};

export const getGroupList4Retrieve = (params) => {
  return defHttp.get({ url: Api.getGroupList4Retrieve, params });
};

export const undoRetrieve = (params) => {
  return defHttp.get({ url: Api.undoRetrieve, params });
};

export const unReg = (params) => {
  return defHttp.get({ url: Api.unReg, params });
};

export const stat = () => {
  return defHttp.get({ url: Api.stat }, { isTransformResponse: false });
};

export const queryById = (params) => {
  return defHttp.get({ url: Api.queryById, params });
};

export const getItemGroupsByRegIdAndTemplateId = (params) => {
  return defHttp.get({ url: Api.getItemGroupsByRegIdAndTemplateId, params }, { isTransformResponse: false });
};

export const listByCondition = (params) => {
  return defHttp.get({ url: Api.listByCondition, params });
};

export const getByIds = (params) => {
  return defHttp.get({ url: Api.getByIds, params });
};

export const sendFee2His = (params) => {
  return defHttp.get({ url: Api.sendFee2His, params });
};

export const sendFee2HisByIds = (params) => {
  return defHttp.post({ url: Api.sendFee2HisByIds, params }, { isTransformResponse: false });
};

export const verifyAndSaveReport = (params) => {
  return defHttp.post({ url: Api.verifyAndSaveReport, params }, { isTransformResponse: false });
};

export const markReportAsTaken = (params) => {
  return defHttp.post({ url: Api.markReportAsTaken, params }, { isTransformResponse: false });
};

export const updateReportPrintStatusByIds = (params) => {
  return defHttp.get({ url: Api.updateReportPrintStatusById, params }, { isTransformResponse: false });
};
export const updateApplyPrintTimes = (params) => {
  return defHttp.post({ url: Api.updateApplyPrintTimes, params }, { isTransformResponse: false });
};

export const getSpendAmount = (params) => {
  return defHttp.get({ url: Api.getSpendAmount, params });
};
export const getRemainingAmount = (params) => {
  return defHttp.get({ url: Api.getRemainingAmount, params });
};
/**
 * 根据身份证号查询
 * @param params
 */
export const getTeamInfoByIdCard = (params) => defHttp.get({ url: Api.getTeamInfoByIdCard, params }, { isTransformResponse: false });

export const updateHealthQuestId = (params) => {
  return defHttp.get({ url: Api.updateHealthQuestId, params }, { isTransformResponse: false });
};

export const markAsTakenBatch = (params) => {
  return defHttp.post({ url: Api.markAsTakenBatch, params }, { isTransformResponse: false });
};
export const resetState4ReGeneratePdf = (params) => {
  return defHttp.post({ url: Api.resetState4ReGeneratePdf, params }, { isTransformResponse: false });
};

export const verifyBatch = (params) => {
  return defHttp.post({ url: Api.verifyBatch, params }, { isTransformResponse: false });
};

export const listRecipeByRegId = (params) => {
  return defHttp.get({ url: Api.listRecipeByRegId, params }, { isTransformResponse: false });
};

export const assignSummaryDoctorBatch = (params) => {
  return defHttp.post({ url: Api.assignSummaryDoctorBatch, params }, { isTransformResponse: false });
};

export const analysisPayment = (params) => {
  return defHttp.get({ url: Api.analysisPayment, params });
};

export const regOne = (params) => {
  return defHttp.get({ url: Api.regOne, params }, { isTransformResponse: false });
};

export const handleAddItem = (params) => {
  return defHttp.get({ url: Api.handleAddItem, params }, { isTransformResponse: false });
};

export const getLastNYearsReg = (params) => {
  return defHttp.get({ url: Api.getLastNYearsReg, params });
};

export const batchNotify = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认发送',
    content: '确认要给该团检下符合条件的人员发送团检通知？',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.post({ url: Api.companyNotify, data: params }, { joinParamsToUrl: true, isTransformResponse: false }).then((res) => {
        handleSuccess(res);
      });
    },
  });
};
export const changeItemGroupByCompanyTeam = (params) => {
  return defHttp.get({ url: Api.changeItemGroupByCompanyTeam, params }, { isTransformResponse: false });
};
export const sendPsyNotify = (params) => {
  return defHttp.get({ url: Api.sendPsyNotify, params }, { isTransformResponse: false });
};

export const sendReportNotify = (params) => {
  return defHttp.get({ url: Api.sendReportNotify, params }, { isTransformResponse: false });
};

export const regenratePdf = (params) => {
  return defHttp.get({ url: Api.regenratePdf, params }, { isTransformResponse: false });
};

export const updateReportId = (params) => {
  return defHttp.get({ url: Api.updateReportId, params }, { isTransformResponse: false });
};

export const getItemGroupOfTeam = (params) => {
  return defHttp.get({ url: Api.getItemGroupOfTeam, params }, { isTransformResponse: false });
};

export const sendItemGroup2Interface = (params) => {
  return defHttp.post({ url: Api.sendItemGroup2Interface, data: params }, { isTransformResponse: false });
};
export const checkIsSummary = (params) => {
  return defHttp.get({ url: Api.checkIsSummary, params }, { isTransformResponse: false });
};

/**
 * 添加带检查部位的项目组合
 * @param params
 */
export const addItemGroupWithCheckParts = (params) => {
  return defHttp.post({ url: Api.addItemGroupWithCheckParts, data: params }, { isTransformResponse: false });
};

export const getTeamAndLimitInfoByIdCard = (params) => defHttp.get({ url: Api.getTeamAndLimitInfoByIdCard, params }, { isTransformResponse: false });

export const getTeamLimitListByTeamId = (params) => defHttp.get({ url: Api.getTeamLimitListByTeamId, params });

/**
 * 查询项目依赖关系
 * @param params
 */
export const getDependenciesByGroupId = (params) => defHttp.get({ url: '/reg/customerReg/getDependenciesByGroupId', params });

/**
 * 批量查询项目依赖关系
 * @param data
 */
export const getDependenciesByGroupIds = (data) => defHttp.post({ url: '/basicinfo/itemGroupRelation/getDependenciesByGroupIds', data });

/**
 * 批量查询依赖项目结果
 * @param params
 */
export const getDependentItemResultsBatch = (params) =>
  defHttp.post({
    url: '/reg/customerReg/getDependentItemResultsBatch',
    params,
  });

/**
 * 根据工种获取关联的危害因素
 * @param params
 */
export const getRiskFactorsByWorkType = (params) => defHttp.get({ url: Api.getRiskFactorsByWorkType, params }, { isTransformResponse: false });

/**
 * 根据岗位类别筛选危害因素
 * @param params
 */
export const filterRiskFactorsByJobStatus = (params) =>
  defHttp.get({ url: Api.filterRiskFactorsByJobStatus, params }, { isTransformResponse: false });

/**
 * 获取带完整依赖关系分析的项目列表
 * @param params
 */
export const getItemGroupWithDependencyAnalysis = (params) =>
  defHttp.get({ url: Api.getItemGroupWithDependencyAnalysis, params });
