import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/reg/companyReg/list',
  save = '/reg/companyReg/add',
  edit = '/reg/companyReg/edit',
  deleteOne = '/reg/companyReg/delete',
  deleteBatch = '/reg/companyReg/deleteBatch',
  importExcel = '/reg/companyReg/importExcel',
  exportXls = '/reg/companyReg/exportXls',
  companyTeamList = '/reg/companyReg/listCompanyTeamByMainId',
  companyTeamSave = '/reg/companyReg/addCompanyTeam',
  companyTeamEdit = '/reg/companyReg/editCompanyTeam',
  companyTeamDelete = '/reg/companyReg/deleteCompanyTeam',
  companyTeamDeleteBatch = '/reg/companyReg/deleteBatchCompanyTeam',
  teamNameDuplicateCheck = '/reg/companyReg/duplicateCheck',
  getItemGroupByTeam = '/reg/companyReg/getItemGroupOfTeam',
  saveItemGroupOfTeam = '/reg/companyReg/saveItemGroupOfTeam',
  saveItemGroupOfTeamWithRelations = '/reg/companyReg/saveItemGroupOfTeamWithRelations',
  deleteBatchItemGroupOfTeam = '/reg/companyReg/deleteBatchItemGroupOfTeam',
  getTeamById = '/reg/companyReg/getCompanyTeam',
  getCompanyRegDetail = '/reg/companyReg/getCompanyRegDetail',
  getCompanyReg = '/reg/companyReg/getCompanyReg',
  getCompanyTeamDetail = '/reg/companyReg/getCompanyTeamDetail',
  searchCompanyReg = '/reg/companyReg/searchCompanyReg',
  getCompanyDeptListByPid = '/reg/companyReg/searchCompanyDeptByPid',
  getLimitAmountRecordsByRegId = '/fee/teamCustomerLimitAmount/getLimitAmountRecordsByRegId',
  copyCompanyTeam = '/reg/companyReg/copyCompanyTeam',
  getCompanyRegById = '/reg/companyReg/getCompanyRegById',
  notifyEnterprise = '/reg/companyReg/notifyEnterprise',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 列表接口
 * @param params
 */
export const companyTeamList = (params) => {
  if (params['companyRegId']) {
    return defHttp.get({ url: Api.companyTeamList, params });
  }
  return Promise.resolve({});
};
export const getCompanyDeptListByPid = (params) => {
  if (params['pid']) {
    return defHttp.get({ url: Api.getCompanyDeptListByPid , params });
  }
  return Promise.resolve({});
};

/**
 * 删除单个
 */
export const companyTeamDelete = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.companyTeamDelete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 */
export const companyTeamDeleteBatch = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.companyTeamDeleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 */
export const companyTeamSaveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.companyTeamEdit : Api.companyTeamSave;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 导入
 */
export const companyTeamImportUrl = '/reg/companyReg/importCompanyTeam';

/**
 * 导出
 */
export const companyTeamExportXlsUrl = '/reg/companyReg/exportCompanyTeam';

export const teanNameDuplicateCheck = (params) => {
  return defHttp.get({ url: Api.teamNameDuplicateCheck, params });
};

export const getItemGroupByTeam = (params) => {
  return defHttp.get({ url: Api.getItemGroupByTeam, params });
};

export const saveItemGroupOfTeam = (params) => {
  return defHttp.post({ url: Api.saveItemGroupOfTeam, params }, { isTransformResponse: false });
};

export const saveItemGroupOfTeamWithRelations = (params) => {
  return defHttp.post({ url: Api.saveItemGroupOfTeamWithRelations, params }, { isTransformResponse: false });
};

export const deleteBatchItemGroupOfTeam = (params) => {
  return defHttp.delete({ url: Api.deleteBatchItemGroupOfTeam, data: params }, { isTransformResponse: false });
};

export const getTeamById = (params) => {
  return defHttp.get({ url: Api.getTeamById, params });
};

export const getCompanyRegDetail = (params) => {
  return defHttp.get({ url: Api.getCompanyRegDetail, params });
};

export const getCompanyTeamDetail = (params) => {
  return defHttp.get({ url: Api.getCompanyTeamDetail, params });
};

export const getCompanyReg = (params) => {
  return defHttp.get({ url: Api.getCompanyReg, params });
};

export const searchCompanyReg = (params) => {
  return defHttp.get({ url: Api.searchCompanyReg, params });
};
export const getLimitAmountRecordsByRegId = (params) => {
  return defHttp.get({ url: Api.getLimitAmountRecordsByRegId, params }, {isTransformResponse: false});
};
export const copyCompanyTeam = (params) => {
  return defHttp.get({ url: Api.copyCompanyTeam, params }, {isTransformResponse: false});
};
export const getCompanyRegById = (params) => {
  return defHttp.get({ url: Api.getCompanyRegById, params });
};

/**
 * 通知企业端
 */
export const notifyEnterprise = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认通知',
    content: `确定要通知企业端关于预约"${params.regName || ''}"的信息吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.post({ url: Api.notifyEnterprise, params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 获取团体分组的项目列表
 * @param params
 */
export const getItemGroupOfTeam = (params) => defHttp.get({ url: Api.getItemGroupByTeam, params });



