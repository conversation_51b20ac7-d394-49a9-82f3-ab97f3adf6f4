<template>
  <div style="padding: 10px">
    <a-spin :spinning="confirmLoading">
      <JFormContainer :disabled="disabled">
        <template #detail>
          <a-form class="antd-modal-form" v-bind="formItemLayout" ref="formRef" name="QuestionnaireContentForm">
            <a-row>
              <a-col :span="12">
                <a-form-item label="题号" v-bind="validateInfos.sort" id="QuestionnaireContent-sort" name="sort">
                  <a-input-number v-model:value="formData.sort" placeholder="请输入排序号" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="题目" v-bind="validateInfos.content" id="QuestionnaireContent-content" name="content">
                  <a-input v-model:value="formData.content" placeholder="请输入题目" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="类型" v-bind="validateInfos.type" id="QuestionnaireContent-type" name="type">
                  <j-dict-select-tag v-model:value="formData.type" dictCode="quest_item_type" placeholder="请选择类型" allow-clear />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="必答" v-bind="validateInfos.type" id="QuestionnaireContent-type" name="type">
                  <j-switch v-model:value="formData.requiredFlag" :options="[1, 0]" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </template>
      </JFormContainer>
      <template v-if="formData.type == '单选' || formData.type == '多选'">
        <a-divider orientation="left">选项管理</a-divider>
        <div class="mb-2">
          <a-button type="dashed" @click="addOption">新增选项</a-button>
        </div>
        <a-table
          :data-source="topicOptionsTable.dataSource"
          :columns="optionColumns"
          :pagination="false"
          :rowKey="(row) => row.id || row._key"
          :loading="topicOptionsTable.loading"
          :scroll="{ y: 340, x: 900 }"
          size="small"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'optionNumber'">
              <a-input-number v-model:value="record.optionNumber" style="width: 100%" :min="0" />
            </template>
            <template v-else-if="column.dataIndex === 'content'">
              <a-input v-model:value="record.content" placeholder="请输入标题" />
            </template>
            <template v-else-if="column.dataIndex === 'score'">
              <a-input-number v-model:value="record.score" style="width: 100%" />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button type="link" danger @click="removeOption(record, index)">删除</a-button>
            </template>
          </template>
        </a-table>
      </template>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { defineExpose, defineProps, inject, nextTick, reactive, ref, unref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { getValueType } from '/@/utils';
  import { deleteOption, getNextQuestSeq, getOptonsByContentId, questionnaireContentSaveOrUpdate } from '../QuestionnaireDefinition.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import type { TopicOptionRow } from '@/views/quest/QuestionnaireDefinition.data';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import { deleteCalResult } from '@/views/quest/QuestionCalculationResult.api';
  // j-switch 通过全局注册可直接在模板中使用，无需在此导入

  const topicOptionsTable = reactive({
    loading: false,
    dataSource: [] as TopicOptionRow[],
    show: false,
    requiredFlag: '1',
  });
  const optionColumns = [
    { title: '题号', dataIndex: 'optionNumber', width: 64, align: 'center' },
    { title: '标题', dataIndex: 'content', width: 420, ellipsis: true },
    { title: '得分', dataIndex: 'score', width: 140 },
    { title: '操作', key: 'action', width: 100, fixed: 'right' },
  ];
  //接收主表id
  const mainId = inject<any>('mainId');
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    titleNumber: '',
    content: '',
    type: '',
    sort: undefined,
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = {
    content: [{ required: true, message: '请输入题目!' }],
    type: [{ required: true, message: '请输入类型!' }],
    sort: [{ required: true, message: '请输入排序号!' }],
  };
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });
  const props = defineProps({
    disabled: { type: Boolean, default: false },
  });
  const formItemLayout = {
    labelCol: { xs: { span: 24 }, sm: { span: 5 } },
    wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
  };

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
      if (record.id) {
        initOptions(record.id);
      } else {
        getNextQuestSeq({ questId: mainId.value }).then((res) => {
          formData.sort = res.result;
        });
      }
    });
  }

  function initOptions(questContentId) {
    getOptonsByContentId({ contentId: questContentId }).then((res) => {
      topicOptionsTable.dataSource = (res.result || []).map((row, idx) => ({
        optionNumber: row.optionNumber,
        content: row.content,
        score: row.score,
        id: row.id,
      }));
    });
  }

  function removeOption(row, index) {
    if (row.id) {
      deleteOption({ id: row.id }).then(() => {
        topicOptionsTable.dataSource.splice(index, 1);
      });
    } else {
      topicOptionsTable.dataSource.splice(index, 1);
    }
  }

  function addOption() {
    topicOptionsTable.dataSource.push({
      optionNumber: undefined,
      content: '',
      score: undefined,
    } as TopicOptionRow);
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }

    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    if (unref(mainId)) {
      model['definitionId'] = unref(mainId);
    }

    model.options = topicOptionsTable.dataSource.map((row: TopicOptionRow, idx) => ({
      id: row.id,
      optionNumber: row.optionNumber ?? idx + 1,
      content: row.content ?? '',
      score: row.score ?? 0,
      sortNumber: idx + 1,
    }));
    await questionnaireContentSaveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
